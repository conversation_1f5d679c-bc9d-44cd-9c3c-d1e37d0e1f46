"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiGameArea = ApiGameArea;
/** 获取游戏分区信息与分区服务器地址 - 纯HTTP架构 */
async function ApiGameArea(call) {
    console.log('🔍 [GameArea] 客户端请求游戏区域信息（纯HTTP架构）');
    // 根据环境变量确定配置
    const isProduction = process.env.NODE_ENV === 'production';
    const domain = process.env.DOMAIN || 'idlefun.press';
    // 简化：在dev-facebook分支默认为Facebook请求
    const isFacebookRequest = true;
    let config;
    if (isProduction) {
        // 生产环境配置
        if (isFacebookRequest) {
            config = {
                name: "Facebook游戏服务器",
                httpUrl: `https://${domain}/api/facebook` // Facebook HTTP API
            };
            console.log('🎮 [GameArea] 返回Facebook生产环境配置');
        }
        else {
            config = {
                name: "个人游戏服务器",
                httpUrl: `https://${domain}/api` // 个人游戏HTTP API
            };
            console.log('🌐 [GameArea] 返回个人游戏生产环境配置');
        }
    }
    else {
        // 开发环境配置
        config = {
            name: "开发服务器",
            httpUrl: "http://127.0.0.1:5000" // HTTP服务
        };
        console.log('🔧 [GameArea] 返回开发环境配置');
    }
    call.succ({ area: config });
    console.log('✅ [GameArea] 纯HTTP配置信息返回完成:', {
        isFacebookRequest,
        config: config
    });
}
