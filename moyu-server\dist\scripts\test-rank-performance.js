"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const MongoDB_1 = require("../module/common/MongoDB");
const RedisManager_1 = require("../module/common/RedisManager");
const Rank_1 = require("../module/rank/bll/Rank");
const base_1 = require("../tsrpc/protocols/base");
async function testRankPerformance() {
    console.log('🧪 开始测试排行榜性能...');
    try {
        // 初始化数据库连接
        console.log('🔌 初始化数据库连接...');
        await MongoDB_1.MongoDB.init();
        // 初始化Redis连接
        console.log('🔌 初始化Redis连接...');
        try {
            await RedisManager_1.RedisManager.init();
            console.log('✅ Redis连接成功');
        }
        catch (error) {
            console.warn('⚠️ Redis连接失败，继续测试:', error);
        }
        // 初始化排行榜
        console.log('🏆 初始化排行榜系统...');
        Rank_1.Rank.init();
        // 测试各种排行榜类型
        const rankTypes = [
            { type: base_1.RankType.World, name: '世界排行榜' },
            { type: base_1.RankType.Country, name: '国家排行榜(CN)' },
            { type: base_1.RankType.City, name: '城市排行榜(CN)' }
        ];
        for (const rankType of rankTypes) {
            console.log(`\n📊 测试 ${rankType.name}...`);
            const startTime = Date.now();
            try {
                let result = [];
                switch (rankType.type) {
                    case base_1.RankType.World:
                        result = await Rank_1.Rank.getWorldList();
                        break;
                    case base_1.RankType.Country:
                        result = await Rank_1.Rank.getCountryList('CN');
                        break;
                    case base_1.RankType.City:
                        result = await Rank_1.Rank.getCountryCityList('CN');
                        break;
                }
                const duration = Date.now() - startTime;
                console.log(`✅ ${rankType.name} 查询成功`);
                console.log(`   📈 数据量: ${result.length} 条`);
                console.log(`   ⏱️  耗时: ${duration}ms`);
                if (duration > 1000) {
                    console.log(`   ⚠️  性能警告: 耗时超过1秒 (${duration}ms)`);
                }
                else if (duration > 500) {
                    console.log(`   ⚠️  性能提醒: 耗时较长 (${duration}ms)`);
                }
                else {
                    console.log(`   ✅ 性能良好 (${duration}ms)`);
                }
                // 显示前3名数据
                if (result.length > 0) {
                    console.log(`   🏆 前3名:`);
                    result.slice(0, 3).forEach((record, index) => {
                        console.log(`      ${index + 1}. ${record.name || record.id} - 分数: ${record.score}`);
                    });
                }
            }
            catch (error) {
                console.log(`❌ ${rankType.name} 查询失败:`, error);
            }
        }
        // 连续测试性能 (世界排行榜)
        console.log('\n🔄 连续性能测试 (世界排行榜 x 10)...');
        const times = [];
        for (let i = 0; i < 10; i++) {
            const startTime = Date.now();
            try {
                await Rank_1.Rank.getWorldList();
                const duration = Date.now() - startTime;
                times.push(duration);
                console.log(`   第${i + 1}次: ${duration}ms ✅`);
            }
            catch (error) {
                console.log(`   第${i + 1}次: 失败 ❌`, error);
            }
        }
        if (times.length > 0) {
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            const maxTime = Math.max(...times);
            const minTime = Math.min(...times);
            console.log(`\n📊 性能统计:`);
            console.log(`   平均耗时: ${avgTime.toFixed(1)}ms`);
            console.log(`   最大耗时: ${maxTime}ms`);
            console.log(`   最小耗时: ${minTime}ms`);
            if (avgTime < 50) {
                console.log(`   🎉 性能优秀！`);
            }
            else if (avgTime < 100) {
                console.log(`   ✅ 性能良好`);
            }
            else if (avgTime < 300) {
                console.log(`   ⚠️  性能一般`);
            }
            else {
                console.log(`   ❌ 性能较差，需要优化`);
            }
        }
        // 测试排名查询性能
        console.log('\n🔍 测试排名查询性能...');
        const positionStartTime = Date.now();
        try {
            const position = await Rank_1.Rank.getRankInWorld('CN');
            const positionDuration = Date.now() - positionStartTime;
            console.log(`✅ 排名查询成功: CN排名第${position}位, 耗时: ${positionDuration}ms`);
            if (positionDuration > 500) {
                console.log(`   ⚠️  排名查询性能警告: 耗时${positionDuration}ms`);
            }
            else {
                console.log(`   ✅ 排名查询性能良好`);
            }
        }
        catch (error) {
            console.log(`❌ 排名查询失败:`, error);
        }
    }
    catch (error) {
        console.error('❌ 测试失败:', error);
    }
}
// 运行测试
testRankPerformance().then(() => {
    console.log('\n🎉 排行榜性能测试完成！');
    process.exit(0);
}).catch((error) => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
});
