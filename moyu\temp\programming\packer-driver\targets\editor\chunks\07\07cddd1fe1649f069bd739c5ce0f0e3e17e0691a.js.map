{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/initialize/view/LoginViewComp.ts"], "names": ["_decorator", "EditBox", "Node", "sys", "oops", "PlatformUtil", "ecs", "CCVMParentComp", "JustAuthPlatformType", "CommonTools", "ClientConfig", "GameStorageConfig", "PromptManager", "smc", "ccclass", "property", "LoginViewComp", "register", "type", "data", "isRegister", "loginFail", "isSafeAccount", "isSafePassword", "isPasswordMatch", "lastLogin", "callbacks", "getLastFacebookLoginResult", "lastFacebookLoginResult", "onAdded", "reset", "gui", "removeByNode", "node", "onLoad", "setButton", "start", "regAccountEBox", "on", "checkAccountEdit", "regPasswordEBox", "checkPasswordEdit", "regPasswordAgainEBox", "checkPasswordMatch", "loginAccountEBox", "string", "userName", "loginPasswordEBox", "passWord", "ret", "checkUsername", "checkPassword", "googleButton1", "googleLogin", "googleButton2", "doAfterLogin", "httpUrl", "log", "logBusiness", "ssoToken", "storage", "get", "exists", "length", "preview", "substring", "res", "role", "loadData", "success", "do<PERSON><PERSON><PERSON>", "loginData", "reqLoginData", "server", "net", "hcGate", "callApi", "isSucc", "set", "UserDumpKey", "toast", "err", "message", "loginButton", "url", "initialize", "GateModel", "area", "isGuest", "onOldUserLogin", "forgetPasswordButton", "loginGuestButton", "onNewUserLogin", "args", "platform", "getPlateform", "platformType", "retRegister", "confirmRegButton", "instance", "confirm", "justAuthByPlatformType", "google", "connectGateWs", "doFacebookLogin", "facebookData", "fbInstantManager", "getFacebookLoginData", "log<PERSON>arn", "reqFacebookLogin", "facebookId", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "locale", "countryCode", "fbRes", "guuid", "isNewUser", "userData", "__ssoToken", "errorInfo", "code", "logError", "error"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,G,OAAAA,G;;AAC3BC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,oB,iBAAAA,oB;;AACAC,MAAAA,W,iBAAAA,W;;AAGAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,a,kBAAAA,a;;AACAC,MAAAA,G,kBAAAA,G;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;AAmB9B;+BAGagB,a,WAFZF,OAAO,CAAC,eAAD,C,UACP;AAAA;AAAA,sBAAIG,QAAJ,CAAa,WAAb,EAA0B,KAA1B,C,UAUIF,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,qDAzBb,MAEae,aAFb;AAAA;AAAA,4CAEkD;AAAA;AAAA;;AAC9C;AAD8C,eAE9CG,IAF8C,GAElC;AACRC,YAAAA,UAAU,EAAE,CADJ;AACO;AACfC,YAAAA,SAAS,EAAE,CAFH;AAEM;AACdC,YAAAA,aAAa,EAAE,CAHP;AAGU;AAClBC,YAAAA,cAAc,EAAE,CAJR;AAIW;AACnBC,YAAAA,eAAe,EAAE,CALT,CAKY;;AALZ,WAFkC;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AA0B9C;AA1B8C,eA2BtCC,SA3BsC;AA4B9C;AA5B8C,eA6BtCC,SA7BsC;AAAA;;AAkC9C;AACJ;AACA;AACqC,eAA1BC,0BAA0B,GAAQ;AACrC,iBAAOX,aAAa,CAACY,uBAArB;AACH;;AAEDC,QAAAA,OAAO,CAACV,IAAD,EAA8D;AACjE,eAAKM,SAAL,GAAiBN,IAAjB,oBAAiBA,IAAI,CAAEM,SAAvB;AACA,eAAKC,SAAL,GAAiBP,IAAjB,oBAAiBA,IAAI,CAAEO,SAAvB;AACH;;AACDI,QAAAA,KAAK,GAAS;AACV;AAAA;AAAA,4BAAKC,GAAL,CAASC,YAAT,CAAsB,KAAKC,IAA3B;AACH;;AAEDC,QAAAA,MAAM,GAAS;AACX,gBAAMA,MAAN;AACA,eAAKC,SAAL,CAAe,KAAf;AACH;;AACSC,QAAAA,KAAK,GAAS;AACpB,eAAKC,cAAL,CAAoBJ,IAApB,CAAyBK,EAAzB,CAA4B,mBAA5B,EAAiD,KAAKC,gBAAtD,EAAwE,IAAxE;AACA,eAAKF,cAAL,CAAoBJ,IAApB,CAAyBK,EAAzB,CAA4B,oBAA5B,EAAkD,KAAKC,gBAAvD,EAAyE,IAAzE;AACA,eAAKC,eAAL,CAAqBP,IAArB,CAA0BK,EAA1B,CAA6B,mBAA7B,EAAkD,KAAKG,iBAAvD,EAA0E,IAA1E;AACA,eAAKD,eAAL,CAAqBP,IAArB,CAA0BK,EAA1B,CAA6B,oBAA7B,EAAmD,KAAKG,iBAAxD,EAA2E,IAA3E;AACA,eAAKC,oBAAL,CAA0BT,IAA1B,CAA+BK,EAA/B,CAAkC,mBAAlC,EAAuD,KAAKK,kBAA5D,EAAgF,IAAhF;AACA,eAAKD,oBAAL,CAA0BT,IAA1B,CAA+BK,EAA/B,CAAkC,oBAAlC,EAAwD,KAAKK,kBAA7D,EAAiF,IAAjF;;AACA,cAAI,KAAKlB,SAAT,EAAoB;AAAA;;AAChB,iBAAKmB,gBAAL,CAAsBC,MAAtB,GAA+B,yBAAKpB,SAAL,qCAAgBqB,QAAhB,KAA4B,EAA3D;AACA,iBAAKC,iBAAL,CAAuBF,MAAvB,GAAgC,0BAAKpB,SAAL,sCAAgBuB,QAAhB,KAA4B,EAA5D;AACH;;AACD,eAAKP,iBAAL;AACA,eAAKF,gBAAL;AACH;;AACOA,QAAAA,gBAAgB,GAAG;AACvB,cAAIU,GAAG,GAAG;AAAA;AAAA,0CAAYC,aAAZ,CAA0B,KAAKb,cAAL,CAAoBQ,MAA9C,CAAV;AACA,eAAK1B,IAAL,CAAUG,aAAV,GAA0B2B,GAAG,GAAG,CAAH,GAAO,CAApC;AACA,iBAAOA,GAAP;AACH;;AACOR,QAAAA,iBAAiB,GAAG;AACxB,cAAIQ,GAAG,GAAG;AAAA;AAAA,0CAAYE,aAAZ,CAA0B,KAAKX,eAAL,CAAqBK,MAA/C,CAAV;AACA,eAAK1B,IAAL,CAAUI,cAAV,GAA2B0B,GAAG,GAAG,CAAH,GAAO,CAArC;AACA,iBAAOA,GAAP;AACH;;AAEON,QAAAA,kBAAkB,GAAG;AACzB,cAAI,KAAKH,eAAL,CAAqBK,MAArB,KAAgC,KAAKH,oBAAL,CAA0BG,MAA9D,EAAsE;AAClE,iBAAK1B,IAAL,CAAUK,eAAV,GAA4B,CAA5B;AACA,mBAAO,KAAP;AACH;;AACD,eAAKL,IAAL,CAAUK,eAAV,GAA4B,CAA5B;AACA,iBAAO,IAAP;AACH;;AACD4B,QAAAA,aAAa,GAAG;AACZ,eAAKC,WAAL;AACH;;AACDC,QAAAA,aAAa,GAAG;AACZ,eAAKD,WAAL;AACH;;AACgC,qBAAZE,YAAY,CAACC,OAAD,EAAkB;AAC/C;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,iCAArB,EAAwD;AAAEF,YAAAA;AAAF,WAAxD,EAD+C,CAG/C;;AACA,gBAAMG,QAAQ,GAAG;AAAA;AAAA,4BAAKC,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAjB;AACA;AAAA;AAAA,4BAAKJ,GAAL,CAASC,WAAT,CAAqB,mBAArB,EAA0C;AACtCI,YAAAA,MAAM,EAAE,CAAC,CAACH,QAD4B;AAEtCI,YAAAA,MAAM,EAAEJ,QAAQ,GAAGA,QAAQ,CAACI,MAAZ,GAAqB,CAFC;AAGtCC,YAAAA,OAAO,EAAEL,QAAQ,GAAGA,QAAQ,CAACM,SAAT,CAAmB,CAAnB,EAAsB,CAAtB,IAA2B,KAA9B,GAAsC;AAHjB,WAA1C;AAMA;AAAA;AAAA,4BAAKR,GAAL,CAASC,WAAT,CAAqB,qCAArB;AAEA,cAAIQ,GAAG,GAAG,MAAM;AAAA;AAAA,0BAAIC,IAAJ,CAASC,QAAT,EAAhB;AACA;AAAA;AAAA,4BAAKX,GAAL,CAASC,WAAT,CAAqB,uBAArB,EAA8C;AAAEW,YAAAA,OAAO,EAAEH;AAAX,WAA9C;AAEA,iBAAOA,GAAP,CAhB+C,CAgBnC;AACf;;AACmB,qBAAPI,OAAO,CAACC,SAAD,EAAuB;AACvC,cAAIC,YAAY,GAAG;AACfC,YAAAA,MAAM,EAAEF,SAAS,CAACf,OADH;AAEfV,YAAAA,QAAQ,EAAEyB,SAAS,CAACzB,QAFL;AAGfE,YAAAA,QAAQ,EAAEuB,SAAS,CAACvB;AAHL,WAAnB;AAKA,gBAAMC,GAAG,GAAG,MAAM;AAAA;AAAA,0BAAIyB,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAwB,OAAxB,EAAgCJ,YAAhC,CAAlB;;AACA,cAAIvB,GAAG,CAAC4B,MAAR,EAAgB;AACZ,gBAAIX,GAAG,GAAG,MAAMlD,aAAa,CAACuC,YAAd,CAA2BgB,SAAS,CAACf,OAArC,CAAhB;;AACA,gBAAIU,GAAJ,EAAS;AACL;AACA;AAAA;AAAA,gCAAKN,OAAL,CAAakB,GAAb,CAAiB;AAAA;AAAA,0DAAkBC,WAAnC,EAAgDP,YAAhD;AACH;;AACD,mBAAON,GAAP;AACH,WAPD,MAOO;AACH;AAAA;AAAA,8BAAKnC,GAAL,CAASiD,KAAT,CAAe/B,GAAG,CAACgC,GAAJ,CAAQC,OAAvB,EAAgC,IAAhC;AACA,mBAAO,KAAP;AACH;AACJ;;AAEgB,cAAXC,WAAW,GAAG;AAAA;;AAChB,cAAI,KAAKvC,gBAAL,CAAsBC,MAAtB,CAA6BkB,MAA7B,IAAuC,CAAvC,IAA4C,KAAKhB,iBAAL,CAAuBF,MAAvB,CAA8BkB,MAA9B,IAAwC,CAAxF,EAA2F;AACvF;AAAA;AAAA,8BAAKhC,GAAL,CAASiD,KAAT,CAAe,aAAf,EAA8B,IAA9B;AACA,mBAAO,KAAP;AACH,WAJe,CAMhB;;;AACA,gBAAMI,GAAG,GAAG;AAAA;AAAA,0BAAIC,UAAJ,CAAeC,SAAf,CAAyBC,IAAzB,2CAA+B/B,OAA/B,KAA0C,uBAAtD;AAEA,cAAIP,GAAG,GAAG,MAAMjC,aAAa,CAACsD,OAAd,CAAsB;AAClCxB,YAAAA,QAAQ,EAAE,KAAKF,gBAAL,CAAsBC,MADE;AAElCG,YAAAA,QAAQ,EAAE,KAAKD,iBAAL,CAAuBF,MAFC;AAGlC2C,YAAAA,OAAO,EAAE,KAHyB;AAIlChC,YAAAA,OAAO,EAAE4B;AAJyB,WAAtB,CAAhB;;AAOA,cAAInC,GAAJ,EAAS;AAAA;;AACL;AAAA;AAAA,8BAAKQ,GAAL,CAASC,WAAT,CAAqB,+BAArB;AACA,sCAAM,KAAKhC,SAAX,aAAM,gBAAgB+D,cAAtB,oBAAM,gBAAgBA,cAAhB,EAAN;AACA,iBAAK3D,KAAL;AACH;;AACD,iBAAOmB,GAAP;AACH;;AAEDyC,QAAAA,oBAAoB,GAAG,CAAE;;AAEH,cAAhBC,gBAAgB,GAAG;AACrB,cAAIzB,GAAG,GAAG,MAAM,KAAKjD,QAAL,CAAc;AAAEuE,YAAAA,OAAO,EAAE;AAAX,WAAd,CAAhB;;AACA,cAAItB,GAAJ,EAAS;AAAA;;AACL;AACA,kBAAMV,OAAO,GAAG;AAAA;AAAA,4BAAI6B,UAAJ,CAAeC,SAAf,CAAyBC,IAAzB,4CAA+B/B,OAA/B,KAA0C,uBAA1D;AAEA,gBAAIP,GAAG,GAAG,MAAMjC,aAAa,CAACsD,OAAd,CAAsB;AAClCxB,cAAAA,QAAQ,EAAEoB,GAAG,CAACpB,QADoB;AAElCE,cAAAA,QAAQ,EAAEkB,GAAG,CAAClB,QAFoB;AAGlCwC,cAAAA,OAAO,EAAE,IAHyB;AAIlChC,cAAAA,OAAO,EAAEA;AAJyB,aAAtB,CAAhB;;AAOA,gBAAIP,GAAJ,EAAS;AAAA;;AACL;AAAA;AAAA,gCAAKQ,GAAL,CAASC,WAAT,CAAqB,gCAArB;AACA,yCAAM,KAAKhC,SAAX,aAAM,iBAAgBkE,cAAtB,oBAAM,iBAAgBA,cAAhB,EAAN;AACA,mBAAK9D,KAAL;AACH;;AACD,mBAAOmB,GAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AACqB,cAARhC,QAAQ,CAACE,IAAD,EAAqB;AACvC,cAAI0E,IAAiB,GAAG;AACpBC,YAAAA,QAAQ,EAAE;AAAA;AAAA,8CAAaC,YAAb,EADU;AAEpBC,YAAAA,YAAY,EAAE7F,GAAG,CAAC2F,QAFE;AAGpBN,YAAAA,OAAO,EAAErE,IAAI,CAACqE,OAAL,IAAgB,KAHL;AAIpB1C,YAAAA,QAAQ,EAAE3B,IAAI,CAAC2B,QAJK;AAKpBE,YAAAA,QAAQ,EAAE7B,IAAI,CAAC6B;AALK,WAAxB;AAOA,gBAAMiD,WAAW,GAAG,MAAM;AAAA;AAAA,0BAAIvB,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAwB,UAAxB,EAAmCiB,IAAnC,CAA1B;;AACA,cAAII,WAAW,CAACpB,MAAhB,EAAwB;AACpB,mBAAOoB,WAAW,CAAC/B,GAAnB;AACH,WAFD,MAEO;AACH;AAAA;AAAA,8BAAKnC,GAAL,CAASiD,KAAT,CAAeiB,WAAW,CAAChB,GAAZ,CAAgBC,OAA/B,EAAwC,IAAxC;AACA,mBAAO,KAAP;AACH;AACJ;;AACqB,cAAhBgB,gBAAgB,GAAG;AACrB,cAAI,KAAK3D,gBAAL,MAA2B,KAAKE,iBAAL,EAA3B,IAAuD,KAAKE,kBAAL,EAA3D,EAAsF;AAClF,gBAAIM,GAAG,GAAG,MAAM,KAAKhC,QAAL,CAAc;AAC1B6B,cAAAA,QAAQ,EAAE,KAAKT,cAAL,CAAoBQ,MADJ;AAE1BG,cAAAA,QAAQ,EAAE,KAAKR,eAAL,CAAqBK,MAFL;AAG1B2C,cAAAA,OAAO,EAAE;AAHiB,aAAd,CAAhB;;AAMA,gBAAIvC,GAAJ,EAAS;AACL;AAAA;AAAA,kDAAckD,QAAd,CAAuBC,OAAvB,CAA+B,CAA/B,EAAkC,YAAY;AAAA;;AAC1C;AACA,sBAAM5C,OAAO,GACT;AAAA;AAAA,gCAAI6B,UAAJ,CAAeC,SAAf,CAAyBC,IAAzB,4CAA+B/B,OAA/B,KAA0C,uBAD9C;AAGA,oBAAIU,GAAG,GAAG,MAAMlD,aAAa,CAACsD,OAAd,CAAsB;AAClCxB,kBAAAA,QAAQ,EAAEG,GAAG,CAACH,QADoB;AAElCE,kBAAAA,QAAQ,EAAEC,GAAG,CAACD,QAFoB;AAGlCwC,kBAAAA,OAAO,EAAE,KAHyB;AAIlChC,kBAAAA,OAAO,EAAEA;AAJyB,iBAAtB,CAAhB;;AAOA,oBAAIU,GAAJ,EAAS;AAAA;;AACL;AAAA;AAAA,oCAAKT,GAAL,CAASC,WAAT,CAAqB,wCAArB;AACA,6CAAM,KAAKhC,SAAX,aAAM,iBAAgBkE,cAAtB,oBAAM,iBAAgBA,cAAhB,EAAN;AACA,uBAAK9D,KAAL;AACH;AACJ,eAjBD;AAkBH;AACJ;;AACD;AACH;;AACwB,cAAXuB,WAAW,GAAG;AACxB,cAAIJ,GAAG,GAAG,MAAM,KAAKoD,sBAAL,CAA4B;AAAA;AAAA,4DAAqBC,MAAjD,CAAhB;AACA,iBAAOrD,GAAP;AACH;;AACmC,cAAtBoD,sBAAsB,CAACnF,IAAD,EAA6B;AAC7D;AACA;AAAA;AAAA,4BAAKuC,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACA;AAAA;AAAA,4BAAK3B,GAAL,CAASiD,KAAT,CAAe,aAAf,EAA8B,IAA9B;AACA,iBAAO,KAAP;AACH;;AAE0B,cAAbuB,aAAa,GAAG;AAC1B;AACA;AAAA;AAAA,4BAAK9C,GAAL,CAASC,WAAT,CAAqB,2BAArB;AACA,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACgC,qBAAf8C,eAAe,GAAqB;AAC7C;AAAA;AAAA,4BAAK/C,GAAL,CAASC,WAAT,CAAqB,6BAArB;;AAEA,cAAI;AACA;AACA,kBAAM+C,YAAY,GAAG;AAAA;AAAA,4BAAIC,gBAAJ,CAAqBC,oBAArB,EAArB;;AACA,gBAAI,CAACF,YAAL,EAAmB;AACf;AAAA;AAAA,gCAAKhD,GAAL,CAASmD,OAAT,CAAiB,qBAAjB;AACA,qBAAO,KAAP;AACH,aAND,CAQA;;;AACA,kBAAMpD,OAAO,GAAG;AAAA;AAAA,8CAAaA,OAA7B,CATA,CAWA;;AACA,kBAAMqD,gBAAkC,GAAG;AACvCC,cAAAA,UAAU,EAAEL,YAAY,CAACK,UADc;AAEvCC,cAAAA,UAAU,EAAEN,YAAY,CAACM,UAFc;AAGvCC,cAAAA,WAAW,EAAEP,YAAY,CAACO,WAHa;AAIvCC,cAAAA,MAAM,EAAER,YAAY,CAACQ,MAJkB;AAKvCC,cAAAA,WAAW,EAAET,YAAY,CAACS,WALa;AAMvCpB,cAAAA,QAAQ,EAAEW,YAAY,CAACX,QANgB;AAOvCE,cAAAA,YAAY,EAAES,YAAY,CAACT,YAPY;AAQvCxC,cAAAA,OAAO,EAAEA,OAR8B,CAQrB;;AARqB,aAA3C;AAWA;AAAA;AAAA,8BAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB,EAA2D;AACvDoD,cAAAA,UAAU,EAAEL,YAAY,CAACK,UAD8B;AAEvDC,cAAAA,UAAU,EAAEN,YAAY,CAACM;AAF8B,aAA3D;AAKA,kBAAM9D,GAAG,GAAG,MAAM;AAAA;AAAA,4BAAIyB,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAwB,eAAxB,EAAwCiC,gBAAxC,CAAlB;;AAEA,gBAAI5D,GAAG,CAAC4B,MAAR,EAAgB;AACZ,oBAAMsC,KAAK,GAAGlE,GAAG,CAACiB,GAAlB;AACA;AAAA;AAAA,gCAAKT,GAAL,CAASC,WAAT,CAAqB,oBAArB,EAA2C;AACvC0D,gBAAAA,KAAK,EAAED,KAAK,CAACC,KAD0B;AAEvCtE,gBAAAA,QAAQ,EAAEqE,KAAK,CAACrE,QAFuB;AAGvCuE,gBAAAA,SAAS,EAAEF,KAAK,CAACE;AAHsB,eAA3C,EAFY,CAQZ;;AACArG,cAAAA,aAAa,CAACY,uBAAd,GAAwC;AACpCyC,gBAAAA,OAAO,EAAE,IAD2B;AAEpCgD,gBAAAA,SAAS,EAAEF,KAAK,CAACE,SAFmB;AAGpCC,gBAAAA,QAAQ,EAAE;AACNF,kBAAAA,KAAK,EAAED,KAAK,CAACC,KADP;AAENtE,kBAAAA,QAAQ,EAAEqE,KAAK,CAACrE,QAFV;AAGNgE,kBAAAA,UAAU,EAAEL,YAAY,CAACK,UAHnB;AAINC,kBAAAA,UAAU,EAAEN,YAAY,CAACM;AAJnB;AAH0B,eAAxC,CATY,CAoBZ;;AACA,kBAAII,KAAK,CAACI,UAAV,EAAsB;AAClB;AAAA;AAAA,kCAAK3D,OAAL,CAAakB,GAAb,CAAiB,WAAjB,EAA8BqC,KAAK,CAACI,UAApC;AACA;AAAA;AAAA,kCAAK9D,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH;;AAED;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAsB,cAAayD,KAAK,CAACE,SAAN,GAAkB,GAAlB,GAAwB,GAAI,SAA/D;AACA,qBAAO,IAAP;AACH,aA5BD,MA4BO;AACH,oBAAMG,SAAS,GAAG;AACdtC,gBAAAA,OAAO,EAAEjC,GAAG,CAACgC,GAAJ,CAAQC,OAAR,IAAmB,eADd;AAEduC,gBAAAA,IAAI,EAAExE,GAAG,CAACgC,GAAJ,CAAQwC,IAAR,IAAgB;AAFR,eAAlB;AAKA;AAAA;AAAA,gCAAKhE,GAAL,CAASiE,QAAT,CAAkB,iBAAlB,EAAqCF,SAArC,EANG,CAQH;;AACAxG,cAAAA,aAAa,CAACY,uBAAd,GAAwC;AACpCyC,gBAAAA,OAAO,EAAE,KAD2B;AAEpCsD,gBAAAA,KAAK,EAAEH;AAF6B,eAAxC;AAKA,qBAAO,KAAP;AACH;AACJ,WA1ED,CA0EE,OAAOG,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKlE,GAAL,CAASiE,QAAT,CAAkB,kBAAlB,EAAsCC,KAAtC,EADY,CAGZ;;AACA3G,YAAAA,aAAa,CAACY,uBAAd,GAAwC;AACpCyC,cAAAA,OAAO,EAAE,KAD2B;AAEpCsD,cAAAA,KAAK,EAAEA;AAF6B,aAAxC;AAKA,mBAAO,KAAP;AACH;AACJ;;AA9U6C,O,UAgC/B/F,uB,GAA+B,I", "sourcesContent": ["import { _decorator, EditBox, Node, sys } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PlatformUtil } from '../../../../../extensions/oops-plugin-framework/assets/core/utils/PlatformUtil';\nimport { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { CCVMParentComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCVMParentComp';\nimport { JustAuthPlatformType } from '../../../tsrpc/protocols/base';\nimport { CommonTools } from '../../../tsrpc/protocols/commonTools';\nimport { ReqFacebookLogin } from '../../../tsrpc/protocols/gate/PtlFacebookLogin';\nimport { ReqRegister } from '../../../tsrpc/protocols/gate/PtlRegister';\nimport { ClientConfig } from '../../common/ClientConfig';\nimport { GameStorageConfig } from '../../common/config/GameStorageConfig';\nimport { PromptManager } from '../../common/prompt/PromptManager';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { LastLogin } from '../bll/Login';\n\nconst { ccclass, property } = _decorator;\nexport interface LoginData {\n    userName: string;\n    passWord: string;\n    isGuest: boolean;\n    httpUrl: string;\n}\nexport interface registerArgs {\n    userName?: string;\n    passWord?: string;\n    isGuest: boolean;\n}\n\nexport interface LoginCallbacks {\n    onRemoved?: () => void;\n    onNewUserLogin?: () => Promise<void>;\n    onOldUserLogin?: () => Promise<void>;\n}\n\n// 登录界面和注册界面切换\n@ccclass('LoginViewComp')\**************('LoginView', false)\nexport class LoginViewComp extends CCVMParentComp {\n    /** VM 组件绑定数据 */\n    data: any = {\n        isRegister: 0, //显示注册\n        loginFail: 0, // 登录失败\n        isSafeAccount: 1, // 账号安全\n        isSafePassword: 1, // 密码安全\n        isPasswordMatch: 1, // 密码匹配\n    };\n    @property(Node)\n    loginNode!: Node;\n\n    @property(Node)\n    registerNode!: Node;\n\n    @property({ type: EditBox })\n    private loginAccountEBox!: EditBox;\n    @property({ type: EditBox })\n    private loginPasswordEBox!: EditBox;\n    @property({ type: EditBox })\n    private regAccountEBox!: EditBox;\n    @property({ type: EditBox })\n    private regPasswordEBox!: EditBox;\n    @property({ type: EditBox })\n    private regPasswordAgainEBox!: EditBox;\n\n    // 上次登录信息\n    private lastLogin?: LastLogin;\n    // 回调函数\n    private callbacks?: LoginCallbacks;\n\n    // 静态变量存储最后一次Facebook登录的结果\n    private static lastFacebookLoginResult: any = null;\n\n    /**\n     * 获取最后一次Facebook登录的结果（供InitRes使用）\n     */\n    static getLastFacebookLoginResult(): any {\n        return LoginViewComp.lastFacebookLoginResult;\n    }\n\n    onAdded(data: { lastLogin?: LastLogin; callbacks?: LoginCallbacks }) {\n        this.lastLogin = data?.lastLogin;\n        this.callbacks = data?.callbacks;\n    }\n    reset(): void {\n        oops.gui.removeByNode(this.node);\n    }\n\n    onLoad(): void {\n        super.onLoad();\n        this.setButton(false);\n    }\n    protected start(): void {\n        this.regAccountEBox.node.on('editing-did-ended', this.checkAccountEdit, this);\n        this.regAccountEBox.node.on('editing-did-change', this.checkAccountEdit, this);\n        this.regPasswordEBox.node.on('editing-did-ended', this.checkPasswordEdit, this);\n        this.regPasswordEBox.node.on('editing-did-change', this.checkPasswordEdit, this);\n        this.regPasswordAgainEBox.node.on('editing-did-ended', this.checkPasswordMatch, this);\n        this.regPasswordAgainEBox.node.on('editing-did-change', this.checkPasswordMatch, this);\n        if (this.lastLogin) {\n            this.loginAccountEBox.string = this.lastLogin?.userName || '';\n            this.loginPasswordEBox.string = this.lastLogin?.passWord || '';\n        }\n        this.checkPasswordEdit();\n        this.checkAccountEdit();\n    }\n    private checkAccountEdit() {\n        let ret = CommonTools.checkUsername(this.regAccountEBox.string);\n        this.data.isSafeAccount = ret ? 1 : 0;\n        return ret;\n    }\n    private checkPasswordEdit() {\n        let ret = CommonTools.checkPassword(this.regPasswordEBox.string);\n        this.data.isSafePassword = ret ? 1 : 0;\n        return ret;\n    }\n\n    private checkPasswordMatch() {\n        if (this.regPasswordEBox.string !== this.regPasswordAgainEBox.string) {\n            this.data.isPasswordMatch = 0;\n            return false;\n        }\n        this.data.isPasswordMatch = 1;\n        return true;\n    }\n    googleButton1() {\n        this.googleLogin();\n    }\n    googleButton2() {\n        this.googleLogin();\n    }\n    private static async doAfterLogin(httpUrl: string) {\n        oops.log.logBusiness('🔄 开始doAfterLogin流程（纯HTTP架构）...', { httpUrl });\n\n        // 检查SSO Token是否存在\n        const ssoToken = oops.storage.get('SSO_TOKEN');\n        oops.log.logBusiness('🔑 当前SSO Token状态:', {\n            exists: !!ssoToken,\n            length: ssoToken ? ssoToken.length : 0,\n            preview: ssoToken ? ssoToken.substring(0, 8) + '...' : '无',\n        });\n\n        oops.log.logBusiness('📋 开始Role.loadData()调用（HTTP API）...');\n\n        let res = await smc.role.loadData();\n        oops.log.logBusiness('📋 Role.loadData()结果:', { success: res });\n\n        return res; // 直接返回结果\n    }\n    static async doLogin(loginData: LoginData) {\n        let reqLoginData = {\n            server: loginData.httpUrl as string,\n            userName: loginData.userName,\n            passWord: loginData.passWord,\n        };\n        const ret = await smc.net.hcGate.callApi(`Login`, reqLoginData);\n        if (ret.isSucc) {\n            let res = await LoginViewComp.doAfterLogin(loginData.httpUrl);\n            if (res) {\n                // 设置本地存储登录数据\n                oops.storage.set(GameStorageConfig.UserDumpKey, reqLoginData);\n            }\n            return res;\n        } else {\n            oops.gui.toast(ret.err.message, true);\n            return false;\n        }\n    }\n\n    async loginButton() {\n        if (this.loginAccountEBox.string.length == 0 || this.loginPasswordEBox.string.length == 0) {\n            oops.gui.toast('login_empty', true);\n            return false;\n        }\n\n        // 获取服务器配置\n        const url = smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';\n\n        let ret = await LoginViewComp.doLogin({\n            userName: this.loginAccountEBox.string,\n            passWord: this.loginPasswordEBox.string,\n            isGuest: false,\n            httpUrl: url,\n        });\n\n        if (ret) {\n            oops.log.logBusiness('Old user manual login success');\n            await this.callbacks?.onOldUserLogin?.();\n            this.reset();\n        }\n        return ret;\n    }\n\n    forgetPasswordButton() {}\n\n    async loginGuestButton() {\n        let res = await this.register({ isGuest: true });\n        if (res) {\n            // 获取服务器配置\n            const httpUrl = smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';\n\n            let ret = await LoginViewComp.doLogin({\n                userName: res.userName,\n                passWord: res.passWord,\n                isGuest: true,\n                httpUrl: httpUrl,\n            });\n\n            if (ret) {\n                oops.log.logBusiness('Guest login success (new user)');\n                await this.callbacks?.onNewUserLogin?.();\n                this.reset();\n            }\n            return ret;\n        }\n        return false;\n    }\n    private async register(data: registerArgs) {\n        let args: ReqRegister = {\n            platform: PlatformUtil.getPlateform(),\n            platformType: sys.platform,\n            isGuest: data.isGuest || false,\n            userName: data.userName,\n            passWord: data.passWord,\n        };\n        const retRegister = await smc.net.hcGate.callApi(`Register`, args);\n        if (retRegister.isSucc) {\n            return retRegister.res;\n        } else {\n            oops.gui.toast(retRegister.err.message, true);\n            return false;\n        }\n    }\n    async confirmRegButton() {\n        if (this.checkAccountEdit() && this.checkPasswordEdit() && this.checkPasswordMatch()) {\n            let ret = await this.register({\n                userName: this.regAccountEBox.string,\n                passWord: this.regPasswordEBox.string,\n                isGuest: false,\n            });\n\n            if (ret) {\n                PromptManager.instance.confirm(7, async () => {\n                    // 获取服务器配置\n                    const httpUrl =\n                        smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';\n\n                    let res = await LoginViewComp.doLogin({\n                        userName: ret.userName,\n                        passWord: ret.passWord,\n                        isGuest: false,\n                        httpUrl: httpUrl,\n                    });\n\n                    if (res) {\n                        oops.log.logBusiness('Register user login success (new user)');\n                        await this.callbacks?.onNewUserLogin?.();\n                        this.reset();\n                    }\n                });\n            }\n        }\n        return;\n    }\n    private async googleLogin() {\n        let ret = await this.justAuthByPlatformType(JustAuthPlatformType.google);\n        return ret;\n    }\n    private async justAuthByPlatformType(type: JustAuthPlatformType) {\n        // 🎯 纯HTTP架构：第三方登录暂不支持，返回false\n        oops.log.logBusiness('⚠️ 纯HTTP架构：第三方登录功能暂不支持');\n        oops.gui.toast('第三方登录功能暂不支持', true);\n        return false;\n    }\n\n    private async connectGateWs() {\n        // 🎯 纯HTTP架构：WebSocket连接已移除\n        oops.log.logBusiness('⚠️ 纯HTTP架构：WebSocket连接已移除');\n        return false;\n    }\n\n    /**\n     * Facebook自动登录 - 纯HTTP版本\n     * 在Facebook环境中自动使用Facebook ID登录，返回服务端的用户信息\n     */\n    static async doFacebookLogin(): Promise<boolean> {\n        oops.log.logBusiness('🎮 开始Facebook登录（纯HTTP架构）...');\n\n        try {\n            // 获取Facebook登录数据\n            const facebookData = smc.fbInstantManager.getFacebookLoginData();\n            if (!facebookData) {\n                oops.log.logWarn('⚠️ 无法获取Facebook登录数据');\n                return false;\n            }\n\n            // 🔒 使用纯HTTP API进行Facebook登录\n            const httpUrl = ClientConfig.httpUrl;\n\n            // 准备Facebook登录请求\n            const reqFacebookLogin: ReqFacebookLogin = {\n                facebookId: facebookData.facebookId,\n                playerName: facebookData.playerName,\n                playerPhoto: facebookData.playerPhoto,\n                locale: facebookData.locale,\n                countryCode: facebookData.countryCode,\n                platform: facebookData.platform,\n                platformType: facebookData.platformType,\n                httpUrl: httpUrl, // 改为HTTP URL\n            };\n\n            oops.log.logBusiness('📤 发送Facebook登录请求到服务器（HTTP API）...', {\n                facebookId: facebookData.facebookId,\n                playerName: facebookData.playerName,\n            });\n\n            const ret = await smc.net.hcGate.callApi(`FacebookLogin`, reqFacebookLogin);\n\n            if (ret.isSucc) {\n                const fbRes = ret.res as any;\n                oops.log.logBusiness('✅ Facebook登录API成功:', {\n                    guuid: fbRes.guuid,\n                    userName: fbRes.userName,\n                    isNewUser: fbRes.isNewUser,\n                });\n\n                // 🔑 缓存登录结果供后续使用\n                LoginViewComp.lastFacebookLoginResult = {\n                    success: true,\n                    isNewUser: fbRes.isNewUser,\n                    userData: {\n                        guuid: fbRes.guuid,\n                        userName: fbRes.userName,\n                        facebookId: facebookData.facebookId,\n                        playerName: facebookData.playerName,\n                    },\n                };\n\n                // 存储SSO Token（如果需要）\n                if (fbRes.__ssoToken) {\n                    oops.storage.set('SSO_TOKEN', fbRes.__ssoToken);\n                    oops.log.logBusiness('🔑 SSO Token已存储');\n                }\n\n                oops.log.logBusiness(`🎉 Facebook${fbRes.isNewUser ? '新' : '老'}用户登录成功！`);\n                return true;\n            } else {\n                const errorInfo = {\n                    message: ret.err.message || 'Unknown error',\n                    code: ret.err.code || 'Unknown code',\n                };\n\n                oops.log.logError('❌ Facebook登录失败:', errorInfo);\n\n                // 缓存失败结果\n                LoginViewComp.lastFacebookLoginResult = {\n                    success: false,\n                    error: errorInfo,\n                };\n\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('💥 Facebook登录异常:', error);\n\n            // 缓存异常结果\n            LoginViewComp.lastFacebookLoginResult = {\n                success: false,\n                error: error,\n            };\n\n            return false;\n        }\n    }\n}\n"]}