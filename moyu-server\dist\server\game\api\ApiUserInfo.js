"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiUserInfo = ApiUserInfo;
const base_1 = require("../../../tsrpc/protocols/base");
const Rank_1 = require("../../../module/rank/bll/Rank");
const UserUtil_1 = require("../../../module/common/UserUtil");
async function ApiUserInfo(call) {
    const dbUser = call.dbUser;
    if (!dbUser) {
        return call.error('用户未找到', { code: 'USER_NOT_FOUND' });
    }
    try {
        // 获取国家排名
        dbUser.selfCountryRank = await Rank_1.Rank.getRankInCountry(dbUser.countryCode, dbUser.guuid);
        // 数据类型清理和转换
        const sanitizedUserData = (0, UserUtil_1.sanitizeUserDataTypes)(dbUser);
        call.succ({
            code: 0,
            message: '获取用户信息成功',
            updateType: base_1.DataUpdateType.FULL,
            timestamp: Date.now(),
            userGameData: {
                key: sanitizedUserData.key,
                guuid: sanitizedUserData.guuid,
                googleUuid: sanitizedUserData.googleUuid || '',
                facebookId: sanitizedUserData.facebookId || '',
                userName: sanitizedUserData.userName,
                nickName: sanitizedUserData.nickName,
                sex: sanitizedUserData.sex,
                createtime: sanitizedUserData.createtime,
                openid: sanitizedUserData.openid,
                platform: sanitizedUserData.platform,
                platformType: sanitizedUserData.platformType,
                avatar: sanitizedUserData.avatar,
                avatarId: sanitizedUserData.avatarId,
                countryCode: sanitizedUserData.countryCode,
                passTimes: sanitizedUserData.passTimes,
                index: sanitizedUserData.index,
                currCountryPassTimes: sanitizedUserData.currCountryPassTimes,
                lastChangeCountryTime: sanitizedUserData.lastChangeCountryTime,
                selfCountryRank: sanitizedUserData.selfCountryRank,
                propUseData: sanitizedUserData.propUseData,
                recordData: sanitizedUserData.recordData,
                isNewPlayer: sanitizedUserData.isNewPlayer,
                isGuest: sanitizedUserData.isGuest,
                lastStep: sanitizedUserData.lastStep
            }
        });
    }
    catch (error) {
        console.error('ApiUserInfo error:', error);
        call.error('获取用户信息失败', { code: 'GET_USER_INFO_FAILED' });
    }
}
