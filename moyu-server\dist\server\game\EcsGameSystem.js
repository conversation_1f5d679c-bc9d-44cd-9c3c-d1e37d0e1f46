"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcsGameSystem = void 0;
/*
 * @Author: dgflash
 * @Date: 2022-05-12 14:18:40
 * @LastEditors: dgflash
 * @LastEditTime: 2022-06-22 18:45:55
 */
const ECS_1 = require("../../core/ecs/ECS");
/** 游戏ECS系统 */
class EcsGameSystem extends ECS_1.ecs.System {
    constructor() {
        super();
    }
    init() {
        console.log('🎮 [EcsGameSystem] 游戏ECS系统初始化');
    }
    destroy() {
        console.log('🎮 [EcsGameSystem] 游戏ECS系统销毁');
    }
}
exports.EcsGameSystem = EcsGameSystem;
