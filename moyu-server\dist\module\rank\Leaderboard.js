"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Leaderboard = void 0;
const MongoDB_1 = require("../common/MongoDB");
class Leaderboard {
    constructor(db) {
        this.db = db; // 初始化数据库
    }
    /**
     * 创建一个新的排行榜
     * @param leaderboardId - 排行榜的唯一标识符
     * @param opts - 创建选项，包含可选的过期时间
     * @example
     * const options: CreateOptions = { ttl: 3600 }; // 设置 TTL 为 3600 秒
     * await leaderboard.create('test_leaderboard', options);
     */
    async create(leaderboardId, opts) {
        // 获取指定 leaderboardId 的集合
        const collection = this.getCollection(leaderboardId);
        if (opts) {
            // 根据 opts.ttl 的值决定索引的字段
            const spec = (opts.ttl)
                ? { createdAt: 1 } // 如果 opts.ttl 存在，使用 createdAt 字段
                : { expireAt: 1 }; // 否则使用 expireAt 字段
            // 根据 opts.ttl 的值决定索引的选项
            const options = (opts.ttl)
                ? { expireAfterSeconds: opts.ttl } // 如果 opts.ttl 存在，设置过期时间
                : { expireAfterSeconds: 0 }; // 否则不设置过期时间
            // 创建 TTL 索引，指定索引字段和选项
            if (spec && options) {
                // @ts-ignore
                await collection.createIndex(spec, options);
            }
            // 创建分数的降序索引，以便快速查询高分记录
            await collection.createIndex({ score: -1 });
            // 创建 ID 的升序索引，以便快速查找特定记录
            await collection.createIndex({ id: 1 });
        }
        /**
         * 删除指定的排行榜
         * @param leaderboardId - 排行榜的唯一标识符
         * @example
         * await leaderboard.destroy('test_leaderboard');
         */
    }
    async destroy(leaderboardId) {
        const collection = this.getCollection(leaderboardId);
        await collection.dropIndexes(); // 删除索引
        return collection.drop(); // 删除集合
    }
    /**
     * 记录用户的分数
     * @param leaderboardId - 排行榜的唯一标识符
     * @param record - 包含用户 ID 和分数的记录
     * @param expireAt - 可选的过期时间
     * @returns 更新后的记录状态
     * @example
     * const record = { id: 'user1', score: 100 };
     * await leaderboard.record('test_leaderboard', record);
     */
    async record(leaderboardId, record) {
        const row = Object.assign({}, record);
        const id = row.id; // 获取记录的 ID
        delete row.id; // 删除 ID 属性
        const score = row.score; // 获取记录的分数
        delete row.score; // 删除分数属性
        // 检查用户的分数是否高于最后一名
        const lastRecord = await this.getCollection(leaderboardId)
            .find({})
            .sort({ score: 1 }) // 按分数升序排序
            .limit(1) // 只获取最后一名
            .toArray();
        if (lastRecord.length > 0 && score <= lastRecord[0].score) {
            return Promise.resolve(false); // 如果分数不高于最后一名，返回 false
        }
        const update = {
            $max: { score: score }, // 更新分数
            $setOnInsert: { createdAt: new Date() }, // 设置创建时间
        };
        if (Object.keys(row).length > 0) {
            update.$set = row; // 如果有其他属性，设置它们
        }
        return new Promise((resolve, reject) => {
            this.getCollection(leaderboardId).
                findOneAndUpdate({ id }, update, { upsert: true }).
                then((r) => resolve(r.ok));
        });
    }
    /**
     * 自增用户的分数
     * @param leaderboardId - 排行榜的唯一标识符
     * @param record - 包含用户 ID 和分数的记录
     * @returns 更新后的记录状态
     * @example
     * const record = { id: 'user1' }; // 不需要提供分数，默认为 1
     * await leaderboard.incrementRecordScore('test_leaderboard', record);
     */
    async incrementRecordScore(leaderboardId, record) {
        const row = Object.assign({}, record);
        const id = row.id; // 获取记录的 ID
        const score = row.score || 1; // 获取记录的分数，默认为 1
        // 检查用户是否已存在
        const existingRecord = await this.getCollection(leaderboardId)
            .findOne({ id });
        if (existingRecord) {
            // 如果记录存在，则累加分数
            return this.getCollection(leaderboardId)
                .findOneAndUpdate({ id }, { $inc: { score: score } }, // 累加分数
            { returnDocument: 'after' } // 返回更新后的文档
            )
                .then((result) => result.value);
        }
        else {
            // 如果记录不存在，则创建新记录，默认分数为 1
            const newRecord = {
                id,
                score: 1, // 默认分数为 1
                createdAt: new Date() // 设置创建时间
            };
            await this.getCollection(leaderboardId).insertOne(newRecord);
            return newRecord; // 返回新创建的记录
        }
    }
    /**
     * 列出排行榜中的记录
     * @param leaderboardId - 排行榜的唯一标识符
     * @param opts - 列表选项，包含限制数量
     * @param limit - 限制数量，默认为 50
     * @returns 排行榜记录数组
     * @example
     * const records = await leaderboard.list('test_leaderboard', { limit: 5 });
     */
    list(leaderboardId, opts, limit = 50) {
        let collection = this.getCollection(leaderboardId);
        return collection.
            find({}). // 用动态查询条件
            project(opts). // 使用动态投影对象
            sort({ score: -1 }). // 按分数降序排序
            limit(limit). // 限制返回数量
            toArray();
    }
    /**
     * 根据 ID 获取排行榜中的记录
     * @param leaderboardId - 排行榜的唯一标识符
     * @param id - 用户的唯一标识符
     * @returns 用户的记录
     * @example
     * const userRecord = await leaderboard.get('test_leaderboard', 'user1');
     */
    async get(leaderboardId, id) {
        return await this.getCollection(leaderboardId).findOne({ id }); // 根据 ID 获取记录
    }
    /**
     * 获取用户在排行榜中的排名
     * @param leaderboardId - 排行榜的唯一标识符
     * @param id - 用户的唯一标识符
     * @returns 用户的排名
     * @example
     * const position = await leaderboard.position('test_leaderboard', 'user1');
     */
    async position(leaderboardId, id) {
        const user = await this.get(leaderboardId, id);
        if (!user)
            return 0; // 如果用户不存在，返回0
        // 使用 countDocuments 代替 toArray 来提高性能
        const count = await this.getCollection(leaderboardId).countDocuments({
            score: {
                $gt: user.score // 查找分数大于用户分数的记录数量
            }
        });
        const position = count + 1; // 计算用户的排名
        return position; // 返回用户的排名
    }
    /**
     * 获取用户周围的记录
     * @param leaderboardId - 排行榜的唯一标识符
     * @param id - 用户的唯一标识符
     * @param opts - 列表选项，包含限制数量
     * @param limit - 限制数量，默认为 50
     * @returns 用户周围的记录数组
     * @example
     * const surroundingRecords = await leaderboard.surrounding('test_leaderboard', 'user1', { limit: 3 });
     */
    async surrounding(leaderboardId, id, opts, limit = 50) {
        const collection = this.getCollection(leaderboardId);
        const user = await collection.findOne({ id });
        if (!user)
            return []; // 如果用户不存在，返回空数组
        const [before, after] = await Promise.all([
            collection.
                find({ id: { $ne: id }, score: { $lte: user.score } }).
                project(opts). // 不返回 _id 字段
                sort({ score: -1 }). // 按分数降序排序
                limit(limit). // 限制返回数量
                toArray(),
            collection.
                find({ id: { $ne: id }, score: { $gte: user.score } }).
                project(opts). // 不返回 _id 字段
                sort({ score: 1 }). // 按分数升序排序
                limit(limit). // 限制返回数量
                toArray(),
        ]);
        return after.reverse().concat(user).concat(before); // 返回用户周围的记录
    }
    //检查 集合是否存在，不存在则创建，并创建索引
    async checkCollection(leaderboardId) {
        let exists = await MongoDB_1.MongoDB.collectionExist(leaderboardId, this.db);
        if (!exists) {
            await this.db.createCollection(leaderboardId);
        }
        let collection = this.db.collection(`${leaderboardId}`); // 获取指定的集合
        // 检查并创建分数的降序索引，以便快速查询高分记录
        let indexExists = await collection.indexExists('score_-1');
        if (!indexExists) {
            await collection.createIndex({ score: -1 });
        }
        // 检查并创建 ID 的升序索引，以便快速查找特定记录
        indexExists = await collection.indexExists('id_1');
        if (!indexExists) {
            await collection.createIndex({ id: 1 });
        }
    }
    /**
     * 获取指定排行榜的集合，并创建索引
     * @param leaderboardId - 排行榜的唯一标识符
     * @returns 数据库集合
     * @example
     * const collection = leaderboard.getCollection('test_leaderboard');
     */
    getCollection(leaderboardId) {
        this.checkCollection(leaderboardId);
        let collection = this.db.collection(`${leaderboardId}`); // 获取指定的集合
        return collection;
    }
}
exports.Leaderboard = Leaderboard;
