"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiLogin = ApiLogin;
exports.handleLogin = handleLogin;
/*
 * @Author: dgflash
 * @Date: 2022-06-23 09:38:35
 * @LastEditors: dgflash
 * @LastEditTime: 2022-07-06 17:02:12
 */
const bcrypt_1 = __importDefault(require("bcrypt"));
const RedisSessionManager_1 = require("../../../module/common/RedisSessionManager");
const User_1 = require("../../../module/account/bll/User");
async function ApiLogin(call) {
    console.log('🔍 [Login] 收到登录请求:', JSON.stringify({
        server: call.req.server,
        userName: call.req.userName,
        // 不记录密码
    }, null, 2));
    try {
        // 根据用户名查找用户
        var dUser = await User_1.User.getUserByUserName(call.req.userName);
        if (!dUser) {
            console.log('❌ [Login] 用户不存在:', call.req.userName);
            call.error('User_Not_Exist');
            return;
        }
        // 验证密码
        if (!bcrypt_1.default.compareSync(call.req.passWord, dUser.passWord)) {
            console.log('❌ [Login] 密码错误:', call.req.userName);
            call.error('login_fail');
            return;
        }
        // 使用Redis会话管理器创建会话
        const sessionMetadata = {
            ip: call.conn.ip,
            userAgent: call.req.userAgent || 'unknown',
            platform: dUser.platform || 'unknown'
        };
        const ssoToken = await RedisSessionManager_1.RedisSessionManager.createSession(dUser, sessionMetadata);
        console.log('✅ [Login] 登录成功:', {
            userName: dUser.userName,
            guuid: dUser.guuid,
            ssoToken: ssoToken.substring(0, 8) + '...' // 只显示部分token用于调试
        });
        // 统一服务器模式 - 直接返回成功响应
        call.succ({
            code: 0,
            message: "登录成功",
            __ssoToken: ssoToken,
        });
    }
    catch (error) {
        console.error('💥 [Login] 登录过程出错:', error);
        call.error('common_server_error');
    }
}
/**
 * 处理用户登录逻辑，供其他模块调用
 * @param dUser 用户数据
 * @param metadata 会话元数据
 * @returns 登录结果
 */
async function handleLogin(dUser, metadata) {
    try {
        // 使用Redis会话管理器创建会话
        const ssoToken = await RedisSessionManager_1.RedisSessionManager.createSession(dUser, metadata);
        console.log('✅ [handleLogin] 登录成功:', {
            userName: dUser.userName,
            guuid: dUser.guuid,
            ssoToken: ssoToken.substring(0, 8) + '...' // 只显示部分token用于调试
        });
        return {
            success: true,
            ssoToken: ssoToken,
            userData: {
                userName: dUser.userName,
                guuid: dUser.guuid,
            }
        };
    }
    catch (error) {
        console.error('💥 [handleLogin] 登录过程出错:', error);
        return {
            success: false,
            error: 'common_server_error'
        };
    }
}
