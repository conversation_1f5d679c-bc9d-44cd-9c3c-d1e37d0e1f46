"use strict";
/*
 * @Author: dgflash
 * @Date: 2022-06-28 17:57:23
 * @LastEditors: dgflash
 * @LastEditTime: 2022-09-20 10:29:15
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonFactory = void 0;
const tsrpc_1 = require("tsrpc");
const Security_1 = require("../../tsrpc/models/Security");
const ShareConfig_1 = require("../../tsrpc/models/ShareConfig");
const ServiceProtoGame_1 = require("../../tsrpc/protocols/ServiceProtoGame");
const ServiceProtoGate_1 = require("../../tsrpc/protocols/ServiceProtoGate");
const Account_1 = require("../account/Account");
const Config_1 = require("../config/Config");
const CommonUtil_1 = require("./CommonUtil");
/**
 * TSRPC 客户端、服务器对象工厂
 * 🎯 纯HTTP架构：移除所有WebSocket相关方法
 */
class CommonFactory {
    /** 创建 Http 网关服务端对象 */
    static createHsGate() {
        var options = {
            port: parseInt(Config_1.Config.gate.port),
            json: ShareConfig_1.ShareConfig.json,
            https: CommonUtil_1.CommonUtil.getCertificate()
        };
        var hs = new tsrpc_1.HttpServer(ServiceProtoGate_1.serviceProto, options);
        this.flowServerApi(hs);
        return hs;
    }
    /** 创建 Http 游戏服务端对象 */
    static createHsGame() {
        var options = {
            port: parseInt(Config_1.Config.game.httpPort),
            json: ShareConfig_1.ShareConfig.json,
            https: CommonUtil_1.CommonUtil.getCertificate()
        };
        var hs = new tsrpc_1.HttpServer(ServiceProtoGame_1.serviceProto, options);
        this.flowServerApi(hs);
        Account_1.account.checkAuth(hs); // 检查客户端身份  
        return hs;
    }
    /** 创建游戏服务器的 Http 客户端连接 */
    static createHcGame(serverUrl) {
        let hc = new tsrpc_1.HttpClient(ServiceProtoGame_1.serviceProto, { server: serverUrl });
        this.flowClientApi(hc);
        return hc;
    }
    /** 创建gate服务器的 Http 客户端连接 */
    static createHcGate() {
        let url = `${ShareConfig_1.ShareConfig.https ? "https" : "http"}://${ShareConfig_1.ShareConfig.gate}/`;
        let hc = new tsrpc_1.HttpClient(ServiceProtoGate_1.serviceProto, { server: url });
        this.flowClientApi(hc);
        return hc;
    }
    /** HTTP 服务端协议数据加密、解密 */
    static flowServerApi(hs) {
        if (!ShareConfig_1.ShareConfig.security)
            return;
        // 在将数据发送到网络之前，通常要进行加密/解密
        hs.flows.preSendDataFlow.push((v) => {
            if (v.data instanceof Uint8Array) {
                v.data = Security_1.Security.encrypt(v.data);
            }
            return v;
        });
        // 在处理接收到的数据之前，通常要进行加密/解密
        hs.flows.preRecvDataFlow.push((v) => {
            if (v.data instanceof Uint8Array) {
                v.data = Security_1.Security.decrypt(v.data);
            }
            return v;
        });
    }
    /** HTTP 客户端协议数据加密、解密 */
    static flowClientApi(hc) {
        if (!ShareConfig_1.ShareConfig.security)
            return;
        hc.flows.preSendDataFlow.push((v) => {
            if (v.data instanceof Uint8Array) {
                v.data = Security_1.Security.encrypt(v.data);
            }
            return v;
        });
        // 在处理接收到的数据之前，通常要进行加密/解密
        hc.flows.preRecvDataFlow.push((v) => {
            if (v.data instanceof Uint8Array) {
                v.data = Security_1.Security.decrypt(v.data);
            }
            return v;
        });
    }
}
exports.CommonFactory = CommonFactory;
