"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiFacebookLogin = ApiFacebookLogin;
const User_1 = require("../../../module/account/bll/User");
const UserService_1 = require("../../../module/account/bll/UserService");
// 临时导入登录处理逻辑（需要从gate迁移）
async function handleLogin(dUser, sessionMetadata) {
    // 简化的登录处理，后续可以完善
    return {
        success: true,
        ssoToken: `temp_token_${Date.now()}_${dUser.key}`,
        error: undefined
    };
}
async function ApiFacebookLogin(call) {
    const startTime = Date.now();
    console.log('🎮 [FacebookLogin] ==================== 开始处理Facebook登录 ====================');
    console.log('🎮 [FacebookLogin] 收到Facebook登录请求:', JSON.stringify({
        facebookId: call.req.facebookId,
        playerName: call.req.playerName,
        playerPhoto: call.req.playerPhoto ? call.req.playerPhoto.substring(0, 50) + '...' : 'null',
        locale: call.req.locale,
        countryCode: call.req.countryCode,
        platform: call.req.platform,
        platformType: call.req.platformType,
        clientIP: call.conn.ip
    }, null, 2));
    try {
        const { facebookId, playerName, playerPhoto, locale, countryCode, platform, platformType, httpUrl } = call.req;
        // 1. 检查是否已存在Facebook用户
        console.log('🔍 [FacebookLogin] 步骤1: 查询现有Facebook用户...');
        let dUser = await User_1.User.getUserByFacebookId(facebookId);
        let isNewUser = false;
        if (!dUser) {
            // 2. 新用户：自动注册
            console.log('🆕 [FacebookLogin] 步骤2: 新Facebook用户，开始自动注册...');
            console.log('📝 [FacebookLogin] 注册数据:', {
                facebookId: facebookId,
                playerName: playerName,
                locale: locale,
                countryCode: countryCode,
                platform: platform,
                platformType: platformType
            });
            const registerResult = await UserService_1.UserService.registerFacebookUser({
                facebookId,
                playerName,
                playerPhoto,
                locale,
                countryCode,
                platform,
                platformType,
                ip: call.conn.ip
            });
            if (!registerResult) {
                console.log('❌ [FacebookLogin] Facebook用户注册失败');
                call.error('facebook_register_failed');
                return;
            }
            console.log('✅ [FacebookLogin] 用户注册成功:', {
                guuid: registerResult.guuid,
                userName: registerResult.userName,
                generatedPassword: registerResult.passWord
            });
            // 获取新注册的用户
            dUser = await User_1.User.getUserByFacebookId(facebookId);
            if (!dUser) {
                console.log('❌ [FacebookLogin] 注册后无法找到用户');
                call.error('facebook_user_not_found');
                return;
            }
            isNewUser = true;
            console.log('🎉 [FacebookLogin] Facebook新用户创建完成:', {
                key: dUser.key,
                guuid: dUser.guuid,
                userName: dUser.userName,
                nickName: dUser.nickName,
                facebookId: dUser.facebookId,
                countryCode: dUser.countryCode,
                platform: dUser.platform,
                createTime: dUser.createtime
            });
        }
        else {
            // 3. 老用户：更新信息
            console.log('👤 [FacebookLogin] 步骤3: 老Facebook用户登录');
            console.log('📋 [FacebookLogin] 现有用户信息:', {
                key: dUser.key,
                guuid: dUser.guuid,
                userName: dUser.userName,
                nickName: dUser.nickName,
                facebookId: dUser.facebookId,
                countryCode: dUser.countryCode,
                platform: dUser.platform,
                passTimes: dUser.passTimes,
                lastLoginTime: dUser.createtime
            });
            // 更新玩家信息（昵称、头像等可能会变化）
            let needUpdate = false;
            const oldData = {
                nickName: dUser.nickName,
                avatar: dUser.avatar,
                countryCode: dUser.countryCode
            };
            if (dUser.nickName !== playerName) {
                console.log(`🔄 [FacebookLogin] 昵称变更: "${dUser.nickName}" -> "${playerName}"`);
                dUser.nickName = playerName;
                needUpdate = true;
            }
            if (dUser.avatar !== playerPhoto) {
                console.log(`🔄 [FacebookLogin] 头像变更: "${dUser.avatar}" -> "${playerPhoto}"`);
                dUser.avatar = playerPhoto || dUser.avatar;
                needUpdate = true;
            }
            if (dUser.countryCode !== countryCode) {
                console.log(`🔄 [FacebookLogin] 国家变更: "${dUser.countryCode}" -> "${countryCode}"`);
                dUser.countryCode = countryCode;
                needUpdate = true;
            }
            if (needUpdate) {
                await User_1.User.updateUserData(dUser._id, dUser);
                console.log('✅ [FacebookLogin] 玩家信息更新完成:', {
                    oldData: oldData,
                    newData: {
                        nickName: dUser.nickName,
                        avatar: dUser.avatar,
                        countryCode: dUser.countryCode
                    }
                });
            }
            else {
                console.log('ℹ️ [FacebookLogin] 玩家信息无变化，跳过更新');
            }
        }
        // 4. 执行登录流程
        console.log('🔐 [FacebookLogin] 步骤4: 执行服务器登录流程...');
        const sessionMetadata = {
            ip: call.conn.ip,
            userAgent: httpUrl || 'facebook-instant-games',
            platform: platform || 'facebook',
            facebookId: facebookId,
            playerName: playerName,
            locale: locale
        };
        console.log('📝 [FacebookLogin] 会话元数据:', sessionMetadata);
        const loginResult = await handleLogin(dUser, sessionMetadata);
        if (!loginResult.success) {
            console.log('❌ [FacebookLogin] 登录流程失败:', loginResult.error);
            call.error('facebook_login_failed');
            return;
        }
        const processingTime = Date.now() - startTime;
        console.log('🎉 [FacebookLogin] ==================== Facebook登录成功 ====================');
        console.log('✅ [FacebookLogin] 登录结果汇总:', {
            用户类型: isNewUser ? '新用户' : '老用户',
            用户ID: dUser.key,
            GUUID: dUser.guuid,
            用户名: dUser.userName,
            昵称: dUser.nickName,
            FacebookID: dUser.facebookId,
            国家: dUser.countryCode,
            平台: dUser.platform,
            通关次数: dUser.passTimes,
            SSO令牌: loginResult.ssoToken ? loginResult.ssoToken.substring(0, 8) + '...' : 'undefined',
            处理耗时: processingTime + 'ms'
        });
        // 5. 记录Facebook用户活跃数据（用于后续分析）
        console.log('📊 [FacebookLogin] 记录用户活跃数据...');
        console.log('📊 [FacebookLogin] Facebook用户统计:', {
            总登录次数: '待实现计数器',
            今日活跃: '待实现计数器',
            地区分布: countryCode,
            平台分布: platform
        });
        // 6. 返回成功响应
        const response = {
            code: 0,
            message: "Facebook登录成功",
            guuid: dUser.guuid,
            userName: dUser.userName,
            isNewUser: isNewUser,
            __ssoToken: loginResult.ssoToken || ''
        };
        console.log('📤 [FacebookLogin] 返回响应:', {
            ...response,
            __ssoToken: response.__ssoToken ? response.__ssoToken.substring(0, 8) + '...' : 'undefined'
        });
        call.succ(response);
    }
    catch (error) {
        const processingTime = Date.now() - startTime;
        console.error('💥 [FacebookLogin] ==================== Facebook登录失败 ====================');
        console.error('💥 [FacebookLogin] 错误详情:', {
            错误信息: error.message,
            错误堆栈: error.stack,
            处理耗时: processingTime + 'ms',
            请求数据: {
                facebookId: call.req.facebookId,
                playerName: call.req.playerName,
                locale: call.req.locale,
                countryCode: call.req.countryCode
            }
        });
        call.error('common_server_error');
    }
}
