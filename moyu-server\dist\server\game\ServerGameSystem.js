"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerGameSystem = void 0;
/*
 * @Author: dgflash
 * @Date: 2022-05-12 14:18:40
 * @LastEditors: dgflash
 * @LastEditTime: 2022-06-22 18:45:55
 */
const ECS_1 = require("../../core/ecs/ECS");
const EcsGameSystem_1 = require("./EcsGameSystem");
/** 游戏服务器模块 */
class ServerGameSystem extends ECS_1.ecs.RootSystem {
    constructor() {
        super();
        this.add(new EcsGameSystem_1.EcsGameSystem());
    }
}
exports.ServerGameSystem = ServerGameSystem;
