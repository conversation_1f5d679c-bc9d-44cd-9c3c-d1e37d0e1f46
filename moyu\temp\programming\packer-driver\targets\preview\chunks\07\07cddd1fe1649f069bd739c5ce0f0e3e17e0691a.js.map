{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/initialize/view/LoginViewComp.ts"], "names": ["_decorator", "EditBox", "Node", "sys", "oops", "PlatformUtil", "ecs", "CCVMParentComp", "JustAuthPlatformType", "CommonTools", "ClientConfig", "GameStorageConfig", "PromptManager", "smc", "ccclass", "property", "LoginViewComp", "register", "type", "data", "isRegister", "loginFail", "isSafeAccount", "isSafePassword", "isPasswordMatch", "lastLogin", "callbacks", "getLastFacebookLoginResult", "lastFacebookLoginResult", "onAdded", "reset", "gui", "removeByNode", "node", "onLoad", "setButton", "start", "regAccountEBox", "on", "checkAccountEdit", "regPasswordEBox", "checkPasswordEdit", "regPasswordAgainEBox", "checkPasswordMatch", "loginAccountEBox", "string", "userName", "loginPasswordEBox", "passWord", "ret", "checkUsername", "checkPassword", "googleButton1", "googleLogin", "googleButton2", "doAfterLogin", "httpUrl", "log", "logBusiness", "ssoToken", "storage", "get", "exists", "length", "preview", "substring", "res", "role", "loadData", "success", "do<PERSON><PERSON><PERSON>", "loginData", "reqLoginData", "server", "net", "hcGate", "callApi", "isSucc", "set", "UserDumpKey", "toast", "err", "message", "loginButton", "url", "initialize", "GateModel", "area", "isGuest", "onOldUserLogin", "forgetPasswordButton", "loginGuestButton", "onNewUserLogin", "args", "platform", "getPlateform", "platformType", "retRegister", "confirmRegButton", "instance", "confirm", "justAuthByPlatformType", "google", "connectGateWs", "doFacebookLogin", "facebookData", "fbInstantManager", "getFacebookLoginData", "log<PERSON>arn", "reqFacebookLogin", "facebookId", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "locale", "countryCode", "fbRes", "guuid", "isNewUser", "userData", "__ssoToken", "errorInfo", "code", "logError", "error"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,G,OAAAA,G;;AAC3BC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,oB,iBAAAA,oB;;AACAC,MAAAA,W,iBAAAA,W;;AAGAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,a,kBAAAA,a;;AACAC,MAAAA,G,kBAAAA,G;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;AAmB9B;+BAGagB,a,WAFZF,OAAO,CAAC,eAAD,C,UACP;AAAA;AAAA,sBAAIG,QAAJ,CAAa,WAAb,EAA0B,KAA1B,C,UAUIF,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAACb,IAAD,C,UAGRa,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,UAERc,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEjB;AAAR,OAAD,C,qDAzBb,MAEae,aAFb;AAAA;AAAA,4CAEkD;AAAA;AAAA;;AAC9C;AAD8C,eAE9CG,IAF8C,GAElC;AACRC,YAAAA,UAAU,EAAE,CADJ;AACO;AACfC,YAAAA,SAAS,EAAE,CAFH;AAEM;AACdC,YAAAA,aAAa,EAAE,CAHP;AAGU;AAClBC,YAAAA,cAAc,EAAE,CAJR;AAIW;AACnBC,YAAAA,eAAe,EAAE,CALT,CAKY;;AALZ,WAFkC;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AA0B9C;AA1B8C,eA2BtCC,SA3BsC;AA4B9C;AA5B8C,eA6BtCC,SA7BsC;AAAA;;AAkC9C;AACJ;AACA;AACqC,eAA1BC,0BAA0B,GAAQ;AACrC,iBAAOX,aAAa,CAACY,uBAArB;AACH;;AAEDC,QAAAA,OAAO,CAACV,IAAD,EAA8D;AACjE,eAAKM,SAAL,GAAiBN,IAAjB,oBAAiBA,IAAI,CAAEM,SAAvB;AACA,eAAKC,SAAL,GAAiBP,IAAjB,oBAAiBA,IAAI,CAAEO,SAAvB;AACH;;AACDI,QAAAA,KAAK,GAAS;AACV;AAAA;AAAA,4BAAKC,GAAL,CAASC,YAAT,CAAsB,KAAKC,IAA3B;AACH;;AAEDC,QAAAA,MAAM,GAAS;AACX,gBAAMA,MAAN;AACA,eAAKC,SAAL,CAAe,KAAf;AACH;;AACSC,QAAAA,KAAK,GAAS;AACpB,eAAKC,cAAL,CAAoBJ,IAApB,CAAyBK,EAAzB,CAA4B,mBAA5B,EAAiD,KAAKC,gBAAtD,EAAwE,IAAxE;AACA,eAAKF,cAAL,CAAoBJ,IAApB,CAAyBK,EAAzB,CAA4B,oBAA5B,EAAkD,KAAKC,gBAAvD,EAAyE,IAAzE;AACA,eAAKC,eAAL,CAAqBP,IAArB,CAA0BK,EAA1B,CAA6B,mBAA7B,EAAkD,KAAKG,iBAAvD,EAA0E,IAA1E;AACA,eAAKD,eAAL,CAAqBP,IAArB,CAA0BK,EAA1B,CAA6B,oBAA7B,EAAmD,KAAKG,iBAAxD,EAA2E,IAA3E;AACA,eAAKC,oBAAL,CAA0BT,IAA1B,CAA+BK,EAA/B,CAAkC,mBAAlC,EAAuD,KAAKK,kBAA5D,EAAgF,IAAhF;AACA,eAAKD,oBAAL,CAA0BT,IAA1B,CAA+BK,EAA/B,CAAkC,oBAAlC,EAAwD,KAAKK,kBAA7D,EAAiF,IAAjF;;AACA,cAAI,KAAKlB,SAAT,EAAoB;AAAA;;AAChB,iBAAKmB,gBAAL,CAAsBC,MAAtB,GAA+B,yBAAKpB,SAAL,qCAAgBqB,QAAhB,KAA4B,EAA3D;AACA,iBAAKC,iBAAL,CAAuBF,MAAvB,GAAgC,0BAAKpB,SAAL,sCAAgBuB,QAAhB,KAA4B,EAA5D;AACH;;AACD,eAAKP,iBAAL;AACA,eAAKF,gBAAL;AACH;;AACOA,QAAAA,gBAAgB,GAAG;AACvB,cAAIU,GAAG,GAAG;AAAA;AAAA,0CAAYC,aAAZ,CAA0B,KAAKb,cAAL,CAAoBQ,MAA9C,CAAV;AACA,eAAK1B,IAAL,CAAUG,aAAV,GAA0B2B,GAAG,GAAG,CAAH,GAAO,CAApC;AACA,iBAAOA,GAAP;AACH;;AACOR,QAAAA,iBAAiB,GAAG;AACxB,cAAIQ,GAAG,GAAG;AAAA;AAAA,0CAAYE,aAAZ,CAA0B,KAAKX,eAAL,CAAqBK,MAA/C,CAAV;AACA,eAAK1B,IAAL,CAAUI,cAAV,GAA2B0B,GAAG,GAAG,CAAH,GAAO,CAArC;AACA,iBAAOA,GAAP;AACH;;AAEON,QAAAA,kBAAkB,GAAG;AACzB,cAAI,KAAKH,eAAL,CAAqBK,MAArB,KAAgC,KAAKH,oBAAL,CAA0BG,MAA9D,EAAsE;AAClE,iBAAK1B,IAAL,CAAUK,eAAV,GAA4B,CAA5B;AACA,mBAAO,KAAP;AACH;;AACD,eAAKL,IAAL,CAAUK,eAAV,GAA4B,CAA5B;AACA,iBAAO,IAAP;AACH;;AACD4B,QAAAA,aAAa,GAAG;AACZ,eAAKC,WAAL;AACH;;AACDC,QAAAA,aAAa,GAAG;AACZ,eAAKD,WAAL;AACH;;AACgC,eAAZE,YAAY,CAACC,OAAD,EAAkB;AAAA;AAC/C;AAAA;AAAA,8BAAKC,GAAL,CAASC,WAAT,CAAqB,iCAArB,EAAwD;AAAEF,cAAAA;AAAF,aAAxD,EAD+C,CAG/C;;AACA,gBAAMG,QAAQ,GAAG;AAAA;AAAA,8BAAKC,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAjB;AACA;AAAA;AAAA,8BAAKJ,GAAL,CAASC,WAAT,CAAqB,mBAArB,EAA0C;AACtCI,cAAAA,MAAM,EAAE,CAAC,CAACH,QAD4B;AAEtCI,cAAAA,MAAM,EAAEJ,QAAQ,GAAGA,QAAQ,CAACI,MAAZ,GAAqB,CAFC;AAGtCC,cAAAA,OAAO,EAAEL,QAAQ,GAAGA,QAAQ,CAACM,SAAT,CAAmB,CAAnB,EAAsB,CAAtB,IAA2B,KAA9B,GAAsC;AAHjB,aAA1C;AAMA;AAAA;AAAA,8BAAKR,GAAL,CAASC,WAAT,CAAqB,qCAArB;AAEA,gBAAIQ,GAAG,SAAS;AAAA;AAAA,4BAAIC,IAAJ,CAASC,QAAT,EAAhB;AACA;AAAA;AAAA,8BAAKX,GAAL,CAASC,WAAT,CAAqB,uBAArB,EAA8C;AAAEW,cAAAA,OAAO,EAAEH;AAAX,aAA9C;AAEA,mBAAOA,GAAP,CAhB+C,CAgBnC;AAhBmC;AAiBlD;;AACmB,eAAPI,OAAO,CAACC,SAAD,EAAuB;AAAA;AACvC,gBAAIC,YAAY,GAAG;AACfC,cAAAA,MAAM,EAAEF,SAAS,CAACf,OADH;AAEfV,cAAAA,QAAQ,EAAEyB,SAAS,CAACzB,QAFL;AAGfE,cAAAA,QAAQ,EAAEuB,SAAS,CAACvB;AAHL,aAAnB;AAKA,gBAAMC,GAAG,SAAS;AAAA;AAAA,4BAAIyB,GAAJ,CAAQC,MAAR,CAAeC,OAAf,UAAgCJ,YAAhC,CAAlB;;AACA,gBAAIvB,GAAG,CAAC4B,MAAR,EAAgB;AACZ,kBAAIX,GAAG,SAASlD,aAAa,CAACuC,YAAd,CAA2BgB,SAAS,CAACf,OAArC,CAAhB;;AACA,kBAAIU,GAAJ,EAAS;AACL;AACA;AAAA;AAAA,kCAAKN,OAAL,CAAakB,GAAb,CAAiB;AAAA;AAAA,4DAAkBC,WAAnC,EAAgDP,YAAhD;AACH;;AACD,qBAAON,GAAP;AACH,aAPD,MAOO;AACH;AAAA;AAAA,gCAAKnC,GAAL,CAASiD,KAAT,CAAe/B,GAAG,CAACgC,GAAJ,CAAQC,OAAvB,EAAgC,IAAhC;AACA,qBAAO,KAAP;AACH;AAjBsC;AAkB1C;;AAEKC,QAAAA,WAAW,GAAG;AAAA;;AAAA;AAAA;;AAChB,gBAAI,KAAI,CAACvC,gBAAL,CAAsBC,MAAtB,CAA6BkB,MAA7B,IAAuC,CAAvC,IAA4C,KAAI,CAAChB,iBAAL,CAAuBF,MAAvB,CAA8BkB,MAA9B,IAAwC,CAAxF,EAA2F;AACvF;AAAA;AAAA,gCAAKhC,GAAL,CAASiD,KAAT,CAAe,aAAf,EAA8B,IAA9B;AACA,qBAAO,KAAP;AACH,aAJe,CAMhB;;;AACA,gBAAMI,GAAG,GAAG;AAAA;AAAA,4BAAIC,UAAJ,CAAeC,SAAf,CAAyBC,IAAzB,2CAA+B/B,OAA/B,KAA0C,uBAAtD;AAEA,gBAAIP,GAAG,SAASjC,aAAa,CAACsD,OAAd,CAAsB;AAClCxB,cAAAA,QAAQ,EAAE,KAAI,CAACF,gBAAL,CAAsBC,MADE;AAElCG,cAAAA,QAAQ,EAAE,KAAI,CAACD,iBAAL,CAAuBF,MAFC;AAGlC2C,cAAAA,OAAO,EAAE,KAHyB;AAIlChC,cAAAA,OAAO,EAAE4B;AAJyB,aAAtB,CAAhB;;AAOA,gBAAInC,GAAJ,EAAS;AAAA;;AACL;AAAA;AAAA,gCAAKQ,GAAL,CAASC,WAAT,CAAqB,+BAArB;AACA,uCAAM,KAAI,CAAChC,SAAX,aAAM,gBAAgB+D,cAAtB,oBAAM,gBAAgBA,cAAhB,EAAN;;AACA,cAAA,KAAI,CAAC3D,KAAL;AACH;;AACD,mBAAOmB,GAAP;AArBgB;AAsBnB;;AAEDyC,QAAAA,oBAAoB,GAAG,CAAE;;AAEnBC,QAAAA,gBAAgB,GAAG;AAAA;;AAAA;AACrB,gBAAIzB,GAAG,SAAS,MAAI,CAACjD,QAAL,CAAc;AAAEuE,cAAAA,OAAO,EAAE;AAAX,aAAd,CAAhB;;AACA,gBAAItB,GAAJ,EAAS;AAAA;;AACL;AACA,kBAAMV,OAAO,GAAG;AAAA;AAAA,8BAAI6B,UAAJ,CAAeC,SAAf,CAAyBC,IAAzB,4CAA+B/B,OAA/B,KAA0C,uBAA1D;AAEA,kBAAIP,GAAG,SAASjC,aAAa,CAACsD,OAAd,CAAsB;AAClCxB,gBAAAA,QAAQ,EAAEoB,GAAG,CAACpB,QADoB;AAElCE,gBAAAA,QAAQ,EAAEkB,GAAG,CAAClB,QAFoB;AAGlCwC,gBAAAA,OAAO,EAAE,IAHyB;AAIlChC,gBAAAA,OAAO,EAAEA;AAJyB,eAAtB,CAAhB;;AAOA,kBAAIP,GAAJ,EAAS;AAAA;;AACL;AAAA;AAAA,kCAAKQ,GAAL,CAASC,WAAT,CAAqB,gCAArB;AACA,0CAAM,MAAI,CAAChC,SAAX,aAAM,iBAAgBkE,cAAtB,oBAAM,iBAAgBA,cAAhB,EAAN;;AACA,gBAAA,MAAI,CAAC9D,KAAL;AACH;;AACD,qBAAOmB,GAAP;AACH;;AACD,mBAAO,KAAP;AApBqB;AAqBxB;;AACahC,QAAAA,QAAQ,CAACE,IAAD,EAAqB;AAAA;AACvC,gBAAI0E,IAAiB,GAAG;AACpBC,cAAAA,QAAQ,EAAE;AAAA;AAAA,gDAAaC,YAAb,EADU;AAEpBC,cAAAA,YAAY,EAAE7F,GAAG,CAAC2F,QAFE;AAGpBN,cAAAA,OAAO,EAAErE,IAAI,CAACqE,OAAL,IAAgB,KAHL;AAIpB1C,cAAAA,QAAQ,EAAE3B,IAAI,CAAC2B,QAJK;AAKpBE,cAAAA,QAAQ,EAAE7B,IAAI,CAAC6B;AALK,aAAxB;AAOA,gBAAMiD,WAAW,SAAS;AAAA;AAAA,4BAAIvB,GAAJ,CAAQC,MAAR,CAAeC,OAAf,aAAmCiB,IAAnC,CAA1B;;AACA,gBAAII,WAAW,CAACpB,MAAhB,EAAwB;AACpB,qBAAOoB,WAAW,CAAC/B,GAAnB;AACH,aAFD,MAEO;AACH;AAAA;AAAA,gCAAKnC,GAAL,CAASiD,KAAT,CAAeiB,WAAW,CAAChB,GAAZ,CAAgBC,OAA/B,EAAwC,IAAxC;AACA,qBAAO,KAAP;AACH;AAdsC;AAe1C;;AACKgB,QAAAA,gBAAgB,GAAG;AAAA;;AAAA;AACrB,gBAAI,MAAI,CAAC3D,gBAAL,MAA2B,MAAI,CAACE,iBAAL,EAA3B,IAAuD,MAAI,CAACE,kBAAL,EAA3D,EAAsF;AAClF,kBAAIM,GAAG,SAAS,MAAI,CAAChC,QAAL,CAAc;AAC1B6B,gBAAAA,QAAQ,EAAE,MAAI,CAACT,cAAL,CAAoBQ,MADJ;AAE1BG,gBAAAA,QAAQ,EAAE,MAAI,CAACR,eAAL,CAAqBK,MAFL;AAG1B2C,gBAAAA,OAAO,EAAE;AAHiB,eAAd,CAAhB;;AAMA,kBAAIvC,GAAJ,EAAS;AACL;AAAA;AAAA,oDAAckD,QAAd,CAAuBC,OAAvB,CAA+B,CAA/B,iCAAkC,aAAY;AAAA;;AAC1C;AACA,sBAAM5C,OAAO,GACT;AAAA;AAAA,kCAAI6B,UAAJ,CAAeC,SAAf,CAAyBC,IAAzB,4CAA+B/B,OAA/B,KAA0C,uBAD9C;AAGA,sBAAIU,GAAG,SAASlD,aAAa,CAACsD,OAAd,CAAsB;AAClCxB,oBAAAA,QAAQ,EAAEG,GAAG,CAACH,QADoB;AAElCE,oBAAAA,QAAQ,EAAEC,GAAG,CAACD,QAFoB;AAGlCwC,oBAAAA,OAAO,EAAE,KAHyB;AAIlChC,oBAAAA,OAAO,EAAEA;AAJyB,mBAAtB,CAAhB;;AAOA,sBAAIU,GAAJ,EAAS;AAAA;;AACL;AAAA;AAAA,sCAAKT,GAAL,CAASC,WAAT,CAAqB,wCAArB;AACA,8CAAM,MAAI,CAAChC,SAAX,aAAM,iBAAgBkE,cAAtB,oBAAM,iBAAgBA,cAAhB,EAAN;;AACA,oBAAA,MAAI,CAAC9D,KAAL;AACH;AACJ,iBAjBD;AAkBH;AACJ;;AACD;AA7BqB;AA8BxB;;AACauB,QAAAA,WAAW,GAAG;AAAA;;AAAA;AACxB,gBAAIJ,GAAG,SAAS,MAAI,CAACoD,sBAAL,CAA4B;AAAA;AAAA,8DAAqBC,MAAjD,CAAhB;AACA,mBAAOrD,GAAP;AAFwB;AAG3B;;AACaoD,QAAAA,sBAAsB,CAACnF,IAAD,EAA6B;AAAA;AAC7D;AACA;AAAA;AAAA,8BAAKuC,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACA;AAAA;AAAA,8BAAK3B,GAAL,CAASiD,KAAT,CAAe,aAAf,EAA8B,IAA9B;AACA,mBAAO,KAAP;AAJ6D;AAKhE;;AAEauB,QAAAA,aAAa,GAAG;AAAA;AAC1B;AACA;AAAA;AAAA,8BAAK9C,GAAL,CAASC,WAAT,CAAqB,2BAArB;AACA,mBAAO,KAAP;AAH0B;AAI7B;AAED;AACJ;AACA;AACA;;;AACgC,eAAf8C,eAAe,GAAqB;AAAA;AAC7C;AAAA;AAAA,8BAAK/C,GAAL,CAASC,WAAT,CAAqB,6BAArB;;AAEA,gBAAI;AACA;AACA,kBAAM+C,YAAY,GAAG;AAAA;AAAA,8BAAIC,gBAAJ,CAAqBC,oBAArB,EAArB;;AACA,kBAAI,CAACF,YAAL,EAAmB;AACf;AAAA;AAAA,kCAAKhD,GAAL,CAASmD,OAAT,CAAiB,qBAAjB;AACA,uBAAO,KAAP;AACH,eAND,CAQA;;;AACA,kBAAMpD,OAAO,GAAG;AAAA;AAAA,gDAAaA,OAA7B,CATA,CAWA;;AACA,kBAAMqD,gBAAkC,GAAG;AACvCC,gBAAAA,UAAU,EAAEL,YAAY,CAACK,UADc;AAEvCC,gBAAAA,UAAU,EAAEN,YAAY,CAACM,UAFc;AAGvCC,gBAAAA,WAAW,EAAEP,YAAY,CAACO,WAHa;AAIvCC,gBAAAA,MAAM,EAAER,YAAY,CAACQ,MAJkB;AAKvCC,gBAAAA,WAAW,EAAET,YAAY,CAACS,WALa;AAMvCpB,gBAAAA,QAAQ,EAAEW,YAAY,CAACX,QANgB;AAOvCE,gBAAAA,YAAY,EAAES,YAAY,CAACT,YAPY;AAQvCxC,gBAAAA,OAAO,EAAEA,OAR8B,CAQrB;;AARqB,eAA3C;AAWA;AAAA;AAAA,gCAAKC,GAAL,CAASC,WAAT,CAAqB,oCAArB,EAA2D;AACvDoD,gBAAAA,UAAU,EAAEL,YAAY,CAACK,UAD8B;AAEvDC,gBAAAA,UAAU,EAAEN,YAAY,CAACM;AAF8B,eAA3D;AAKA,kBAAM9D,GAAG,SAAS;AAAA;AAAA,8BAAIyB,GAAJ,CAAQC,MAAR,CAAeC,OAAf,kBAAwCiC,gBAAxC,CAAlB;;AAEA,kBAAI5D,GAAG,CAAC4B,MAAR,EAAgB;AACZ,oBAAMsC,KAAK,GAAGlE,GAAG,CAACiB,GAAlB;AACA;AAAA;AAAA,kCAAKT,GAAL,CAASC,WAAT,CAAqB,oBAArB,EAA2C;AACvC0D,kBAAAA,KAAK,EAAED,KAAK,CAACC,KAD0B;AAEvCtE,kBAAAA,QAAQ,EAAEqE,KAAK,CAACrE,QAFuB;AAGvCuE,kBAAAA,SAAS,EAAEF,KAAK,CAACE;AAHsB,iBAA3C,EAFY,CAQZ;;AACArG,gBAAAA,aAAa,CAACY,uBAAd,GAAwC;AACpCyC,kBAAAA,OAAO,EAAE,IAD2B;AAEpCgD,kBAAAA,SAAS,EAAEF,KAAK,CAACE,SAFmB;AAGpCC,kBAAAA,QAAQ,EAAE;AACNF,oBAAAA,KAAK,EAAED,KAAK,CAACC,KADP;AAENtE,oBAAAA,QAAQ,EAAEqE,KAAK,CAACrE,QAFV;AAGNgE,oBAAAA,UAAU,EAAEL,YAAY,CAACK,UAHnB;AAINC,oBAAAA,UAAU,EAAEN,YAAY,CAACM;AAJnB;AAH0B,iBAAxC,CATY,CAoBZ;;AACA,oBAAII,KAAK,CAACI,UAAV,EAAsB;AAClB;AAAA;AAAA,oCAAK3D,OAAL,CAAakB,GAAb,CAAiB,WAAjB,EAA8BqC,KAAK,CAACI,UAApC;AACA;AAAA;AAAA,oCAAK9D,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH;;AAED;AAAA;AAAA,kCAAKD,GAAL,CAASC,WAAT,4BAAmCyD,KAAK,CAACE,SAAN,GAAkB,GAAlB,GAAwB,GAA3D;AACA,uBAAO,IAAP;AACH,eA5BD,MA4BO;AACH,oBAAMG,SAAS,GAAG;AACdtC,kBAAAA,OAAO,EAAEjC,GAAG,CAACgC,GAAJ,CAAQC,OAAR,IAAmB,eADd;AAEduC,kBAAAA,IAAI,EAAExE,GAAG,CAACgC,GAAJ,CAAQwC,IAAR,IAAgB;AAFR,iBAAlB;AAKA;AAAA;AAAA,kCAAKhE,GAAL,CAASiE,QAAT,CAAkB,iBAAlB,EAAqCF,SAArC,EANG,CAQH;;AACAxG,gBAAAA,aAAa,CAACY,uBAAd,GAAwC;AACpCyC,kBAAAA,OAAO,EAAE,KAD2B;AAEpCsD,kBAAAA,KAAK,EAAEH;AAF6B,iBAAxC;AAKA,uBAAO,KAAP;AACH;AACJ,aA1ED,CA0EE,OAAOG,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKlE,GAAL,CAASiE,QAAT,CAAkB,kBAAlB,EAAsCC,KAAtC,EADY,CAGZ;;AACA3G,cAAAA,aAAa,CAACY,uBAAd,GAAwC;AACpCyC,gBAAAA,OAAO,EAAE,KAD2B;AAEpCsD,gBAAAA,KAAK,EAAEA;AAF6B,eAAxC;AAKA,qBAAO,KAAP;AACH;AAvF4C;AAwFhD;;AA9U6C,O,UAgC/B/F,uB,GAA+B,I", "sourcesContent": ["import { _decorator, EditBox, Node, sys } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { PlatformUtil } from '../../../../../extensions/oops-plugin-framework/assets/core/utils/PlatformUtil';\nimport { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { CCVMParentComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCVMParentComp';\nimport { JustAuthPlatformType } from '../../../tsrpc/protocols/base';\nimport { CommonTools } from '../../../tsrpc/protocols/commonTools';\nimport { ReqFacebookLogin } from '../../../tsrpc/protocols/gate/PtlFacebookLogin';\nimport { ReqRegister } from '../../../tsrpc/protocols/gate/PtlRegister';\nimport { ClientConfig } from '../../common/ClientConfig';\nimport { GameStorageConfig } from '../../common/config/GameStorageConfig';\nimport { PromptManager } from '../../common/prompt/PromptManager';\nimport { smc } from '../../common/SingletonModuleComp';\nimport { LastLogin } from '../bll/Login';\n\nconst { ccclass, property } = _decorator;\nexport interface LoginData {\n    userName: string;\n    passWord: string;\n    isGuest: boolean;\n    httpUrl: string;\n}\nexport interface registerArgs {\n    userName?: string;\n    passWord?: string;\n    isGuest: boolean;\n}\n\nexport interface LoginCallbacks {\n    onRemoved?: () => void;\n    onNewUserLogin?: () => Promise<void>;\n    onOldUserLogin?: () => Promise<void>;\n}\n\n// 登录界面和注册界面切换\n@ccclass('LoginViewComp')\**************('LoginView', false)\nexport class LoginViewComp extends CCVMParentComp {\n    /** VM 组件绑定数据 */\n    data: any = {\n        isRegister: 0, //显示注册\n        loginFail: 0, // 登录失败\n        isSafeAccount: 1, // 账号安全\n        isSafePassword: 1, // 密码安全\n        isPasswordMatch: 1, // 密码匹配\n    };\n    @property(Node)\n    loginNode!: Node;\n\n    @property(Node)\n    registerNode!: Node;\n\n    @property({ type: EditBox })\n    private loginAccountEBox!: EditBox;\n    @property({ type: EditBox })\n    private loginPasswordEBox!: EditBox;\n    @property({ type: EditBox })\n    private regAccountEBox!: EditBox;\n    @property({ type: EditBox })\n    private regPasswordEBox!: EditBox;\n    @property({ type: EditBox })\n    private regPasswordAgainEBox!: EditBox;\n\n    // 上次登录信息\n    private lastLogin?: LastLogin;\n    // 回调函数\n    private callbacks?: LoginCallbacks;\n\n    // 静态变量存储最后一次Facebook登录的结果\n    private static lastFacebookLoginResult: any = null;\n\n    /**\n     * 获取最后一次Facebook登录的结果（供InitRes使用）\n     */\n    static getLastFacebookLoginResult(): any {\n        return LoginViewComp.lastFacebookLoginResult;\n    }\n\n    onAdded(data: { lastLogin?: LastLogin; callbacks?: LoginCallbacks }) {\n        this.lastLogin = data?.lastLogin;\n        this.callbacks = data?.callbacks;\n    }\n    reset(): void {\n        oops.gui.removeByNode(this.node);\n    }\n\n    onLoad(): void {\n        super.onLoad();\n        this.setButton(false);\n    }\n    protected start(): void {\n        this.regAccountEBox.node.on('editing-did-ended', this.checkAccountEdit, this);\n        this.regAccountEBox.node.on('editing-did-change', this.checkAccountEdit, this);\n        this.regPasswordEBox.node.on('editing-did-ended', this.checkPasswordEdit, this);\n        this.regPasswordEBox.node.on('editing-did-change', this.checkPasswordEdit, this);\n        this.regPasswordAgainEBox.node.on('editing-did-ended', this.checkPasswordMatch, this);\n        this.regPasswordAgainEBox.node.on('editing-did-change', this.checkPasswordMatch, this);\n        if (this.lastLogin) {\n            this.loginAccountEBox.string = this.lastLogin?.userName || '';\n            this.loginPasswordEBox.string = this.lastLogin?.passWord || '';\n        }\n        this.checkPasswordEdit();\n        this.checkAccountEdit();\n    }\n    private checkAccountEdit() {\n        let ret = CommonTools.checkUsername(this.regAccountEBox.string);\n        this.data.isSafeAccount = ret ? 1 : 0;\n        return ret;\n    }\n    private checkPasswordEdit() {\n        let ret = CommonTools.checkPassword(this.regPasswordEBox.string);\n        this.data.isSafePassword = ret ? 1 : 0;\n        return ret;\n    }\n\n    private checkPasswordMatch() {\n        if (this.regPasswordEBox.string !== this.regPasswordAgainEBox.string) {\n            this.data.isPasswordMatch = 0;\n            return false;\n        }\n        this.data.isPasswordMatch = 1;\n        return true;\n    }\n    googleButton1() {\n        this.googleLogin();\n    }\n    googleButton2() {\n        this.googleLogin();\n    }\n    private static async doAfterLogin(httpUrl: string) {\n        oops.log.logBusiness('🔄 开始doAfterLogin流程（纯HTTP架构）...', { httpUrl });\n\n        // 检查SSO Token是否存在\n        const ssoToken = oops.storage.get('SSO_TOKEN');\n        oops.log.logBusiness('🔑 当前SSO Token状态:', {\n            exists: !!ssoToken,\n            length: ssoToken ? ssoToken.length : 0,\n            preview: ssoToken ? ssoToken.substring(0, 8) + '...' : '无',\n        });\n\n        oops.log.logBusiness('📋 开始Role.loadData()调用（HTTP API）...');\n\n        let res = await smc.role.loadData();\n        oops.log.logBusiness('📋 Role.loadData()结果:', { success: res });\n\n        return res; // 直接返回结果\n    }\n    static async doLogin(loginData: LoginData) {\n        let reqLoginData = {\n            server: loginData.httpUrl as string,\n            userName: loginData.userName,\n            passWord: loginData.passWord,\n        };\n        const ret = await smc.net.hcGate.callApi(`Login`, reqLoginData);\n        if (ret.isSucc) {\n            let res = await LoginViewComp.doAfterLogin(loginData.httpUrl);\n            if (res) {\n                // 设置本地存储登录数据\n                oops.storage.set(GameStorageConfig.UserDumpKey, reqLoginData);\n            }\n            return res;\n        } else {\n            oops.gui.toast(ret.err.message, true);\n            return false;\n        }\n    }\n\n    async loginButton() {\n        if (this.loginAccountEBox.string.length == 0 || this.loginPasswordEBox.string.length == 0) {\n            oops.gui.toast('login_empty', true);\n            return false;\n        }\n\n        // 获取服务器配置\n        const url = smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';\n\n        let ret = await LoginViewComp.doLogin({\n            userName: this.loginAccountEBox.string,\n            passWord: this.loginPasswordEBox.string,\n            isGuest: false,\n            httpUrl: url,\n        });\n\n        if (ret) {\n            oops.log.logBusiness('Old user manual login success');\n            await this.callbacks?.onOldUserLogin?.();\n            this.reset();\n        }\n        return ret;\n    }\n\n    forgetPasswordButton() {}\n\n    async loginGuestButton() {\n        let res = await this.register({ isGuest: true });\n        if (res) {\n            // 获取服务器配置\n            const httpUrl = smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';\n\n            let ret = await LoginViewComp.doLogin({\n                userName: res.userName,\n                passWord: res.passWord,\n                isGuest: true,\n                httpUrl: httpUrl,\n            });\n\n            if (ret) {\n                oops.log.logBusiness('Guest login success (new user)');\n                await this.callbacks?.onNewUserLogin?.();\n                this.reset();\n            }\n            return ret;\n        }\n        return false;\n    }\n    private async register(data: registerArgs) {\n        let args: ReqRegister = {\n            platform: PlatformUtil.getPlateform(),\n            platformType: sys.platform,\n            isGuest: data.isGuest || false,\n            userName: data.userName,\n            passWord: data.passWord,\n        };\n        const retRegister = await smc.net.hcGate.callApi(`Register`, args);\n        if (retRegister.isSucc) {\n            return retRegister.res;\n        } else {\n            oops.gui.toast(retRegister.err.message, true);\n            return false;\n        }\n    }\n    async confirmRegButton() {\n        if (this.checkAccountEdit() && this.checkPasswordEdit() && this.checkPasswordMatch()) {\n            let ret = await this.register({\n                userName: this.regAccountEBox.string,\n                passWord: this.regPasswordEBox.string,\n                isGuest: false,\n            });\n\n            if (ret) {\n                PromptManager.instance.confirm(7, async () => {\n                    // 获取服务器配置\n                    const httpUrl =\n                        smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';\n\n                    let res = await LoginViewComp.doLogin({\n                        userName: ret.userName,\n                        passWord: ret.passWord,\n                        isGuest: false,\n                        httpUrl: httpUrl,\n                    });\n\n                    if (res) {\n                        oops.log.logBusiness('Register user login success (new user)');\n                        await this.callbacks?.onNewUserLogin?.();\n                        this.reset();\n                    }\n                });\n            }\n        }\n        return;\n    }\n    private async googleLogin() {\n        let ret = await this.justAuthByPlatformType(JustAuthPlatformType.google);\n        return ret;\n    }\n    private async justAuthByPlatformType(type: JustAuthPlatformType) {\n        // 🎯 纯HTTP架构：第三方登录暂不支持，返回false\n        oops.log.logBusiness('⚠️ 纯HTTP架构：第三方登录功能暂不支持');\n        oops.gui.toast('第三方登录功能暂不支持', true);\n        return false;\n    }\n\n    private async connectGateWs() {\n        // 🎯 纯HTTP架构：WebSocket连接已移除\n        oops.log.logBusiness('⚠️ 纯HTTP架构：WebSocket连接已移除');\n        return false;\n    }\n\n    /**\n     * Facebook自动登录 - 纯HTTP版本\n     * 在Facebook环境中自动使用Facebook ID登录，返回服务端的用户信息\n     */\n    static async doFacebookLogin(): Promise<boolean> {\n        oops.log.logBusiness('🎮 开始Facebook登录（纯HTTP架构）...');\n\n        try {\n            // 获取Facebook登录数据\n            const facebookData = smc.fbInstantManager.getFacebookLoginData();\n            if (!facebookData) {\n                oops.log.logWarn('⚠️ 无法获取Facebook登录数据');\n                return false;\n            }\n\n            // 🔒 使用纯HTTP API进行Facebook登录\n            const httpUrl = ClientConfig.httpUrl;\n\n            // 准备Facebook登录请求\n            const reqFacebookLogin: ReqFacebookLogin = {\n                facebookId: facebookData.facebookId,\n                playerName: facebookData.playerName,\n                playerPhoto: facebookData.playerPhoto,\n                locale: facebookData.locale,\n                countryCode: facebookData.countryCode,\n                platform: facebookData.platform,\n                platformType: facebookData.platformType,\n                httpUrl: httpUrl, // 改为HTTP URL\n            };\n\n            oops.log.logBusiness('📤 发送Facebook登录请求到服务器（HTTP API）...', {\n                facebookId: facebookData.facebookId,\n                playerName: facebookData.playerName,\n            });\n\n            const ret = await smc.net.hcGate.callApi(`FacebookLogin`, reqFacebookLogin);\n\n            if (ret.isSucc) {\n                const fbRes = ret.res as any;\n                oops.log.logBusiness('✅ Facebook登录API成功:', {\n                    guuid: fbRes.guuid,\n                    userName: fbRes.userName,\n                    isNewUser: fbRes.isNewUser,\n                });\n\n                // 🔑 缓存登录结果供后续使用\n                LoginViewComp.lastFacebookLoginResult = {\n                    success: true,\n                    isNewUser: fbRes.isNewUser,\n                    userData: {\n                        guuid: fbRes.guuid,\n                        userName: fbRes.userName,\n                        facebookId: facebookData.facebookId,\n                        playerName: facebookData.playerName,\n                    },\n                };\n\n                // 存储SSO Token（如果需要）\n                if (fbRes.__ssoToken) {\n                    oops.storage.set('SSO_TOKEN', fbRes.__ssoToken);\n                    oops.log.logBusiness('🔑 SSO Token已存储');\n                }\n\n                oops.log.logBusiness(`🎉 Facebook${fbRes.isNewUser ? '新' : '老'}用户登录成功！`);\n                return true;\n            } else {\n                const errorInfo = {\n                    message: ret.err.message || 'Unknown error',\n                    code: ret.err.code || 'Unknown code',\n                };\n\n                oops.log.logError('❌ Facebook登录失败:', errorInfo);\n\n                // 缓存失败结果\n                LoginViewComp.lastFacebookLoginResult = {\n                    success: false,\n                    error: errorInfo,\n                };\n\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('💥 Facebook登录异常:', error);\n\n            // 缓存异常结果\n            LoginViewComp.lastFacebookLoginResult = {\n                success: false,\n                error: error,\n            };\n\n            return false;\n        }\n    }\n}\n"]}