"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ApiGameUpdateSimpleData;
const User_1 = require("../../../module/account/bll/User");
const base_1 = require("../../../tsrpc/protocols/base");
async function ApiGameUpdateSimpleData(call) {
    const dbUser = call.dbUser;
    if (!dbUser) {
        return call.error('用户未找到', { code: 'USER_NOT_FOUND' });
    }
    try {
        const { isNewPlayer } = call.req;
        // 如果设置了新手状态为false，则更新用户数据
        if (isNewPlayer === false && dbUser.isNewPlayer === true) {
            // 更新数据库中的用户状态
            dbUser.isNewPlayer = false;
            await User_1.User.updateUserData(dbUser._id, { isNewPlayer: false });
            console.log(`[GameUpdateSimpleData] 用户 ${dbUser.key} 完成新手引导`);
            // 返回基础信息更新响应
            return call.succ({
                code: 0,
                message: "更新成功",
                updateType: base_1.DataUpdateType.BASIC_INFO,
                timestamp: Date.now(),
                changes: {
                    isNewPlayer: false,
                    lastStep: dbUser.lastStep
                }
            });
        }
        // 如果没有实际更新，返回成功但不包含数据
        return call.succ({
            code: 0,
            message: "无需更新",
            updateType: base_1.DataUpdateType.BASIC_INFO,
            timestamp: Date.now(),
            changes: {} // 空的changes对象表示无变化
        });
    }
    catch (error) {
        console.error('[GameUpdateSimpleData] 更新失败:', error);
        return call.error('更新失败', { code: 'UPDATE_FAILED' });
    }
}
