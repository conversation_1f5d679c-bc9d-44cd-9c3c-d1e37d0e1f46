"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceProto = void 0;
exports.serviceProto = {
    "version": 61,
    "services": [
        {
            "id": 3,
            "name": "admin/Auth",
            "type": "api",
            "conf": {
                "needCheckAddress": true
            }
        },
        {
            "id": 1,
            "name": "admin/Logined",
            "type": "api",
            "conf": {
                "needCheckAddress": true
            }
        },
        {
            "id": 15,
            "name": "GameGenTestRankData",
            "type": "api"
        },
        {
            "id": 8,
            "name": "GameGetRank",
            "type": "api",
            "conf": {
                "needLogin": true
            }
        },
        {
            "id": 11,
            "name": "GameUpdateSimpleData",
            "type": "api",
            "conf": {
                "needLogin": true
            }
        },
        {
            "id": 13,
            "name": "UpdateProgress",
            "type": "api",
            "conf": {
                "needLogin": true
            }
        },
        {
            "id": 14,
            "name": "UpdateProp",
            "type": "api",
            "conf": {
                "needLogin": true
            }
        },
        {
            "id": 10,
            "name": "UserInfo",
            "type": "api",
            "conf": {
                "needLogin": true
            }
        }
    ],
    "types": {
        "admin/PtlAuth/ReqAuth": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "type",
                    "type": {
                        "type": "Literal",
                        "literal": "GateServer"
                    }
                }
            ]
        },
        "admin/PtlAuth/ResAuth": {
            "type": "Interface"
        },
        "admin/PtlLogined/ReqLogined": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseRequest"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "user",
                    "type": {
                        "type": "Reference",
                        "target": "../../../module/account/bll/User/DbUser"
                    }
                }
            ]
        },
        "../base/BaseRequest": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "__ssoToken",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "../../../module/account/bll/User/DbUser": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../../../tsrpc/protocols/base/UserGameData"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "_id",
                    "type": {
                        "type": "Reference",
                        "target": "?mongodb/ObjectId"
                    }
                },
                {
                    "id": 1,
                    "name": "passWord",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "platformJsonMap",
                    "type": {
                        "type": "Interface",
                        "indexSignature": {
                            "keyType": "String",
                            "type": {
                                "type": "String"
                            }
                        }
                    },
                    "optional": true
                }
            ]
        },
        "../../../tsrpc/protocols/base/UserGameData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "key",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 22,
                    "name": "guuid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 23,
                    "name": "googleUuid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 25,
                    "name": "facebookId",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 1,
                    "name": "userName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 10,
                    "name": "nickName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 11,
                    "name": "sex",
                    "type": {
                        "type": "Reference",
                        "target": "../../../tsrpc/protocols/base/SexType"
                    }
                },
                {
                    "id": 2,
                    "name": "createtime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 3,
                    "name": "openid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 16,
                    "name": "platform",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 17,
                    "name": "platformType",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 4,
                    "name": "avatar",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 12,
                    "name": "avatarId",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 13,
                    "name": "countryCode",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 8,
                    "name": "passTimes",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 5,
                    "name": "index",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 14,
                    "name": "currCountryPassTimes",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 15,
                    "name": "lastChangeCountryTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 21,
                    "name": "selfCountryRank",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 6,
                    "name": "propUseData",
                    "type": {
                        "type": "Interface",
                        "indexSignature": {
                            "keyType": "Number",
                            "type": {
                                "type": "Reference",
                                "target": "../../../tsrpc/protocols/base/PropTypeData"
                            }
                        }
                    }
                },
                {
                    "id": 7,
                    "name": "recordData",
                    "type": {
                        "type": "Reference",
                        "target": "../../../tsrpc/protocols/base/RecordData"
                    }
                },
                {
                    "id": 19,
                    "name": "isNewPlayer",
                    "type": {
                        "type": "Boolean"
                    }
                },
                {
                    "id": 24,
                    "name": "isGuest",
                    "type": {
                        "type": "Boolean"
                    }
                },
                {
                    "id": 20,
                    "name": "lastStep",
                    "type": {
                        "type": "Number"
                    }
                }
            ]
        },
        "../../../tsrpc/protocols/base/SexType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": 1
                },
                {
                    "id": 1,
                    "value": 2
                },
                {
                    "id": 2,
                    "value": 3
                }
            ]
        },
        "../../../tsrpc/protocols/base/PropTypeData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 4,
                    "name": "lastResetTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 5,
                    "name": "getTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 6,
                    "name": "lastUpdateTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 0,
                    "name": "propId",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 1,
                    "name": "propType",
                    "type": {
                        "type": "Reference",
                        "target": "../../../tsrpc/protocols/base/PropType"
                    }
                },
                {
                    "id": 2,
                    "name": "amount",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 3,
                    "name": "desc",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "../../../tsrpc/protocols/base/PropType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": 0
                },
                {
                    "id": 1,
                    "value": 1
                },
                {
                    "id": 2,
                    "value": 2
                },
                {
                    "id": 3,
                    "value": 3
                },
                {
                    "id": 4,
                    "value": 4
                },
                {
                    "id": 5,
                    "value": 5
                },
                {
                    "id": 6,
                    "value": 6
                },
                {
                    "id": 7,
                    "value": 7
                }
            ]
        },
        "../../../tsrpc/protocols/base/RecordData": {
            "type": "Interface",
            "indexSignature": {
                "keyType": "String",
                "type": {
                    "type": "Interface",
                    "indexSignature": {
                        "keyType": "Number",
                        "type": {
                            "type": "Reference",
                            "target": "../../../tsrpc/protocols/base/RecordTypeData"
                        }
                    }
                }
            }
        },
        "../../../tsrpc/protocols/base/RecordTypeData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "recordType",
                    "type": {
                        "type": "Reference",
                        "target": "../../../tsrpc/protocols/base/RecordType"
                    }
                },
                {
                    "id": 1,
                    "name": "value",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "lastUpdateTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 3,
                    "name": "passTime",
                    "type": {
                        "type": "Date"
                    },
                    "optional": true
                },
                {
                    "id": 4,
                    "name": "createTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 5,
                    "name": "levelDetails",
                    "type": {
                        "type": "Interface",
                        "indexSignature": {
                            "keyType": "String",
                            "type": {
                                "type": "Reference",
                                "target": "../../../tsrpc/protocols/base/LevelChallengeData"
                            }
                        }
                    },
                    "optional": true
                }
            ]
        },
        "../../../tsrpc/protocols/base/RecordType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": 0
                },
                {
                    "id": 1,
                    "value": 1
                },
                {
                    "id": 2,
                    "value": 2
                }
            ]
        },
        "../../../tsrpc/protocols/base/LevelChallengeData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "levelId",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 1,
                    "name": "attempts",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "lastAttemptTime",
                    "type": {
                        "type": "Date"
                    }
                }
            ]
        },
        "admin/PtlLogined/ResLogined": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseResponse"
                    }
                }
            ]
        },
        "../base/BaseResponse": {
            "type": "Interface",
            "properties": [
                {
                    "id": 1,
                    "name": "code",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "message",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 0,
                    "name": "__ssoToken",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "PtlGameGenTestRankData/ReqGameGenTestRankData": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseRequest"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "rankType",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 1,
                    "name": "identifier",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "count",
                    "type": {
                        "type": "Number"
                    },
                    "optional": true
                }
            ]
        },
        "PtlGameGenTestRankData/ResGameGenTestRankData": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "generatedCount",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 1,
                    "name": "rankType",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "identifier",
                    "type": {
                        "type": "String"
                    }
                }
            ]
        },
        "PtlGameGetRank/ReqGameGetRank": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseUserRequest"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "rankType",
                    "type": {
                        "type": "Reference",
                        "target": "../base/RankType"
                    }
                }
            ]
        },
        "../base/BaseUserRequest": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseRequest"
                    }
                }
            ]
        },
        "../base/RankType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": 0
                },
                {
                    "id": 1,
                    "value": 1
                },
                {
                    "id": 2,
                    "value": 2
                },
                {
                    "id": 3,
                    "value": 3
                },
                {
                    "id": 4,
                    "value": 4
                }
            ]
        },
        "PtlGameGetRank/ResGameGetRank": {
            "type": "Interface",
            "extends": [
                {
                    "id": 1,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "rankType",
                    "type": {
                        "type": "Reference",
                        "target": "../base/RankType"
                    }
                },
                {
                    "id": 5,
                    "name": "selfRankData",
                    "type": {
                        "type": "Reference",
                        "target": "../base/RankRecord"
                    },
                    "optional": true
                },
                {
                    "id": 6,
                    "name": "rankTypeData",
                    "type": {
                        "type": "Array",
                        "elementType": {
                            "type": "Reference",
                            "target": "../base/RankRecord"
                        }
                    },
                    "optional": true
                }
            ]
        },
        "../base/RankRecord": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "id",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 8,
                    "name": "name",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 4,
                    "name": "avatar",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 5,
                    "name": "avatarId",
                    "type": {
                        "type": "Number"
                    },
                    "optional": true
                },
                {
                    "id": 1,
                    "name": "score",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "expireAt",
                    "type": {
                        "type": "Number"
                    },
                    "optional": true
                },
                {
                    "id": 6,
                    "name": "updateTime",
                    "type": {
                        "type": "Date"
                    },
                    "optional": true
                },
                {
                    "id": 7,
                    "name": "rank",
                    "type": {
                        "type": "Number"
                    },
                    "optional": true
                }
            ]
        },
        "PtlGameUpdateSimpleData/ReqGameUpdateSimpleData": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseUserRequest"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "isNewPlayer",
                    "type": {
                        "type": "Boolean"
                    },
                    "optional": true
                }
            ]
        },
        "PtlGameUpdateSimpleData/ResGameUpdateSimpleData": {
            "type": "Interface",
            "extends": [
                {
                    "id": 2,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BasicInfoUpdateResponse"
                    }
                }
            ]
        },
        "../base/BasicInfoUpdateResponse": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseDataUpdateResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "updateType",
                    "type": {
                        "type": "Literal",
                        "literal": "BASIC_INFO"
                    }
                },
                {
                    "id": 1,
                    "name": "changes",
                    "type": {
                        "type": "Interface",
                        "properties": [
                            {
                                "id": 0,
                                "name": "nickName",
                                "type": {
                                    "type": "String"
                                },
                                "optional": true
                            },
                            {
                                "id": 1,
                                "name": "avatarId",
                                "type": {
                                    "type": "Number"
                                },
                                "optional": true
                            },
                            {
                                "id": 2,
                                "name": "countryCode",
                                "type": {
                                    "type": "String"
                                },
                                "optional": true
                            },
                            {
                                "id": 3,
                                "name": "isNewPlayer",
                                "type": {
                                    "type": "Boolean"
                                },
                                "optional": true
                            },
                            {
                                "id": 4,
                                "name": "lastStep",
                                "type": {
                                    "type": "Number"
                                },
                                "optional": true
                            }
                        ]
                    }
                }
            ]
        },
        "../base/BaseDataUpdateResponse": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "updateType",
                    "type": {
                        "type": "Reference",
                        "target": "../base/DataUpdateType"
                    }
                },
                {
                    "id": 1,
                    "name": "timestamp",
                    "type": {
                        "type": "Number"
                    }
                }
            ]
        },
        "../base/DataUpdateType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": "PROP"
                },
                {
                    "id": 1,
                    "value": "PROGRESS"
                },
                {
                    "id": 2,
                    "value": "BASIC_INFO"
                },
                {
                    "id": 3,
                    "value": "RECORD"
                },
                {
                    "id": 4,
                    "value": "FULL"
                }
            ]
        },
        "PtlUpdateProgress/ReqUpdateProgress": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseUserRequest"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "index",
                    "type": {
                        "type": "Number"
                    },
                    "optional": true
                },
                {
                    "id": 1,
                    "name": "passTimesIncrement",
                    "type": {
                        "type": "Number"
                    },
                    "optional": true
                },
                {
                    "id": 2,
                    "name": "isGm",
                    "type": {
                        "type": "Boolean"
                    },
                    "optional": true
                }
            ]
        },
        "PtlUpdateProgress/ResUpdateProgress": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/ProgressUpdateResponse"
                    }
                }
            ]
        },
        "../base/ProgressUpdateResponse": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseDataUpdateResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "updateType",
                    "type": {
                        "type": "Literal",
                        "literal": "PROGRESS"
                    }
                },
                {
                    "id": 1,
                    "name": "changes",
                    "type": {
                        "type": "Interface",
                        "properties": [
                            {
                                "id": 0,
                                "name": "level",
                                "type": {
                                    "type": "Number"
                                },
                                "optional": true
                            },
                            {
                                "id": 1,
                                "name": "index",
                                "type": {
                                    "type": "Number"
                                },
                                "optional": true
                            },
                            {
                                "id": 2,
                                "name": "passTimes",
                                "type": {
                                    "type": "Number"
                                },
                                "optional": true
                            },
                            {
                                "id": 3,
                                "name": "currCountryPassTimes",
                                "type": {
                                    "type": "Number"
                                },
                                "optional": true
                            },
                            {
                                "id": 4,
                                "name": "selfCountryRank",
                                "type": {
                                    "type": "Number"
                                },
                                "optional": true
                            }
                        ]
                    }
                }
            ]
        },
        "PtlUpdateProp/ReqUpdateProp": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseUserRequest"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "propType",
                    "type": {
                        "type": "Reference",
                        "target": "../base/PropType"
                    }
                },
                {
                    "id": 1,
                    "name": "amount",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "reason",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "../base/PropType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": 0
                },
                {
                    "id": 1,
                    "value": 1
                },
                {
                    "id": 2,
                    "value": 2
                },
                {
                    "id": 3,
                    "value": 3
                },
                {
                    "id": 4,
                    "value": 4
                },
                {
                    "id": 5,
                    "value": 5
                },
                {
                    "id": 6,
                    "value": 6
                },
                {
                    "id": 7,
                    "value": 7
                }
            ]
        },
        "PtlUpdateProp/ResUpdateProp": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/PropUpdateResponse"
                    }
                }
            ]
        },
        "../base/PropUpdateResponse": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseDataUpdateResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "updateType",
                    "type": {
                        "type": "Literal",
                        "literal": "PROP"
                    }
                },
                {
                    "id": 1,
                    "name": "changes",
                    "type": {
                        "type": "Interface",
                        "properties": [
                            {
                                "id": 0,
                                "name": "propType",
                                "type": {
                                    "type": "Reference",
                                    "target": "../base/PropType"
                                }
                            },
                            {
                                "id": 1,
                                "name": "newAmount",
                                "type": {
                                    "type": "Number"
                                }
                            },
                            {
                                "id": 2,
                                "name": "changeAmount",
                                "type": {
                                    "type": "Number"
                                }
                            },
                            {
                                "id": 3,
                                "name": "lastUpdateTime",
                                "type": {
                                    "type": "Date"
                                }
                            }
                        ]
                    }
                }
            ]
        },
        "PtlUserInfo/ReqUserInfo": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseUserRequest"
                    }
                }
            ]
        },
        "PtlUserInfo/ResUserInfo": {
            "type": "Interface",
            "extends": [
                {
                    "id": 1,
                    "type": {
                        "type": "Reference",
                        "target": "../base/FullDataResponse"
                    }
                }
            ]
        },
        "../base/FullDataResponse": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseDataUpdateResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "updateType",
                    "type": {
                        "type": "Literal",
                        "literal": "FULL"
                    }
                },
                {
                    "id": 1,
                    "name": "userGameData",
                    "type": {
                        "type": "Reference",
                        "target": "../base/UserGameData"
                    }
                }
            ]
        },
        "../base/UserGameData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "key",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 30,
                    "name": "guuid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 31,
                    "name": "googleUuid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 33,
                    "name": "facebookId",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 10,
                    "name": "userName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 18,
                    "name": "nickName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 19,
                    "name": "sex",
                    "type": {
                        "type": "Reference",
                        "target": "../base/SexType"
                    }
                },
                {
                    "id": 12,
                    "name": "createtime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 13,
                    "name": "openid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 24,
                    "name": "platform",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 25,
                    "name": "platformType",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "avatar",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 20,
                    "name": "avatarId",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 21,
                    "name": "countryCode",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 16,
                    "name": "passTimes",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 14,
                    "name": "index",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 22,
                    "name": "currCountryPassTimes",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 23,
                    "name": "lastChangeCountryTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 29,
                    "name": "selfCountryRank",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 11,
                    "name": "propUseData",
                    "type": {
                        "type": "Interface",
                        "indexSignature": {
                            "keyType": "Number",
                            "type": {
                                "type": "Reference",
                                "target": "../base/PropTypeData"
                            }
                        }
                    }
                },
                {
                    "id": 15,
                    "name": "recordData",
                    "type": {
                        "type": "Reference",
                        "target": "../base/RecordData"
                    }
                },
                {
                    "id": 27,
                    "name": "isNewPlayer",
                    "type": {
                        "type": "Boolean"
                    }
                },
                {
                    "id": 32,
                    "name": "isGuest",
                    "type": {
                        "type": "Boolean"
                    }
                },
                {
                    "id": 28,
                    "name": "lastStep",
                    "type": {
                        "type": "Number"
                    }
                }
            ]
        },
        "../base/SexType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": 1
                },
                {
                    "id": 1,
                    "value": 2
                },
                {
                    "id": 2,
                    "value": 3
                }
            ]
        },
        "../base/PropTypeData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 4,
                    "name": "lastResetTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 5,
                    "name": "getTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 6,
                    "name": "lastUpdateTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 0,
                    "name": "propId",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 1,
                    "name": "propType",
                    "type": {
                        "type": "Reference",
                        "target": "../base/PropType"
                    }
                },
                {
                    "id": 2,
                    "name": "amount",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 3,
                    "name": "desc",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "../base/RecordData": {
            "type": "Interface",
            "indexSignature": {
                "keyType": "String",
                "type": {
                    "type": "Interface",
                    "indexSignature": {
                        "keyType": "Number",
                        "type": {
                            "type": "Reference",
                            "target": "../base/RecordTypeData"
                        }
                    }
                }
            }
        },
        "../base/RecordTypeData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "recordType",
                    "type": {
                        "type": "Reference",
                        "target": "../base/RecordType"
                    }
                },
                {
                    "id": 1,
                    "name": "value",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "lastUpdateTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 3,
                    "name": "passTime",
                    "type": {
                        "type": "Date"
                    },
                    "optional": true
                },
                {
                    "id": 4,
                    "name": "createTime",
                    "type": {
                        "type": "Date"
                    }
                },
                {
                    "id": 5,
                    "name": "levelDetails",
                    "type": {
                        "type": "Interface",
                        "indexSignature": {
                            "keyType": "String",
                            "type": {
                                "type": "Reference",
                                "target": "../base/LevelChallengeData"
                            }
                        }
                    },
                    "optional": true
                }
            ]
        },
        "../base/RecordType": {
            "type": "Enum",
            "members": [
                {
                    "id": 0,
                    "value": 0
                },
                {
                    "id": 1,
                    "value": 1
                },
                {
                    "id": 2,
                    "value": 2
                }
            ]
        },
        "../base/LevelChallengeData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "levelId",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 1,
                    "name": "attempts",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "lastAttemptTime",
                    "type": {
                        "type": "Date"
                    }
                }
            ]
        }
    }
};
