"use strict";
/*
 * Redis会话管理器 - 优化的会话管理，支持分布式部署
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisSessionManager = void 0;
const chalk_1 = __importDefault(require("chalk"));
const uuid_1 = require("uuid");
const RedisManager_1 = require("./RedisManager");
class RedisSessionManager {
    /**
     * 初始化会话管理器
     */
    static async init() {
        try {
            console.log(chalk_1.default.cyan('📝 初始化Redis会话管理器...'));
            // 启动定期清理
            this.startCleanupTimer();
            console.log(chalk_1.default.green('✅ Redis会话管理器初始化完成'));
            console.log(chalk_1.default.gray(`📋 配置: 过期时间=${this.config.defaultExpiration}s, 空闲时间=${this.config.maxIdleTime}s`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Redis会话管理器初始化失败:'), error);
            throw error;
        }
    }
    /**
     * 创建会话
     */
    static async createSession(user, metadata) {
        try {
            const token = (0, uuid_1.v4)();
            const now = Date.now();
            const sessionData = {
                user: user,
                createdTime: now,
                lastAccessTime: now,
                lastRefreshTime: now,
                expiredTime: now + this.config.defaultExpiration * 1000,
                refreshCount: 0,
                metadata: metadata,
            };
            // 存储会话数据
            const sessionKey = this.getSessionKey(token);
            await RedisManager_1.RedisManager.set(sessionKey, JSON.stringify(sessionData), this.config.defaultExpiration);
            // 记录用户的会话（用于多端登录管理）
            await this.addUserSession(user.guuid, token);
            console.log(chalk_1.default.green(`🔐 用户 ${user.userName} 创建会话成功，Token: ${token.substring(0, 8)}...`));
            return token;
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 创建会话失败:'), error);
            throw error;
        }
    }
    /**
     * 获取会话
     */
    static async getSession(token) {
        try {
            const sessionKey = this.getSessionKey(token);
            const sessionDataStr = await RedisManager_1.RedisManager.get(sessionKey);
            if (!sessionDataStr) {
                return null;
            }
            const sessionData = JSON.parse(sessionDataStr);
            const now = Date.now();
            // 检查是否过期
            if (sessionData.expiredTime < now) {
                await this.removeSession(token);
                return null;
            }
            // 检查空闲时间
            if (now - sessionData.lastAccessTime > this.config.maxIdleTime * 1000) {
                console.log(chalk_1.default.yellow(`⚠️  会话因空闲时间过长被清除: ${token.substring(0, 8)}...`));
                await this.removeSession(token);
                return null;
            }
            // 更新最后访问时间
            sessionData.lastAccessTime = now;
            // 🎮 智能刷新策略：避免频繁刷新
            const timeToExpire = sessionData.expiredTime - now;
            const timeSinceLastRefresh = now - sessionData.lastRefreshTime;
            let needUpdateRedis = false;
            const shouldRefresh = timeToExpire < this.config.refreshThreshold * 1000 && // 剩余时间少于阈值
                sessionData.refreshCount < this.config.maxRefreshCount && // 未达到最大刷新次数
                timeSinceLastRefresh > this.config.refreshInterval * 1000; // 距离上次刷新超过间隔
            if (shouldRefresh) {
                sessionData.expiredTime = now + this.config.defaultExpiration * 1000;
                sessionData.lastRefreshTime = now;
                sessionData.refreshCount++;
                needUpdateRedis = true;
                console.log(chalk_1.default.blue(`🎮 会话智能刷新: ${token.substring(0, 8)}... (第${sessionData.refreshCount}次)`));
                console.log(chalk_1.default.gray(`   剩余: ${Math.floor(timeToExpire / 60000)}分钟, 距上次刷新: ${Math.floor(timeSinceLastRefresh / 60000)}分钟`));
            }
            // 🔧 优化：只在需要时更新Redis（避免每次访问都写Redis）
            // 注意：用户数据的更新由数据同步中间件负责，这里只更新访问时间和过期时间
            const timeSinceLastAccess = now - sessionData.lastAccessTime;
            if (needUpdateRedis || timeSinceLastAccess > 60000) {
                // 超过1分钟才更新访问时间
                await RedisManager_1.RedisManager.set(sessionKey, JSON.stringify(sessionData), Math.floor((sessionData.expiredTime - now) / 1000));
            }
            return sessionData.user;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 获取会话失败 ${token}:`), error);
            return null;
        }
    }
    /**
     * 更新会话中的用户数据
     */
    static async updateSession(token, userData) {
        try {
            const sessionKey = this.getSessionKey(token);
            const sessionDataStr = await RedisManager_1.RedisManager.get(sessionKey);
            if (!sessionDataStr) {
                return false;
            }
            const sessionData = JSON.parse(sessionDataStr);
            const now = Date.now();
            // 检查是否过期
            if (sessionData.expiredTime < now) {
                await this.removeSession(token);
                return false;
            }
            // 更新用户数据和访问时间
            sessionData.user = userData;
            sessionData.lastAccessTime = now;
            // 保存更新后的会话数据
            await RedisManager_1.RedisManager.set(sessionKey, JSON.stringify(sessionData), Math.floor((sessionData.expiredTime - now) / 1000));
            return true;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 更新会话失败 ${token}:`), error);
            return false;
        }
    }
    /**
     * 移除会话
     */
    static async removeSession(token) {
        try {
            const sessionKey = this.getSessionKey(token);
            // 获取会话数据以便清理用户会话记录
            const sessionDataStr = await RedisManager_1.RedisManager.get(sessionKey);
            if (sessionDataStr) {
                const sessionData = JSON.parse(sessionDataStr);
                await this.removeUserSession(sessionData.user.guuid, token);
            }
            // 删除会话
            await RedisManager_1.RedisManager.del(sessionKey);
            console.log(chalk_1.default.yellow(`🔓 会话已注销: ${token.substring(0, 8)}...`));
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 移除会话失败 ${token}:`), error);
        }
    }
    /**
     * 移除用户的所有会话
     */
    static async removeUserAllSessions(userGuid) {
        try {
            const userSessionsKey = this.getUserSessionsKey(userGuid);
            const sessions = await RedisManager_1.RedisManager.get(userSessionsKey);
            if (sessions) {
                const sessionTokens = JSON.parse(sessions);
                // 删除所有会话
                for (const token of sessionTokens) {
                    await RedisManager_1.RedisManager.del(this.getSessionKey(token));
                }
                // 清空用户会话记录
                await RedisManager_1.RedisManager.del(userSessionsKey);
                console.log(chalk_1.default.yellow(`🔓 用户 ${userGuid} 的所有会话已清除 (${sessionTokens.length}个)`));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 清除用户会话失败 ${userGuid}:`), error);
        }
    }
    /**
     * 获取会话统计信息
     */
    static async getSessionStats() {
        try {
            // 获取所有会话键
            const sessionKeys = await RedisManager_1.RedisManager.getClient().keys(this.keyPrefix + '*');
            const userSessionKeys = await RedisManager_1.RedisManager.getClient().keys(this.userSessionPrefix + '*');
            // 获取Redis内存使用情况
            const memoryUsage = await RedisManager_1.RedisManager.getMemoryUsage();
            return {
                totalSessions: sessionKeys.length,
                activeUsers: userSessionKeys.length,
                memoryUsage: memoryUsage,
            };
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 获取会话统计失败:'), error);
            return {
                totalSessions: 0,
                activeUsers: 0,
                memoryUsage: null,
            };
        }
    }
    /**
     * 手动清理过期会话
     */
    static async cleanupExpiredSessions() {
        try {
            let cleanedCount = 0;
            const sessionKeys = await RedisManager_1.RedisManager.getClient().keys(this.keyPrefix + '*');
            for (const sessionKey of sessionKeys) {
                const sessionDataStr = await RedisManager_1.RedisManager.get(sessionKey);
                if (sessionDataStr) {
                    const sessionData = JSON.parse(sessionDataStr);
                    const now = Date.now();
                    // 检查是否过期或空闲时间过长
                    if (sessionData.expiredTime < now ||
                        now - sessionData.lastAccessTime > this.config.maxIdleTime * 1000) {
                        const token = sessionKey.replace(this.keyPrefix, '');
                        await this.removeSession(token);
                        cleanedCount++;
                    }
                }
            }
            if (cleanedCount > 0) {
                console.log(chalk_1.default.blue(`🧹 清理过期会话: ${cleanedCount} 个`));
            }
            return cleanedCount;
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 清理过期会话失败:'), error);
            return 0;
        }
    }
    /**
     * 更新会话配置
     */
    static updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log(chalk_1.default.blue('📝 会话配置已更新:'), newConfig);
        // 重启清理定时器
        if (newConfig.cleanupInterval) {
            this.stopCleanupTimer();
            this.startCleanupTimer();
        }
    }
    // ============ 私有方法 ============
    /**
     * 获取会话键
     */
    static getSessionKey(token) {
        return this.keyPrefix + token;
    }
    /**
     * 获取用户会话键
     */
    static getUserSessionsKey(userGuid) {
        return this.userSessionPrefix + userGuid;
    }
    /**
     * 添加用户会话记录
     */
    static async addUserSession(userGuid, token) {
        try {
            const userSessionsKey = this.getUserSessionsKey(userGuid);
            const sessionsStr = await RedisManager_1.RedisManager.get(userSessionsKey);
            let sessions = [];
            if (sessionsStr) {
                sessions = JSON.parse(sessionsStr);
            }
            // 避免重复添加
            if (!sessions.includes(token)) {
                sessions.push(token);
                await RedisManager_1.RedisManager.set(userSessionsKey, JSON.stringify(sessions), this.config.defaultExpiration);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 添加用户会话记录失败:'), error);
        }
    }
    /**
     * 移除用户会话记录
     */
    static async removeUserSession(userGuid, token) {
        try {
            const userSessionsKey = this.getUserSessionsKey(userGuid);
            const sessionsStr = await RedisManager_1.RedisManager.get(userSessionsKey);
            if (sessionsStr) {
                let sessions = JSON.parse(sessionsStr);
                sessions = sessions.filter(t => t !== token);
                if (sessions.length > 0) {
                    await RedisManager_1.RedisManager.set(userSessionsKey, JSON.stringify(sessions), this.config.defaultExpiration);
                }
                else {
                    await RedisManager_1.RedisManager.del(userSessionsKey);
                }
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 移除用户会话记录失败:'), error);
        }
    }
    /**
     * 启动清理定时器
     */
    static startCleanupTimer() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        this.cleanupTimer = setInterval(async () => {
            await this.cleanupExpiredSessions();
        }, this.config.cleanupInterval * 1000);
        console.log(chalk_1.default.gray(`⏰ 会话清理定时器启动，间隔 ${this.config.cleanupInterval} 秒`));
    }
    /**
     * 停止清理定时器
     */
    static stopCleanupTimer() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
            console.log(chalk_1.default.gray('⏰ 会话清理定时器已停止'));
        }
    }
    /**
     * 关闭会话管理器
     */
    static async close() {
        this.stopCleanupTimer();
        console.log(chalk_1.default.green('✅ Redis会话管理器已关闭'));
    }
}
exports.RedisSessionManager = RedisSessionManager;
// 🎮 三消游戏优化配置
RedisSessionManager.config = {
    defaultExpiration: 8 * 60 * 60, // 8小时基础过期时间
    maxIdleTime: 4 * 60 * 60, // 4小时空闲自动登出
    refreshThreshold: 2 * 60 * 60, // 剩余2小时时刷新
    maxRefreshCount: 2, // 最多刷新2次（总共24小时）
    refreshInterval: 4 * 60 * 60, // 4小时内不重复刷新
    cleanupInterval: 30 * 60, // 30分钟清理一次
};
RedisSessionManager.keyPrefix = 'session:';
RedisSessionManager.userSessionPrefix = 'user_sessions:';
RedisSessionManager.cleanupTimer = null;
