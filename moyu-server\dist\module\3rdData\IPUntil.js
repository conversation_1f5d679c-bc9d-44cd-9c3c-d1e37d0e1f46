"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.IPUntil = void 0;
const maxmind = __importStar(require("maxmind"));
const fs = __importStar(require("fs"));
const GameConst_1 = require("../../tsrpc/models/GameConst");
const ShareConfig_1 = require("../../tsrpc/models/ShareConfig");
class IPUntil {
    static init() {
        console.log('🔍 IPUntil.init() - 检查平台:', ShareConfig_1.ShareConfig.platform);
        console.log('🔍 IPUntil.init() - 平台类型:', typeof ShareConfig_1.ShareConfig.platform);
        // 在Facebook环境下，地理位置信息由Facebook SDK提供，无需加载GeoLite2文件
        if (ShareConfig_1.ShareConfig.platform === 'facebook') {
            console.log('🌍 Facebook环境：跳过GeoLite2数据库加载，使用Facebook SDK地理位置信息');
            this.lookupCity = null;
            this.lookupCountry = null;
            return;
        }
        let GeoCityPath = './src/module/3rdData/GeoLite2-City.mmdb';
        let GeoCountryPath = './src/module/3rdData/GeoLite2-Country.mmdb';
        // 添加错误处理，避免文件缺失时崩溃
        try {
            if (fs.existsSync(GeoCityPath)) {
                maxmind.open(GeoCityPath).then(reader => {
                    this.lookupCity = reader;
                    console.log('✅ GeoLite2-City数据库加载成功');
                }).catch(error => {
                    console.warn('⚠️ GeoLite2-City数据库文件缺失，将使用默认地理位置信息');
                    console.warn('   文件路径:', GeoCityPath);
                    this.lookupCity = null;
                });
            }
            else {
                console.warn('⚠️ GeoLite2-City数据库文件缺失，将使用默认地理位置信息');
                console.warn('   文件路径:', GeoCityPath);
                this.lookupCity = null;
            }
        }
        catch (error) {
            console.warn('⚠️ GeoLite2-City数据库文件缺失，将使用默认地理位置信息');
            console.warn('   文件路径:', GeoCityPath);
            this.lookupCity = null;
        }
        try {
            if (fs.existsSync(GeoCountryPath)) {
                maxmind.open(GeoCountryPath).then(reader => {
                    this.lookupCountry = reader;
                    console.log('✅ GeoLite2-Country数据库加载成功');
                }).catch(error => {
                    console.warn('⚠️ GeoLite2-Country数据库文件缺失，将使用默认地理位置信息');
                    console.warn('   文件路径:', GeoCountryPath);
                    this.lookupCountry = null;
                });
            }
            else {
                console.warn('⚠️ GeoLite2-Country数据库文件缺失，将使用默认地理位置信息');
                console.warn('   文件路径:', GeoCountryPath);
                this.lookupCountry = null;
            }
        }
        catch (error) {
            console.warn('⚠️ GeoLite2-Country数据库文件缺失，将使用默认地理位置信息');
            console.warn('   文件路径:', GeoCountryPath);
            this.lookupCountry = null;
        }
    }
    static getCountryCodeByIP(ip) {
        var _a, _b;
        // 检查数据库是否加载成功
        if (!this.lookupCountry) {
            console.log(`GeoLite2数据库未加载，使用默认国家代码: ${GameConst_1.GameConst.defaultCountryCode}`);
            return GameConst_1.GameConst.defaultCountryCode;
        }
        try {
            // 查找 IP 地址
            const countryData = this.lookupCountry.get(ip);
            if (countryData) {
                console.log((_a = countryData.country) === null || _a === void 0 ? void 0 : _a.iso_code);
                return ((_b = countryData.country) === null || _b === void 0 ? void 0 : _b.iso_code) || GameConst_1.GameConst.defaultCountryCode;
            }
            else {
                console.log(`未找到 IP 地址: ${ip}`);
            }
        }
        catch (error) {
            console.error(`查询IP地址失败: ${ip}`, error);
        }
        return GameConst_1.GameConst.defaultCountryCode;
    }
    /** 获取完整的地理位置信息 */
    static getLocationInfoByIP(ip) {
        var _a, _b, _c, _d, _e, _f;
        // 检查数据库是否加载成功
        if (!this.lookupCity && !this.lookupCountry) {
            console.log(`GeoLite2数据库未加载，使用默认地理位置信息`);
            return {
                countryCode: GameConst_1.GameConst.defaultCountryCode,
                countryName: '默认国家',
                cityName: '默认城市',
                cityCode: `${GameConst_1.GameConst.defaultCountryCode}_Default`
            };
        }
        try {
            // 查找城市信息
            if (this.lookupCity) {
                const cityData = this.lookupCity.get(ip);
                if (cityData && cityData.country && cityData.city) {
                    const countryCode = cityData.country.iso_code || GameConst_1.GameConst.defaultCountryCode;
                    const countryName = ((_a = cityData.country.names) === null || _a === void 0 ? void 0 : _a['zh-CN']) || ((_b = cityData.country.names) === null || _b === void 0 ? void 0 : _b.en) || '未知国家';
                    const cityName = ((_c = cityData.city.names) === null || _c === void 0 ? void 0 : _c['zh-CN']) || ((_d = cityData.city.names) === null || _d === void 0 ? void 0 : _d.en) || '未知城市';
                    // 生成城市代码：国家代码_城市名（用于排行榜标识）
                    const cityCode = `${countryCode}_${cityName.replace(/\s+/g, '_')}`;
                    return {
                        countryCode,
                        countryName,
                        cityName,
                        cityCode
                    };
                }
            }
            // 如果没有城市信息，尝试只获取国家信息
            if (this.lookupCountry) {
                const countryData = this.lookupCountry.get(ip);
                if (countryData && countryData.country) {
                    const countryCode = countryData.country.iso_code || GameConst_1.GameConst.defaultCountryCode;
                    const countryName = ((_e = countryData.country.names) === null || _e === void 0 ? void 0 : _e['zh-CN']) || ((_f = countryData.country.names) === null || _f === void 0 ? void 0 : _f.en) || '未知国家';
                    return {
                        countryCode,
                        countryName,
                        cityName: '未知城市',
                        cityCode: `${countryCode}_Unknown`
                    };
                }
            }
        }
        catch (error) {
            console.error(`获取IP地理位置信息失败: ${ip}`, error);
        }
        // 默认返回值
        return {
            countryCode: GameConst_1.GameConst.defaultCountryCode,
            countryName: '默认国家',
            cityName: '默认城市',
            cityCode: `${GameConst_1.GameConst.defaultCountryCode}_Default`
        };
    }
    /** 为了向后兼容保留的方法 */
    static getCityCodeByIP(ip) {
        return this.getLocationInfoByIP(ip).cityCode;
    }
}
exports.IPUntil = IPUntil;
