"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const Rank_1 = require("../module/rank/bll/Rank");
const MongoDB_1 = require("../module/common/MongoDB");
const RedisManager_1 = require("../module/common/RedisManager");
// 简化的国家城市数据
const COUNTRY_CITY_DATA = [
    {
        code: 'CN',
        name: '中国',
        cities: [
            { code: 'bj', name: '北京' },
            { code: 'sh', name: '上海' },
            { code: 'sz', name: '深圳' },
            { code: 'gz', name: '广州' },
            { code: 'hz', name: '杭州' },
            { code: 'cd', name: '成都' },
            { code: 'wh', name: '武汉' },
            { code: 'xa', name: '西安' }
        ]
    },
    {
        code: 'US',
        name: '美国',
        cities: [
            { code: 'nyc', name: 'New York' },
            { code: 'la', name: 'Los Angeles' },
            { code: 'chi', name: 'Chicago' },
            { code: 'hou', name: '<PERSON>' },
            { code: 'phx', name: 'Phoenix' },
            { code: 'phl', name: 'Philadelphia' }
        ]
    },
    {
        code: 'JP',
        name: '日本',
        cities: [
            { code: 'tokyo', name: '東京' },
            { code: 'osaka', name: '大阪' },
            { code: 'kyoto', name: '京都' },
            { code: 'nagoya', name: '名古屋' },
            { code: 'yokohama', name: '横浜' }
        ]
    },
    {
        code: 'KR',
        name: '韩国',
        cities: [
            { code: 'seoul', name: '서울' },
            { code: 'busan', name: '부산' },
            { code: 'incheon', name: '인천' },
            { code: 'daegu', name: '대구' }
        ]
    },
    {
        code: 'GB',
        name: '英国',
        cities: [
            { code: 'london', name: 'London' },
            { code: 'birmingham', name: 'Birmingham' },
            { code: 'manchester', name: 'Manchester' },
            { code: 'glasgow', name: 'Glasgow' }
        ]
    }
];
async function generateRealisticRanking() {
    console.log('🌍 开始生成简化的真实排行榜数据...');
    try {
        // 初始化连接
        console.log('📝 初始化数据库连接...');
        await MongoDB_1.MongoDB.init();
        console.log('📝 初始化Redis连接...');
        await RedisManager_1.RedisManager.init();
        // 等待Redis连接
        let retryCount = 0;
        while (!RedisManager_1.RedisManager.isReady() && retryCount < 5) {
            console.log(`⏳ 等待Redis连接... (${retryCount + 1}/5)`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            retryCount++;
        }
        console.log('📝 初始化排行榜系统...');
        Rank_1.Rank.init();
        console.log('\n🗑️ 清理旧数据...');
        await clearAllData();
        console.log('\n🏗️ 生成国家排行榜数据...');
        await generateCountryRankings();
        console.log('\n🏙️ 生成城市排行榜数据...');
        await generateCityRankings();
        console.log('\n🌆 聚合城市数据...');
        await aggregateCityData();
        console.log('\n🌍 聚合世界数据...');
        await aggregateWorldData();
        console.log('\n✅ 数据生成完成！');
        await verifyData();
    }
    catch (error) {
        console.error('❌ 生成失败:', error);
        throw error;
    }
}
async function clearAllData() {
    try {
        const db = MongoDB_1.MongoDB.rankDb;
        const collections = await db.collections();
        for (const collection of collections) {
            const name = collection.collectionName;
            if (name.includes('rank') || name.includes('world') ||
                name.includes('city_') || name.includes('country_cities_') ||
                /^[A-Z]{2}$/.test(name)) {
                await collection.deleteMany({});
                console.log(`🧹 清理集合: ${name}`);
            }
        }
        // 安全清理缓存
        try {
            if (RedisManager_1.RedisManager.isReady()) {
                await Rank_1.Rank.clearAllCache();
                console.log('🧹 清理缓存完成');
            }
            else {
                console.log('⚠️ Redis未就绪，跳过缓存清理');
            }
        }
        catch (error) {
            console.warn('⚠️ 缓存清理失败，但继续:', error);
        }
    }
    catch (error) {
        console.error('清理数据失败:', error);
        throw error;
    }
}
async function generateCountryRankings() {
    for (const country of COUNTRY_CITY_DATA) {
        const playerCount = Math.floor(Math.random() * 40) + 30; // 30-70个玩家
        console.log(`  🏳️ ${country.name} (${country.code}): 生成 ${playerCount} 个玩家`);
        for (let i = 1; i <= playerCount; i++) {
            const mockUser = {
                guuid: `${country.code}_player_${i}_${Date.now()}`,
                countryCode: country.code,
                nickName: `${country.code}Player${i}`,
                avatarId: Math.floor(Math.random() * 10) + 1,
                avatar: '',
                currCountryPassTimes: Math.floor(Math.random() * 100) + 1
            };
            try {
                await Rank_1.Rank.UpdateCountryScore(mockUser);
            }
            catch (error) {
                console.warn(`⚠️ 更新国家分数失败 ${country.code}:`, error);
            }
        }
        console.log(`    ✅ ${country.name} 玩家数据生成完成`);
    }
}
async function generateCityRankings() {
    for (const country of COUNTRY_CITY_DATA) {
        console.log(`  🏙️ 生成 ${country.name} 的城市数据...`);
        for (const city of country.cities) {
            const playerCount = Math.floor(Math.random() * 20) + 15; // 15-35个玩家
            console.log(`    🏙️ ${city.name} (${city.code}): ${playerCount} 个玩家`);
            for (let i = 1; i <= playerCount; i++) {
                const mockUser = {
                    guuid: `${city.code}_player_${i}_${Date.now()}`,
                    countryCode: country.code,
                    nickName: `${city.name}Player${i}`,
                    avatarId: Math.floor(Math.random() * 10) + 1,
                    avatar: '',
                    currCountryPassTimes: Math.floor(Math.random() * 50) + 1
                };
                try {
                    await Rank_1.Rank.UpdateCityScore(mockUser, city.code);
                }
                catch (error) {
                    console.warn(`⚠️ 更新城市分数失败 ${city.code}:`, error);
                }
            }
        }
        console.log(`    ✅ ${country.name} 城市数据生成完成`);
    }
}
async function aggregateCityData() {
    for (const country of COUNTRY_CITY_DATA) {
        console.log(`  🏗️ 聚合 ${country.name} 的城市数据`);
        for (const city of country.cities) {
            try {
                await Rank_1.Rank.UpdateCountryCityScore(country.code, city.code, city.name);
                console.log(`    📊 ${city.name} -> ${country.code}`);
            }
            catch (error) {
                console.warn(`⚠️ 聚合城市数据失败 ${city.code}:`, error);
            }
        }
    }
}
async function aggregateWorldData() {
    for (const country of COUNTRY_CITY_DATA) {
        console.log(`  🌍 聚合 ${country.name} (${country.code})`);
        try {
            await Rank_1.Rank.UpdateWorldScore(country.code);
        }
        catch (error) {
            console.warn(`⚠️ 聚合世界数据失败 ${country.code}:`, error);
        }
    }
}
async function verifyData() {
    console.log('\n🔍 验证排行榜数据...');
    // 验证世界排行榜
    console.log('\n=== 🌍 世界排行榜验证 ===');
    const worldRanking = await Rank_1.Rank.getWorldList();
    if (worldRanking.length > 0) {
        worldRanking.forEach((record, index) => {
            console.log(`${index + 1}. 🏳️ ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        const hasCorrectFormat = worldRanking.every(record => record.name && record.name.length <= 3 && record.name === record.id);
        console.log(hasCorrectFormat ? '✅ 格式正确：显示国家代码' : '❌ 格式错误：应显示国家代码');
    }
    else {
        console.log('❌ 世界排行榜无数据');
    }
    // 验证中国数据
    console.log('\n=== 🇨🇳 中国排行榜验证 ===');
    const cnCountryRanking = await Rank_1.Rank.getCountryList('CN');
    const cnCityRanking = await Rank_1.Rank.getCountryCityList('CN');
    console.log(`🏳️ 中国玩家: ${cnCountryRanking.length} 人`);
    if (cnCountryRanking.length > 0) {
        cnCountryRanking.slice(0, 3).forEach((record, index) => {
            console.log(`  ${index + 1}. 👤 ${record.name} - 分数: ${record.score}`);
        });
    }
    console.log(`🏙️ 中国城市: ${cnCityRanking.length} 个`);
    if (cnCityRanking.length > 0) {
        cnCityRanking.forEach((record, index) => {
            console.log(`  ${index + 1}. 🏙️ ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
    }
    // 验证美国数据
    console.log('\n=== 🇺🇸 美国排行榜验证 ===');
    const usCountryRanking = await Rank_1.Rank.getCountryList('US');
    const usCityRanking = await Rank_1.Rank.getCountryCityList('US');
    console.log(`🏳️ 美国玩家: ${usCountryRanking.length} 人`);
    console.log(`🏙️ 美国城市: ${usCityRanking.length} 个`);
    usCityRanking.slice(0, 3).forEach((record, index) => {
        console.log(`  ${index + 1}. ${record.name} - 分数: ${record.score}`);
    });
    console.log('\n📊 数据总结:');
    console.log(`🌍 世界排行榜: ${worldRanking.length} 个国家`);
    console.log(`🇨🇳 中国: ${cnCountryRanking.length} 玩家, ${cnCityRanking.length} 城市`);
    console.log(`🇺🇸 美国: ${usCountryRanking.length} 玩家, ${usCityRanking.length} 城市`);
}
// 运行脚本
generateRealisticRanking().then(() => {
    console.log('\n🎉 简化的真实排行榜数据生成完成！');
    console.log('\n✨ 特点:');
    console.log('   - 5个国家完整数据');
    console.log('   - 正确的排行榜格式');
    console.log('   - 稳定的数据生成');
    console.log('   - 不依赖IP地理位置库');
    process.exit(0);
}).catch(error => {
    console.error('\n💥 生成失败:', error);
    process.exit(1);
});
