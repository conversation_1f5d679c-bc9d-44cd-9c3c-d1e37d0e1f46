/*
 * 简化统一服务器 - 纯HTTP架构，适配三消游戏
 * 双端口设计：5000(网关) + 5001(游戏)，避免协议冲突
 * 使用Redis优化会话管理
 */

import chalk from 'chalk';
import path from 'path';
import { HttpServer } from 'tsrpc';
import { User } from './module/account/bll/User';
import { MongoDB } from './module/common/MongoDB';
import { RedisManager } from './module/common/RedisManager';
import { RedisSessionManager } from './module/common/RedisSessionManager';
import { Config, dev } from './module/config/Config';
import { Rank } from './module/rank/bll/Rank';
import { ShareConfig } from './tsrpc/models/ShareConfig';
import { BaseConf, BaseRequest } from './tsrpc/protocols/base';
import { serviceProto as gameServiceProto } from './tsrpc/protocols/ServiceProtoGame';
import { serviceProto as gateServiceProto } from './tsrpc/protocols/ServiceProtoGate';

async function startSimpleUnifiedServer() {
    try {
        console.log(chalk.green('🚀 启动统一服务器...'));

        // 仅在开发环境显示详细配置信息
        if (!ShareConfig.isProduction) {
            console.log(chalk.cyan('🔍 环境配置:'));
            console.log(chalk.cyan(`   环境: ${ShareConfig.environment}`));
            console.log(chalk.cyan(`   平台: ${ShareConfig.platform}`));
            console.log(chalk.cyan(`   端口: ${ShareConfig.port}`));
            console.log(chalk.cyan(`   MongoDB: ${ShareConfig.mongoDbName}`));
        }

        // 初始化开发环境配置
        dev();

        // 连接数据库
        await MongoDB.init();
        User.init();
        Rank.init();
        console.log(chalk.green('✅ 数据库连接成功'));

        // 初始化Redis
        await RedisManager.init();
        await RedisSessionManager.init();
        console.log(chalk.green('✅ Redis连接成功'));

        // 身份验证中间件
        const createAuthMiddleware = () => {
            return async (call: any) => {
                let conf: BaseConf | undefined = call.service.conf;
                let req = call.req as BaseRequest;

                call.dbUser = null;

                if (req.__ssoToken) {
                    const user = await RedisSessionManager.getSession(req.__ssoToken);
                    if (user) {
                        call.dbUser = user;
                        if (!ShareConfig.isProduction) {
                            console.log(chalk.gray(`🔐 [认证] 用户已登录: ${user.userName}`));
                        }
                    }
                }

                if (conf?.needCheckAddress && !(Config.ips as any)[call.conn.ip]) {
                    await call.error('没有访问权限', { code: 'NEED_AUTHORITY' });
                    return null;
                } else if (conf?.needLogin && !call.dbUser) {
                    if (!ShareConfig.isProduction) {
                        console.log(chalk.red(`❌ [认证] API ${call.service.name} 需要登录`));
                    }
                    await call.error('登录后获取访问权限', { code: 'NEED_LOGIN' });
                    return null;
                }

                return call;
            };
        };

        // 数据同步中间件
        const createDataSyncMiddleware = () => {
            return {
                postApiReturnFlow: async (value: any) => {
                    const call = value.call;
                    const req = call.req as BaseRequest;

                    if (value.return.isSucc && call.dbUser && req.__ssoToken) {
                        try {
                            const updated = await RedisSessionManager.updateSession(
                                req.__ssoToken,
                                call.dbUser
                            );
                            if (updated && !ShareConfig.isProduction) {
                                console.log(
                                    chalk.blue(`🔄 [数据同步] ${call.service.name} 已同步会话数据`)
                                );
                            }
                        } catch (error) {
                            if (!ShareConfig.isProduction) {
                                console.warn(
                                    chalk.yellow(
                                        `⚠️ [数据同步] ${call.service.name} 同步失败:`,
                                        error
                                    )
                                );
                            }
                        }
                    }

                    return value;
                },
            };
        };

        // 创建CORS中间件
        const createCorsMiddleware = () => {
            return async (call: any) => {
                const httpCall = call as any;
                const origin = httpCall.conn.httpReq?.headers.origin;

                if (!ShareConfig.isProduction) {
                    console.log(chalk.gray(`🌐 [CORS] 请求来源: ${origin}`));
                }

                let isAllowed = false;
                let allowedOriginValue = '';

                if (origin) {
                    for (const allowedOrigin of ShareConfig.corsOrigins) {
                        if (allowedOrigin === '*' || allowedOrigin === origin) {
                            isAllowed = true;
                            allowedOriginValue = origin;
                            break;
                        }
                    }
                }

                if (isAllowed && httpCall.conn.httpRes && allowedOriginValue) {
                    httpCall.conn.httpRes.setHeader(
                        'Access-Control-Allow-Origin',
                        allowedOriginValue
                    );
                    httpCall.conn.httpRes.setHeader(
                        'Access-Control-Allow-Methods',
                        'GET, POST, PUT, DELETE, OPTIONS'
                    );
                    httpCall.conn.httpRes.setHeader(
                        'Access-Control-Allow-Headers',
                        'Content-Type, Authorization, X-Requested-With, X-SSO-Token'
                    );
                    httpCall.conn.httpRes.setHeader('Access-Control-Allow-Credentials', 'true');
                    httpCall.conn.httpRes.setHeader('Access-Control-Max-Age', '86400');
                }

                if (httpCall.conn.httpReq?.method === 'OPTIONS') {
                    if (isAllowed && httpCall.conn.httpRes) {
                        httpCall.conn.httpRes.statusCode = 200;
                        httpCall.conn.httpRes.end();
                        return null;
                    }
                }

                return call;
            };
        };

        // 创建网关服务器 (端口5000)
        const gateServer = new HttpServer(gateServiceProto, {
            port: ShareConfig.port,
            json: true,
            logLevel: ShareConfig.isProduction ? 'error' : 'debug',
        });

        // 添加请求日志中间件
        gateServer.flows.preApiCallFlow.push(async call => {
            console.log(`🌐 [Gateway] 收到API请求: ${call.service.name}, 来源: ${call.conn.ip}`);
            return call;
        });

        // 添加连接日志中间件
        gateServer.flows.preConnectFlow.push(async conn => {
            console.log(
                `🔗 [Gateway] 新连接: ${conn.ip}, User-Agent: ${conn.req.headers['user-agent']}`
            );
            return conn;
        });

        gateServer.flows.preApiCallFlow.push(createCorsMiddleware());
        gateServer.flows.preApiCallFlow.push(createAuthMiddleware());

        // 健康检查
        gateServer.flows.preApiCallFlow.push(async call => {
            const httpCall = call as any;
            if (httpCall.conn.httpReq?.url === '/health') {
                httpCall.conn.httpRes.statusCode = 200;
                httpCall.conn.httpRes.setHeader('Content-Type', 'text/plain');
                httpCall.conn.httpRes.end('gateway-healthy\n');
                return null;
            }
            return call;
        });

        await gateServer.autoImplementApi(path.resolve(__dirname, 'server/gate/api'), true);

        // 创建游戏服务器 (端口5001)
        const gameServer = new HttpServer(gameServiceProto, {
            port: ShareConfig.port + 1,
            json: true,
            logLevel: ShareConfig.isProduction ? 'error' : 'debug',
        });

        gameServer.flows.preApiCallFlow.push(createCorsMiddleware());
        gameServer.flows.preApiCallFlow.push(createAuthMiddleware());

        const dataSyncMiddleware = createDataSyncMiddleware();
        gameServer.flows.postApiReturnFlow.push(dataSyncMiddleware.postApiReturnFlow);

        // 健康检查
        gameServer.flows.preApiCallFlow.push(async call => {
            const httpCall = call as any;
            if (httpCall.conn.httpReq?.url === '/health') {
                httpCall.conn.httpRes.statusCode = 200;
                httpCall.conn.httpRes.setHeader('Content-Type', 'text/plain');
                httpCall.conn.httpRes.end('game-healthy\n');
                return null;
            }
            return call;
        });

        await gameServer.autoImplementApi(path.resolve(__dirname, 'server/game/api'), true);

        // 启动服务器
        await Promise.all([gateServer.start(), gameServer.start()]);

        console.log(chalk.green(`✅ 网关服务器: http://localhost:${ShareConfig.port}`));
        console.log(chalk.green(`✅ 游戏服务器: http://localhost:${ShareConfig.port + 1}`));
        console.log(chalk.green(`🌟 ${ShareConfig.environment} 环境就绪`));

        // 会话统计（仅开发环境）
        if (!ShareConfig.isProduction) {
            setInterval(async () => {
                const stats = await RedisSessionManager.getSessionStats();
                console.log(
                    chalk.blue(
                        `📊 会话统计: 总会话=${stats.totalSessions}, 活跃用户=${stats.activeUsers}`
                    )
                );
            }, 5 * 60 * 1000);
        }
    } catch (error) {
        console.error(chalk.red('❌ 服务器启动失败:'), error);
        process.exit(1);
    }
}

startSimpleUnifiedServer();
