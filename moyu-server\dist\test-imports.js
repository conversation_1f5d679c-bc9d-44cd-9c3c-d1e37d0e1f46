"use strict";
// 测试文件 - 验证导入
console.log('🔍 开始测试导入...');
try {
    console.log('📝 导入 EcsGameSystem...');
    const { EcsGameSystem } = require('./server/game/EcsGameSystem');
    console.log('✅ EcsGameSystem 导入成功:', typeof EcsGameSystem);
    console.log('📝 导入 ServerGameSystem...');
    const { ServerGameSystem } = require('./server/game/ServerGameSystem');
    console.log('✅ ServerGameSystem 导入成功:', typeof ServerGameSystem);
    console.log('📝 测试创建 EcsGameSystem...');
    const ecsSystem = new EcsGameSystem();
    console.log('✅ EcsGameSystem 创建成功');
    console.log('📝 测试创建 ServerGameSystem...');
    const serverSystem = new ServerGameSystem();
    console.log('✅ ServerGameSystem 创建成功');
    console.log('🎉 所有测试通过！');
}
catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
}
