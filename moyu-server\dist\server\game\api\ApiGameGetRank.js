"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ApiGameGetRank;
const Rank_1 = require("../../../module/rank/bll/Rank");
const base_1 = require("../../../tsrpc/protocols/base");
const IPUntil_1 = require("../../../module/3rdData/IPUntil");
const ShareConfig_1 = require("../../../tsrpc/models/ShareConfig");
async function ApiGameGetRank(call) {
    var _a;
    const startTime = Date.now();
    const dbUser = call.dbUser;
    const { rankType } = call.req;
    if (!dbUser) {
        return call.error('用户未找到', { code: 'USER_NOT_FOUND' });
    }
    try {
        let rankTypeData = [];
        let selfRankData = undefined;
        console.log(`[GameGetRank] 开始处理排行榜请求: ${base_1.RankType[rankType]}, 用户: ${dbUser.key}`);
        switch (rankType) {
            case base_1.RankType.World:
                const worldStartTime = Date.now();
                console.log('[WorldRank] 获取世界排行榜数据 - 各国家比拼');
                rankTypeData = await Rank_1.Rank.getWorldList();
                console.log(`[WorldRank] 获取排行榜数据耗时: ${Date.now() - worldStartTime}ms`);
                // 确保世界排行榜数据结构正确 - 显示国家信息
                if (rankTypeData && rankTypeData.length > 0) {
                    rankTypeData.forEach((record, index) => {
                        record.rank = index + 1;
                        // 世界排行榜显示国家缩写和信息
                        record.name = record.id; // 使用国家代码作为显示名称 (CN, US, JP等)
                        if (!record.avatarId) {
                            record.avatarId = 1; // 默认头像
                        }
                        if (!record.updateTime) {
                            record.updateTime = new Date();
                        }
                    });
                }
                const worldRankStartTime = Date.now();
                const worldRank = await Rank_1.Rank.getRankInWorld(dbUser.countryCode);
                const worldData = await Rank_1.Rank.getWorldData(dbUser.countryCode);
                console.log(`[WorldRank] 获取自己排名耗时: ${Date.now() - worldRankStartTime}ms`);
                if (worldRank && worldData) {
                    selfRankData = {
                        id: dbUser.countryCode,
                        rank: worldRank,
                        score: worldData.score,
                        name: dbUser.countryCode, // 显示国家代码
                        avatarId: 1,
                        updateTime: new Date()
                    };
                }
                console.log(`[WorldRank] 获取成功，数据量: ${rankTypeData.length}, 自己国家排名: ${worldRank || '未上榜'}`);
                break;
            case base_1.RankType.Country:
                const countryStartTime = Date.now();
                console.log(`[CountryRank] 获取国家排行榜数据: ${dbUser.countryCode} - 本国玩家比拼`);
                rankTypeData = await Rank_1.Rank.getCountryList(dbUser.countryCode);
                console.log(`[CountryRank] 获取排行榜数据耗时: ${Date.now() - countryStartTime}ms`);
                const countryRankStartTime = Date.now();
                const countryRank = await Rank_1.Rank.getRankInCountry(dbUser.countryCode, dbUser.guuid);
                const countryData = await Rank_1.Rank.getCountryData(dbUser.countryCode, dbUser.guuid);
                console.log(`[CountryRank] 获取自己排名耗时: ${Date.now() - countryRankStartTime}ms`);
                if (countryRank && countryData) {
                    selfRankData = {
                        id: dbUser.guuid,
                        rank: countryRank,
                        score: countryData.score,
                        name: dbUser.nickName, // 显示玩家名称
                        avatarId: dbUser.avatarId,
                        updateTime: new Date()
                    };
                }
                break;
            case base_1.RankType.City:
                try {
                    const cityStartTime = Date.now();
                    // Facebook环境下跳过城市排行榜，因为使用Facebook SDK地理信息
                    if (ShareConfig_1.ShareConfig.platform === 'facebook') {
                        console.log('[CityRank] Facebook环境：跳过城市排行榜，使用Facebook SDK地理信息');
                        rankTypeData = [];
                        selfRankData = undefined;
                        break;
                    }
                    // 城市排行榜只显示同一国家内的城市比拼
                    console.log(`[CityRank] 获取城市排行榜数据: ${dbUser.countryCode} - 本国城市比拼`);
                    // 获取该国家所有城市的排行榜数据
                    rankTypeData = await Rank_1.Rank.getCountryCityList(dbUser.countryCode);
                    console.log(`[CityRank] 获取排行榜数据耗时: ${Date.now() - cityStartTime}ms`);
                    // 获取用户所在城市的排名（如果用户有城市数据）
                    const clientIP = ((_a = call.conn) === null || _a === void 0 ? void 0 : _a.ip) || '127.0.0.1';
                    const locationInfo = IPUntil_1.IPUntil.getLocationInfoByIP(clientIP);
                    if (locationInfo.cityCode) {
                        const cityRankStartTime = Date.now();
                        const cityRank = await Rank_1.Rank.getRankInCountryCity(dbUser.countryCode, locationInfo.cityCode);
                        const cityData = await Rank_1.Rank.getCountryCityData(dbUser.countryCode, locationInfo.cityCode);
                        console.log(`[CityRank] 获取自己排名耗时: ${Date.now() - cityRankStartTime}ms`);
                        if (cityRank && cityData) {
                            selfRankData = {
                                id: locationInfo.cityCode,
                                rank: cityRank,
                                score: cityData.score,
                                name: locationInfo.cityName || locationInfo.cityCode, // 显示城市名称
                                avatarId: 1,
                                updateTime: new Date()
                            };
                        }
                        console.log(`[CityRank] 用户城市: ${locationInfo.cityName} (${locationInfo.cityCode}), 排名: ${cityRank || '未上榜'}`);
                    }
                    else {
                        console.warn('[CityRank] 无法获取城市信息');
                    }
                }
                catch (error) {
                    console.error('[CityRank] 获取城市排行榜失败:', error);
                    rankTypeData = [];
                }
                break;
            case base_1.RankType.Friend:
                // 好友排行榜功能暂未实现
                console.log('[Rank] 好友排行榜功能暂未实现');
                break;
            default:
                return call.error('无效的排行榜类型', { code: 'INVALID_RANK_TYPE' });
        }
        // 通用数据清理和验证
        if (rankTypeData && rankTypeData.length > 0) {
            rankTypeData.forEach((record, index) => {
                // 确保排名正确
                record.rank = index + 1;
                // 确保必要字段存在
                if (!record.name) {
                    record.name = record.id;
                }
                if (typeof record.score !== 'number') {
                    record.score = 0;
                }
                // 修复updateTime类型转换问题
                if (record.updateTime) {
                    if (typeof record.updateTime === 'string') {
                        // 将字符串转换为Date对象
                        record.updateTime = new Date(record.updateTime);
                    }
                    else if (!(record.updateTime instanceof Date)) {
                        // 如果不是Date对象也不是字符串，使用当前时间
                        record.updateTime = new Date();
                    }
                }
                else {
                    // 如果没有updateTime，使用当前时间
                    record.updateTime = new Date();
                }
            });
        }
        // 修复selfRankData的updateTime字段
        if (selfRankData && selfRankData.updateTime) {
            if (typeof selfRankData.updateTime === 'string') {
                selfRankData.updateTime = new Date(selfRankData.updateTime);
            }
            else if (!(selfRankData.updateTime instanceof Date)) {
                selfRankData.updateTime = new Date();
            }
        }
        else if (selfRankData) {
            selfRankData.updateTime = new Date();
        }
        const response = {
            code: 0,
            message: '获取排行榜成功',
            rankType: rankType,
            rankTypeData: rankTypeData || [],
            selfRankData: selfRankData
        };
        console.log(`[GameGetRank] 用户 ${dbUser.key} 获取排行榜成功: ${base_1.RankType[rankType]}, 数据量: ${(rankTypeData === null || rankTypeData === void 0 ? void 0 : rankTypeData.length) || 0}, 总耗时: ${Date.now() - startTime}ms`);
        return call.succ(response);
    }
    catch (error) {
        console.error('获取排行榜失败:', error);
        return call.error('获取排行榜失败', { code: 'GET_RANK_FAILED' });
    }
}
