"use strict";
/*
 * @Author: dgflash
 * @Date: 2022-05-05 09:37:49
 * @LastEditors: dgflash
 * @LastEditTime: 2022-11-14 16:43:51
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Config = void 0;
exports.dev = dev;
const ShareConfig_1 = require("../../tsrpc/models/ShareConfig");
/** 服务器配置 */
exports.Config = {
    /** 密码加密盐 */
    passwordSaltRounds: 10,
    /** 数据库地址 */
    mongodb: ShareConfig_1.ShareConfig.mongoUrl.replace('mongodb://', ''),
    /** 网关服务配置 */
    gate: {
        /** 网关端口 */
        port: ShareConfig_1.ShareConfig.port.toString(),
        /** 游戏服务端口 */
        gamePort: ShareConfig_1.ShareConfig.gamePort.toString(),
    },
    /** 认证服务配置 */
    authHttpUrl: "http://127.0.0.1:44401",
    authHttpsUrl: "https://127.0.0.1:44401",
    /** SSL证书配置 */
    certificate_key: "./cert/private.key",
    certificate_crt: "./cert/certificate.crt",
    /** 统一服务器配置 - 双端口HTTP架构 */
    unified: {
        /** 网关端口 (登录注册) */
        httpPort: ShareConfig_1.ShareConfig.port,
        /** 游戏服务器端口 (游戏逻辑) */
        gamePort: ShareConfig_1.ShareConfig.gamePort,
        /** 服务器地址 - 根据环境确定 */
        host: ShareConfig_1.ShareConfig.isProduction ? "0.0.0.0" : "127.0.0.1",
        /** 网关服务地址 */
        httpUrl: `${ShareConfig_1.ShareConfig.isProduction ? "0.0.0.0" : "127.0.0.1"}:${ShareConfig_1.ShareConfig.port}`,
        /** 游戏服务地址 */
        gameUrl: `${ShareConfig_1.ShareConfig.isProduction ? "0.0.0.0" : "127.0.0.1"}:${ShareConfig_1.ShareConfig.gamePort}`,
    },
    /** 游戏服务配置 */
    game: {
        gmOpen: process.env['gmOpen'] || true,
        /** 网关端口 */
        httpPort: ShareConfig_1.ShareConfig.port.toString(),
        /** 游戏服务器端口 */
        gamePort: ShareConfig_1.ShareConfig.gamePort.toString(),
        /** 游戏日志输出间隔时间 */
        interval_logger: 500000,
        /** 是否打印长连接消息日志 */
        logMsg: false,
        /** 注册失败重试间隔时间 */
        register_fail_retry_interval: 15, // 单位秒
        /** 发送游戏状态与游戏服务器同步数据的间隔时间 */
        update_state_interval: 20000,
        /** 游戏空闲时间 */
        empty_time: 33300,
        /** 游戏主动广播其它玩家状态频率（每秒2次） */
        broadcast_player_state_rate: Math.floor(1000 / 2),
    },
    /** 服务器之间通讯 IP 白名单 */
    ips: {
        "localhost": true,
        "127.0.0.1": true,
        "************": true,
        "**********": true,
        "*************": true,
        "***********": true,
        "idlefun.press": true
    }
};
function dev() {
    // 打印ShareConfig调试信息
    console.log(`🔍 环境确定: ${ShareConfig_1.ShareConfig.environment} (${ShareConfig_1.ShareConfig.platform})`);
    console.log(`🔧 MongoDB: ${ShareConfig_1.ShareConfig.mongoUrl}`);
    console.log(`🔧 端口配置: 网关=${ShareConfig_1.ShareConfig.port}, 游戏=${ShareConfig_1.ShareConfig.gamePort} (双端口HTTP架构)`);
    console.log(`🔧 生产环境: ${ShareConfig_1.ShareConfig.isProduction}`);
    // 统一服务器配置信息
    console.log('🔧 开发环境配置加载完成');
    console.log(`📡 网关服务: http://${exports.Config.unified.host}:${exports.Config.unified.httpPort}`);
    console.log(`📡 游戏服务: http://${exports.Config.unified.host}:${exports.Config.unified.gamePort}`);
    console.log(`🎯 双端口HTTP架构：网关处理登录注册，游戏服务器处理游戏逻辑`);
}
