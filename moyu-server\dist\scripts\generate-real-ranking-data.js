"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const Rank_1 = require("../module/rank/bll/Rank");
const MongoDB_1 = require("../module/common/MongoDB");
const IPUntil_1 = require("../module/3rdData/IPUntil");
const RedisManager_1 = require("../module/common/RedisManager");
const ShareConfig_1 = require("../tsrpc/models/ShareConfig");
// 真实的国家IP数据
const REAL_COUNTRY_DATA = [
    {
        country: '中国',
        countryCode: 'CN',
        ipRanges: [
            '*******', '*******', '*******', '*******', '********',
            '********', '*********', '**********', '********', '***********',
            '*********', '*********', '*********', '**********', '*********',
            '*********', '*********', '*********', '*********', '60.0.0.0',
            '*********', '*********', '*********', '**********', '**********',
            '**********', '***********', '*********', '*********', '**********',
            '*********', '*********', '**********', '**********', '**********',
            '*********', '*********', '**********', '*********', '120.0.0.0',
            '*********', '*********', '*********', '*********', '*********'
        ],
        cities: [
            { name: '北京', nameEn: 'Beijing', code: 'bj', ips: ['*******', '********', '*********'] },
            { name: '上海', nameEn: 'Shanghai', code: 'sh', ips: ['*******', '*********', '*********'] },
            { name: '深圳', nameEn: 'Shenzhen', code: 'sz', ips: ['*******', '********', '*********'] },
            { name: '广州', nameEn: 'Guangzhou', code: 'gz', ips: ['*******', '*********', '60.0.0.0'] },
            { name: '杭州', nameEn: 'Hangzhou', code: 'hz', ips: ['********', '**********', '*********'] },
            { name: '成都', nameEn: 'Chengdu', code: 'cd', ips: ['**********', '*********', '*********'] },
            { name: '武汉', nameEn: 'Wuhan', code: 'wh', ips: ['***********', '*********', '**********'] },
            { name: '西安', nameEn: 'Xian', code: 'xa', ips: ['*********', '*********', '*********'] }
        ]
    },
    {
        country: '美国',
        countryCode: 'US',
        ipRanges: [
            '*******', '*******', '*******', '*******', '*******',
            '********', '*********', '********', '1*******', '1*******',
            '********', '20.0.0.0', '2*******', '2*******', '3*******',
            '********', '40.0.0.0', '4*******', '********', '50.0.0.0',
            '********', '5*******', '6*******', '********', '6*******',
            '6*******', '********', '********', '70.0.0.0', '********',
            '********', '7*******', '7*******', '75.0.0.0', '7*******'
        ],
        cities: [
            { name: 'New York', nameEn: 'New York', code: 'nyc', ips: ['*******', '*******', '40.0.0.0'] },
            { name: 'Los Angeles', nameEn: 'Los Angeles', code: 'la', ips: ['*******', '*********', '4*******'] },
            { name: 'Chicago', nameEn: 'Chicago', code: 'chi', ips: ['*******', '********', '50.0.0.0'] },
            { name: 'Houston', nameEn: 'Houston', code: 'hou', ips: ['*******', '********', '********'] },
            { name: 'Phoenix', nameEn: 'Phoenix', code: 'phx', ips: ['********', '2*******', '6*******'] },
            { name: 'Philadelphia', nameEn: 'Philadelphia', code: 'phl', ips: ['1*******', '3*******', '6*******'] },
            { name: 'San Antonio', nameEn: 'San Antonio', code: 'sat', ips: ['1*******', '********', '70.0.0.0'] },
            { name: 'San Diego', nameEn: 'San Diego', code: 'sd', ips: ['20.0.0.0', '********', '7*******'] }
        ]
    },
    {
        country: '日本',
        countryCode: 'JP',
        ipRanges: [
            '1.0.16.0', '1.33.0.0', '1.72.0.0', '1.112.0.0', '1.176.0.0',
            '14.192.0.0', '27.80.0.0', '27.96.0.0', '42.176.0.0', '49.212.0.0',
            '58.0.0.0', '58.80.0.0', '60.32.0.0', '61.112.0.0', '61.192.0.0',
            '103.1.168.0', '106.72.0.0', '110.0.0.0', '1*********', '1**********',
            '115.176.0.0', '1********', '119.224.0.0', '121.1.0.0', '122.16.0.0',
            '123.216.0.0', '124.144.0.0', '125.16.0.0', '12*******', '13*******'
        ],
        cities: [
            { name: '東京', nameEn: 'Tokyo', code: 'tokyo', ips: ['1.0.16.0', '14.192.0.0', '58.0.0.0'] },
            { name: '大阪', nameEn: 'Osaka', code: 'osaka', ips: ['1.33.0.0', '27.80.0.0', '60.32.0.0'] },
            { name: '横浜', nameEn: 'Yokohama', code: 'yokohama', ips: ['1.72.0.0', '42.176.0.0', '61.112.0.0'] },
            { name: '名古屋', nameEn: 'Nagoya', code: 'nagoya', ips: ['1.112.0.0', '49.212.0.0', '106.72.0.0'] },
            { name: '神戸', nameEn: 'Kobe', code: 'kobe', ips: ['1.176.0.0', '58.80.0.0', '1*********'] },
            { name: '京都', nameEn: 'Kyoto', code: 'kyoto', ips: ['27.96.0.0', '61.192.0.0', '1********'] }
        ]
    },
    {
        country: '韩国',
        countryCode: 'KR',
        ipRanges: [
            '1.11.0.0', '1.16.0.0', '1.176.0.0', '1.224.0.0', '14.32.0.0',
            '2*******', '39.7.0.0', '42.1.0.0', '49.161.0.0', '58.120.0.0',
            '59.5.0.0', '59.18.0.0', '60.160.0.0', '61.74.0.0', '61.252.0.0',
            '101.79.0.0', '103.243.200.0', '106.101.0.0', '110.70.0.0', '112.216.0.0'
        ],
        cities: [
            { name: '서울', nameEn: 'Seoul', code: 'seoul', ips: ['1.11.0.0', '14.32.0.0', '58.120.0.0'] },
            { name: '부산', nameEn: 'Busan', code: 'busan', ips: ['1.16.0.0', '2*******', '59.5.0.0'] },
            { name: '인천', nameEn: 'Incheon', code: 'incheon', ips: ['1.224.0.0', '42.1.0.0', '60.160.0.0'] },
            { name: '대구', nameEn: 'Daegu', code: 'daegu', ips: ['39.7.0.0', '49.161.0.0', '61.74.0.0'] }
        ]
    },
    {
        country: '英国',
        countryCode: 'GB',
        ipRanges: [
            '2.16.0.0', '5.8.0.0', '5.102.0.0', '8.6.0.0', '25.0.0.0',
            '31.0.0.0', '3*******', '4*******', '51.0.0.0', '62.0.0.0',
            '7*******', '78.0.0.0', '79.0.0.0', '80.0.0.0', '81.0.0.0',
            '82.0.0.0', '8*******', '8*******', '85.0.0.0', '8*******'
        ],
        cities: [
            { name: 'London', nameEn: 'London', code: 'lon', ips: ['2.16.0.0', '25.0.0.0', '7*******'] },
            { name: 'Birmingham', nameEn: 'Birmingham', code: 'bir', ips: ['5.8.0.0', '31.0.0.0', '80.0.0.0'] },
            { name: 'Manchester', nameEn: 'Manchester', code: 'man', ips: ['5.102.0.0', '4*******', '8*******'] },
            { name: 'Glasgow', nameEn: 'Glasgow', code: 'gla', ips: ['8.6.0.0', '62.0.0.0', '85.0.0.0'] }
        ]
    }
];
async function generateRealRankingData() {
    console.log('🌍 开始生成真实的排行榜数据...');
    try {
        // 初始化数据库连接
        console.log('📝 初始化数据库连接...');
        await MongoDB_1.MongoDB.init();
        // 初始化Redis连接
        console.log('📝 初始化Redis连接...');
        await RedisManager_1.RedisManager.init();
        // 等待Redis连接就绪
        let retryCount = 0;
        const maxRetries = 10;
        while (!RedisManager_1.RedisManager.isReady() && retryCount < maxRetries) {
            console.log(`⏳ 等待Redis连接就绪... (${retryCount + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            retryCount++;
        }
        if (!RedisManager_1.RedisManager.isReady()) {
            console.warn('⚠️ Redis连接超时，但继续运行...');
        }
        // 初始化其他组件
        console.log('📝 初始化排行榜系统...');
        Rank_1.Rank.init();
        console.log('📝 初始化IP地理位置系统...');
        // Facebook环境下跳过IP地理位置初始化，因为使用Facebook SDK地理信息
        if (ShareConfig_1.ShareConfig.platform === 'facebook') {
            console.log('🌍 Facebook环境：跳过IP地理位置系统初始化');
        }
        else {
            IPUntil_1.IPUntil.init();
        }
        console.log('\n🗑️ 清理旧数据...');
        await clearAllRankingData();
        console.log('\n🏗️ 生成真实的排行榜数据...');
        // 1. 为每个国家生成真实的玩家数据
        await generateCountryPlayersWithRealIPs();
        // 2. 为每个城市生成真实的玩家数据
        await generateCityPlayersWithRealIPs();
        // 3. 聚合城市数据到国家城市排行榜
        await aggregateCityDataToCountryCities();
        // 4. 聚合国家数据到世界排行榜
        await aggregateCountryDataToWorld();
        console.log('\n✅ 真实排行榜数据生成完成！');
        // 验证数据
        await verifyRealRankingData();
    }
    catch (error) {
        console.error('❌ 生成失败:', error);
        throw error;
    }
}
async function clearAllRankingData() {
    try {
        const db = MongoDB_1.MongoDB.rankDb;
        // 清理所有排行榜集合
        const collections = await db.collections();
        for (const collection of collections) {
            if (collection.collectionName.includes('rank') ||
                collection.collectionName.includes('world') ||
                collection.collectionName.includes('city_') ||
                collection.collectionName.includes('country_cities_') ||
                /^[A-Z]{2}$/.test(collection.collectionName)) {
                await collection.deleteMany({});
                console.log(`🧹 已清理集合: ${collection.collectionName}`);
            }
        }
        // 安全地清理缓存
        try {
            if (RedisManager_1.RedisManager.isReady()) {
                await Rank_1.Rank.clearAllCache();
                console.log('🧹 已清理Redis缓存');
            }
            else {
                console.log('⚠️ Redis未就绪，跳过缓存清理');
            }
        }
        catch (cacheError) {
            console.warn('⚠️ 缓存清理失败，但继续运行:', cacheError);
        }
    }
    catch (error) {
        console.error('清理数据失败:', error);
        throw error;
    }
}
async function generateCountryPlayersWithRealIPs() {
    console.log('👥 生成各国玩家数据 (使用真实IP)...');
    for (const countryData of REAL_COUNTRY_DATA) {
        const playerCount = Math.floor(Math.random() * 30) + 20; // 20-50个玩家
        console.log(`  🏳️ ${countryData.country} (${countryData.countryCode}): ${playerCount} 玩家`);
        for (let i = 1; i <= playerCount; i++) {
            // 随机选择一个真实IP
            const randomIP = countryData.ipRanges[Math.floor(Math.random() * countryData.ipRanges.length)];
            // 验证IP地理位置（Facebook环境下跳过）
            let locationInfo = { countryCode: countryData.countryCode, cityName: 'Unknown' };
            if (ShareConfig_1.ShareConfig.platform !== 'facebook') {
                locationInfo = IPUntil_1.IPUntil.getLocationInfoByIP(randomIP);
            }
            const mockUser = {
                guuid: `${countryData.countryCode}_${randomIP.replace(/\./g, '_')}_${i}`,
                countryCode: countryData.countryCode,
                nickName: `${countryData.countryCode}Player${i}`,
                avatarId: Math.floor(Math.random() * 10) + 1,
                avatar: '',
                currCountryPassTimes: Math.floor(Math.random() * 100) + 1
            };
            await Rank_1.Rank.UpdateCountryScore(mockUser);
            // 记录IP映射关系
            console.log(`    👤 ${mockUser.nickName}: IP=${randomIP}, 检测到=${locationInfo.countryCode}, 城市=${locationInfo.cityName}`);
        }
    }
}
async function generateCityPlayersWithRealIPs() {
    console.log('\n🏙️ 生成各城市玩家数据 (使用真实城市IP)...');
    for (const countryData of REAL_COUNTRY_DATA) {
        for (const cityData of countryData.cities) {
            const playerCount = Math.floor(Math.random() * 15) + 10; // 10-25个玩家
            console.log(`    🏙️ ${cityData.name} (${cityData.code}): ${playerCount} 玩家`);
            for (let i = 1; i <= playerCount; i++) {
                try {
                    // 使用城市的真实IP
                    const cityIP = cityData.ips[i % cityData.ips.length];
                    // 验证城市IP地理位置（Facebook环境下跳过）
                    let locationInfo = { cityName: cityData.name };
                    if (ShareConfig_1.ShareConfig.platform !== 'facebook') {
                        locationInfo = IPUntil_1.IPUntil.getLocationInfoByIP(cityIP);
                    }
                    const mockUser = {
                        guuid: `${cityData.code}_${cityIP.replace(/\./g, '_')}_${i}`,
                        countryCode: countryData.countryCode,
                        nickName: `${cityData.nameEn}Player${i}`,
                        avatarId: Math.floor(Math.random() * 10) + 1,
                        avatar: '',
                        currCountryPassTimes: Math.floor(Math.random() * 50) + 1
                    };
                    // 安全地更新城市分数，即使Redis失败也继续
                    try {
                        await Rank_1.Rank.UpdateCityScore(mockUser, cityData.code);
                    }
                    catch (updateError) {
                        console.warn(`⚠️ 更新城市分数失败 ${cityData.code}:`, updateError);
                        // 继续处理下一个玩家，不中断整个流程
                    }
                    console.log(`      👤 ${mockUser.nickName}: IP=${cityIP}, 检测城市=${locationInfo.cityName}`);
                }
                catch (playerError) {
                    console.warn(`⚠️ 生成玩家数据失败 ${cityData.code}_${i}:`, playerError);
                    // 继续处理下一个玩家
                }
            }
        }
    }
}
async function aggregateCityDataToCountryCities() {
    console.log('\n🌆 聚合城市数据到国家城市排行榜...');
    for (const countryData of REAL_COUNTRY_DATA) {
        console.log(`  🏗️ 聚合 ${countryData.country} 的城市数据`);
        for (const cityData of countryData.cities) {
            await Rank_1.Rank.UpdateCountryCityScore(countryData.countryCode, cityData.code, cityData.name);
            console.log(`    📊 ${cityData.name} -> ${countryData.countryCode}`);
        }
    }
}
async function aggregateCountryDataToWorld() {
    console.log('\n🌍 聚合国家数据到世界排行榜...');
    for (const countryData of REAL_COUNTRY_DATA) {
        console.log(`  🌍 聚合 ${countryData.country} (${countryData.countryCode})`);
        await Rank_1.Rank.UpdateWorldScore(countryData.countryCode);
    }
}
async function verifyRealRankingData() {
    console.log('\n🔍 验证真实排行榜数据...');
    // 验证世界排行榜
    console.log('\n=== 🌍 世界排行榜验证 (应显示国家代码) ===');
    const worldRanking = await Rank_1.Rank.getWorldList();
    if (worldRanking.length > 0) {
        worldRanking.slice(0, 5).forEach((record, index) => {
            console.log(`${index + 1}. 🏳️ ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        // 验证是否显示国家代码
        const hasCorrectFormat = worldRanking.every(record => record.name && record.name.length <= 3 && record.name === record.id);
        console.log(hasCorrectFormat ? '✅ 格式正确：显示国家代码' : '❌ 格式错误：应显示国家代码');
    }
    else {
        console.log('❌ 世界排行榜无数据');
    }
    // 验证中国国家排行榜
    console.log('\n=== 🇨🇳 中国国家排行榜验证 (应显示玩家名字) ===');
    const cnCountryRanking = await Rank_1.Rank.getCountryList('CN');
    if (cnCountryRanking.length > 0) {
        cnCountryRanking.slice(0, 5).forEach((record, index) => {
            console.log(`${index + 1}. 👤 ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        console.log(`✅ 中国国家排行榜：${cnCountryRanking.length} 条真实玩家数据`);
    }
    // 验证中国城市排行榜
    console.log('\n=== 🏙️ 中国城市排行榜验证 (应显示城市名字) ===');
    const cnCityRanking = await Rank_1.Rank.getCountryCityList('CN');
    if (cnCityRanking.length > 0) {
        cnCityRanking.forEach((record, index) => {
            console.log(`${index + 1}. 🏙️ ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        console.log(`✅ 中国城市排行榜：${cnCityRanking.length} 条真实城市数据`);
    }
    // 验证美国数据
    console.log('\n=== 🇺🇸 美国排行榜验证 ===');
    const usCountryRanking = await Rank_1.Rank.getCountryList('US');
    const usCityRanking = await Rank_1.Rank.getCountryCityList('US');
    console.log(`🏳️ 美国玩家: ${usCountryRanking.length} 人`);
    console.log(`🏙️ 美国城市: ${usCityRanking.length} 个`);
    usCityRanking.slice(0, 3).forEach((record, index) => {
        console.log(`  ${index + 1}. ${record.name} - 分数: ${record.score}`);
    });
    // 验证日本数据
    console.log('\n=== 🇯🇵 日本排行榜验证 ===');
    const jpCountryRanking = await Rank_1.Rank.getCountryList('JP');
    const jpCityRanking = await Rank_1.Rank.getCountryCityList('JP');
    console.log(`🏳️ 日本玩家: ${jpCountryRanking.length} 人`);
    console.log(`🏙️ 日本城市: ${jpCityRanking.length} 个`);
    jpCityRanking.slice(0, 3).forEach((record, index) => {
        console.log(`  ${index + 1}. ${record.name} - 分数: ${record.score}`);
    });
    console.log('\n📊 数据总结:');
    console.log(`🌍 世界排行榜: ${worldRanking.length} 个国家`);
    console.log(`🇨🇳 中国玩家: ${cnCountryRanking.length} 人, 城市: ${cnCityRanking.length} 个`);
    console.log(`🇺🇸 美国玩家: ${usCountryRanking.length} 人, 城市: ${usCityRanking.length} 个`);
    console.log(`🇯🇵 日本玩家: ${jpCountryRanking.length} 人, 城市: ${jpCityRanking.length} 个`);
}
// 运行脚本
generateRealRankingData().then(() => {
    console.log('\n🎉 真实排行榜数据生成完成！');
    console.log('\n✨ 现在数据包含:');
    console.log('   - 真实的国家IP地址范围');
    console.log('   - 真实的城市IP映射');
    console.log('   - 正确的地理位置检测');
    console.log('   - 国家代码和城市名称显示');
    process.exit(0);
}).catch(error => {
    console.error('\n💥 生成失败:', error);
    process.exit(1);
});
