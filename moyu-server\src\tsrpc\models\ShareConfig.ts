// ShareConfig.ts - dev分支配置 (个人开发环境)
// 根据分支自动确定环境配置，简化部署流程

export enum Environment {
    DEVELOPMENT = 'development',
    FACEBOOK_MOCK = 'facebook_mock',
    PRODUCTION_PERSONAL = 'production_personal',
    PRODUCTION_FACEBOOK = 'production_facebook',
}

export enum Platform {
    PERSONAL = 'personal',
    FACEBOOK = 'facebook',
}

export interface ServerConfig {
    environment: Environment;
    platform: Platform;
    serverUrl: string;
    mongoUrl: string;
    mongoDbName: string;
    port: number;
    isProduction: boolean;
    corsOrigins: string[];
    enableFacebookSDK: boolean;
    facebookAppId?: string;

    // 核心协议配置
    https: boolean;
    gate: string;
    json: boolean;
    security: boolean;

    // 双端口HTTP架构支持
    gamePort: number;
    gameServerUrl: string;
}


// 🔧 dev分支专用：个人开发环境配置
const DEVELOPMENT_CONFIG: ServerConfig = {
    environment: Environment.DEVELOPMENT,
    platform: Platform.PERSONAL,
    serverUrl: 'http://localhost:3000',
    mongoUrl: 'mongodb://localhost:27017',
    mongoDbName: 'moyu_dev',
    port: 3000,
    isProduction: false,
    corsOrigins: ['*'],
    enableFacebookSDK: false,

    // 核心协议配置
    https: false,
    gate: 'localhost:3000',
    json: true,
    security: true,

    // 双端口HTTP架构支持
    gamePort: 3001,
    gameServerUrl: 'http://localhost:3001',
};

// 环境配置映射
const CONFIG_MAP: Record<Environment, ServerConfig> = {
    [Environment.DEVELOPMENT]: DEVELOPMENT_CONFIG,
    [Environment.FACEBOOK_MOCK]: DEVELOPMENT_CONFIG, // 简化
    [Environment.PRODUCTION_PERSONAL]: DEVELOPMENT_CONFIG, // 简化
    [Environment.PRODUCTION_FACEBOOK]: DEVELOPMENT_CONFIG, // 简化
};

// 获取当前配置 - dev分支专用个人开发配置
export const ShareConfig: ServerConfig = DEVELOPMENT_CONFIG;

// 配置验证
export function validateConfig(): boolean {
    const config = ShareConfig;

    if (!config.serverUrl) {
        console.error('Missing required URL configuration');
        return false;
    }

    if (!config.mongoUrl || !config.mongoDbName) {
        console.error('Missing required MongoDB configuration');
        return false;
    }

    if (config.enableFacebookSDK && !config.facebookAppId) {
        console.error('Facebook SDK enabled but no App ID provided');
        return false;
    }

    return true;
}

// 打印当前配置信息
export function printConfigInfo(): void {
    console.log('=== ShareConfig Information (dev) ===');
    console.log(`Environment: ${ShareConfig.environment}`);
    console.log(`Platform: ${ShareConfig.platform}`);
    console.log(`Server URL: ${ShareConfig.serverUrl}`);
    console.log(`Database: ${ShareConfig.mongoDbName}`);
    console.log(`Gateway Port: ${ShareConfig.port} (登录注册)`);
    console.log(`Game Port: ${ShareConfig.gamePort} (游戏逻辑)`);
    console.log(`Game Server URL: ${ShareConfig.gameServerUrl}`);
    console.log(`Production: ${ShareConfig.isProduction}`);
    console.log(`Facebook SDK: ${ShareConfig.enableFacebookSDK}`);
    console.log('============================================================');
}

export default ShareConfig;
