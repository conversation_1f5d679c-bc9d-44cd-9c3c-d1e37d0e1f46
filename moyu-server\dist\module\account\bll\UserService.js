"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const uuid_1 = require("uuid");
const ShareConfig_1 = require("../../../tsrpc/models/ShareConfig");
const commonTools_1 = require("../../../tsrpc/protocols/commonTools");
const IPUntil_1 = require("../../3rdData/IPUntil");
const User_1 = require("./User");
class UserService {
    static async generateUniqueId(len) {
        return (0, uuid_1.v4)().substring(0, len); // 生成一个随机的 UUID v4 并截取所需长度
    }
    static async registerUser(isGuest, isThridParty, ip, platform, platformType, guuid, userName, passWord) {
        // Facebook环境下直接使用默认国家代码，避免IP地理位置查询
        let countryCode;
        if (ShareConfig_1.ShareConfig.platform === 'facebook') {
            countryCode = 'US'; // Facebook环境默认使用美国
        }
        else {
            countryCode = !ShareConfig_1.ShareConfig.https ? 'CN' : IPUntil_1.IPUntil.getCountryCodeByIP(ip);
        }
        let generatedUserName = userName;
        let generatedPassWord = passWord;
        if (isGuest || isThridParty) {
            generatedUserName = await this.generateUniqueId(8);
            let userData = await User_1.User.getUserByUserName(generatedUserName);
            let attempts = 0;
            while (userData && attempts < 3) {
                generatedUserName = await this.generateUniqueId(8);
                userData = await User_1.User.getUserByUserName(generatedUserName);
                attempts++;
            }
            if (attempts >= 3) {
                return null; // 返回 null 表示错误
            }
            generatedPassWord = await this.generateUniqueId(6);
        }
        else {
            if (!userName || !passWord)
                return null;
            let ret = commonTools_1.CommonTools.checkUsername(userName);
            let ret2 = commonTools_1.CommonTools.checkPassword(passWord);
            if (!ret || !ret2) {
                return null; // 返回 null 表示错误
            }
            let userData = await User_1.User.getUserByUserName(userName);
            if (userData) {
                return null; // 返回 null 表示用户名已存在
            }
            generatedUserName = userName;
            generatedPassWord = passWord;
        }
        let args = {
            userName: generatedUserName,
            countryCode: countryCode,
            platform: platform,
            platformType: platformType,
            passWord: generatedPassWord,
            isGuest: isGuest,
            guuid: guuid
        };
        let guuidResult = await User_1.User.addUser(args);
        return { guuid: guuidResult, userName: generatedUserName, passWord: generatedPassWord };
    }
    /**
     * 注册Facebook用户
     * @param facebookUserData Facebook用户数据
     * @returns 注册结果
     */
    static async registerFacebookUser(facebookUserData) {
        const { facebookId, playerName, playerPhoto, locale, countryCode, platform, platformType, ip } = facebookUserData;
        // 生成唯一用户名（基于Facebook昵称）
        let generatedUserName = await this.generateUniqueId(8);
        let userData = await User_1.User.getUserByUserName(generatedUserName);
        let attempts = 0;
        while (userData && attempts < 3) {
            generatedUserName = await this.generateUniqueId(8);
            userData = await User_1.User.getUserByUserName(generatedUserName);
            attempts++;
        }
        if (attempts >= 3) {
            return null; // 返回 null 表示错误
        }
        // 生成随机密码
        const generatedPassWord = await this.generateUniqueId(6);
        // 构建用户数据
        let args = {
            userName: generatedUserName,
            countryCode: countryCode,
            platform: platform,
            platformType: platformType,
            passWord: generatedPassWord,
            isGuest: false, // Facebook用户不是游客
            facebookId: facebookId
        };
        let guuidResult = await User_1.User.addUser(args);
        // 更新用户的昵称和头像
        if (guuidResult) {
            const dUser = await User_1.User.getUserByGuuid(guuidResult);
            if (dUser) {
                dUser.nickName = playerName;
                dUser.avatar = playerPhoto;
                await User_1.User.updateUserData(dUser._id, dUser);
            }
        }
        return { guuid: guuidResult, userName: generatedUserName, passWord: generatedPassWord };
    }
}
exports.UserService = UserService;
