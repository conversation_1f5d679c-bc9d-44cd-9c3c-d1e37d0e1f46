{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/common/CommonNet.ts"], "names": ["CommonNet", "WECHAT", "HttpClient_Browser", "HttpClient", "Ws<PERSON><PERSON>_<PERSON>rowser", "WsClient", "HttpClient_Miniapp", "WsClient_Miniapp", "oops", "Security", "ShareConfig", "DataUpdateType", "ServiceProtoGame", "serviceProto", "ServiceProtoGate", "ClientConfig", "GameServerConfig", "GameStorageConfig", "LocalConfig", "DataManager", "smc", "constructor", "hcGate", "hcGame", "wcGame", "wcGate", "createHcGate", "createHcGame", "serverUrl", "gateUrl", "console", "log", "server", "json", "logger", "timeout", "flowClientApi", "flowAuth", "createWcGate", "isConnected", "disconnect", "wsc", "heartbeat", "interval", "heartbeat_interval", "heartbeat_timeout", "net", "flowClientMsg", "flowUserGameData", "gameUrl", "httpUrl", "createWscGame", "logNet", "client", "flows", "postApiReturnFlow", "push", "v", "return", "isSucc", "res", "updateType", "Object", "values", "includes", "dataManager", "getInstance", "processOptimizedResponse", "userGameData", "warn", "hc", "security", "preSendDataFlow", "data", "Uint8Array", "encrypt", "preRecvDataFlow", "decrypt", "preSendMsgFlow", "preRecvMsgFlow", "preCallApiFlow", "isTokenFreeAPI", "req", "logBusiness", "service", "type", "validateTokenBeforeRequest", "handleTokenExpired", "then", "loginSuccess", "newSsoToken", "storage", "get", "__ssoToken", "log<PERSON>arn", "Promise", "reject", "err", "code", "message", "ssoToken", "now", "Date", "existingTokenInfo", "SSOTokenInfo", "tokenInfo", "JSON", "parse", "lastAccessTime", "set", "stringify", "error", "undefined", "tokenData", "token", "createdTime", "expiredTime", "maxLifetime", "maxIdleTime", "remove", "validateToken", "tokenFreeAPIs", "apiType", "api", "isTokenFree", "SSOToken", "tokenInfoStr", "idleTime", "Math", "floor", "resolve", "setTimeout", "role", "loadDataInBackground", "hasNewToken", "handleTokenExpiredPublic"], "mappings": ";;;qRA8BaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvBJC,MAAAA,M,UAAAA,M;;AACcC,MAAAA,kB,iBAAdC,U;AAA8CC,MAAAA,gB,iBAAZC,Q;;AACpBC,MAAAA,kB,iBAAdH,U;AAA8CI,MAAAA,gB,iBAAZF,Q;;AAClCG,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACcC,MAAAA,c,iBAAAA,c;;AAEHC,MAAAA,gB,iBAAhBC,Y;;AAIgBC,MAAAA,gB,iBAAhBD,Y;;AAGKE,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,G,kBAAAA,G;;;;;;AA3BT;AACA;AACA;AACA;AACA;AACA;;;AAwBA;2BACapB,S,GAAN,MAAMA,SAAN,CAAgB;AAanBqB,QAAAA,WAAW,GAAG;AAZd;AAYc,eAXdC,MAWc,GAXsE,IAWtE;;AATd;AASc,eARdC,MAQc,GARsE,IAQtE;;AANd;AAMc,eALdC,MAKc,GALkE,IAKlE;;AAHd;AAGc,eAFdC,MAEc,GAFkE,IAElE;;AACV;AACA,eAAKC,YAAL;AAEA;;AACA,eAAKC,YAAL;AACH;;AAEDD,QAAAA,YAAY,GAAG;AACX;AACA,cAAME,SAAS,GAAG;AAAA;AAAA,4CAAaC,OAA/B;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCH,SAAhC;AAEA,eAAKN,MAAL,GAAc,KAAKrB,MAAM;AAAA;AAAA;AAAA;AAAA,sDAAX;AAAA;AAAA,oDAAyE;AACnF+B,YAAAA,MAAM,EAAEJ,SAD2E;AAEnFK,YAAAA,IAAI,EAAE;AAAA;AAAA,4CAAYA,IAFiE;AAGnFC,YAAAA,MAAM,EAAEJ,OAH2E;AAInF;AACAK,YAAAA,OAAO,EAAE,IAL0E,CAKpE;;AALoE,WAAzE,CAAd;AAOA,eAAKC,aAAL,CAAmB,KAAKd,MAAxB;AACA,eAAKe,QAAL,CAAc,KAAKf,MAAnB;AACH;;AACDgB,QAAAA,YAAY,CAACV,SAAD,EAAoB;AAC5B,cAAI,KAAKH,MAAL,IAAe,KAAKA,MAAL,CAAYc,WAA/B,EAA4C;AACxC,iBAAKd,MAAL,CAAYe,UAAZ;AACA,mBAAO,KAAKf,MAAZ;AACH,WAJ2B,CAK5B;;;AACA,cAAIgB,GAAG,GAAG,KAAKxC,MAAM;AAAA;AAAA;AAAA;AAAA,kDAAX;AAAA;AAAA,oDAAqE;AAC3E+B,YAAAA,MAAM,EAAEJ,SADmE;AAE3Ec,YAAAA,SAAS,EAAE;AACPC,cAAAA,QAAQ,EAAE;AAAA;AAAA,8CAAYC,kBADf;AAEPT,cAAAA,OAAO,EAAE;AAAA;AAAA,8CAAYU;AAFd,aAFgE;AAM3EZ,YAAAA,IAAI,EAAE;AAAA;AAAA,4CAAYA,IANyD,CAO3E;AACA;;AAR2E,WAArE,CAAV;AAUA;AAAA;AAAA,0BAAIa,GAAJ,CAAQrB,MAAR,GAAiBgB,GAAjB;AACA,eAAKM,aAAL,CAAmBN,GAAnB;AACA,eAAKJ,QAAL,CAAcI,GAAd;AACA,eAAKO,gBAAL,CAAsBP,GAAtB;AACA,iBAAOA,GAAP;AACH;AAED;;;AACAd,QAAAA,YAAY,GAAG;AACX;AACA,cAAMC,SAAS,GAAG;AAAA;AAAA,4CAAaqB,OAA/B;AAEAnB,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCH,SAAhC;AAEA,eAAKL,MAAL,GAAc,KAAKtB,MAAM;AAAA;AAAA;AAAA;AAAA,sDAAX;AAAA;AAAA,oDAAyE;AACnF+B,YAAAA,MAAM,EAAEJ,SAD2E;AAEnFK,YAAAA,IAAI,EAAE;AAAA;AAAA,4CAAYA,IAFiE;AAGnFC,YAAAA,MAAM,EAAEJ,OAH2E;AAInF;AACAK,YAAAA,OAAO,EAAE,IAL0E,CAKpE;;AALoE,WAAzE,CAAd,CANW,CAcX;;AACA;AAAA;AAAA,oDAAiBe,OAAjB,GAA2BtB,SAA3B;AAEA,eAAKQ,aAAL,CAAmB,KAAKb,MAAxB;AACA,eAAKc,QAAL,CAAc,KAAKd,MAAnB;AACA,eAAKyB,gBAAL,CAAsB,KAAKzB,MAA3B;AACH;AACD;AACJ;AACA;AACA;;;AACI4B,QAAAA,aAAa,GAAG;AACZ;AACA;AAAA;AAAA,4BAAKpB,GAAL,CAASqB,MAAT,CAAgB,+BAAhB,EAFY,CAIZ;AACA;;AAEA;AAAA;AAAA,4BAAKrB,GAAL,CAASqB,MAAT,CAAgB,6BAAhB;AACH;;AAEOJ,QAAAA,gBAAgB,CAACK,MAAD,EAAc;AAClC;AACAA,UAAAA,MAAM,CAACC,KAAP,CAAaC,iBAAb,CAA+BC,IAA/B,CAAoCC,CAAC,IAAI;AACrC,gBAAIA,CAAC,CAACC,MAAF,CAASC,MAAT,IAAmBF,CAAC,CAACC,MAAF,CAASE,GAAhC,EAAqC;AACjC,kBAAMA,GAAG,GAAGH,CAAC,CAACC,MAAF,CAASE,GAArB,CADiC,CAGjC;;AACA,kBAAIA,GAAG,CAACC,UAAJ,IAAkBC,MAAM,CAACC,MAAP;AAAA;AAAA,oDAA8BC,QAA9B,CAAuCJ,GAAG,CAACC,UAA3C,CAAtB,EAA8E;AAC1E,oBAAMI,WAAW,GAAG;AAAA;AAAA,gDAAYC,WAAZ,EAApB;AACAD,gBAAAA,WAAW,CAACE,wBAAZ,CAAqCP,GAArC;AACH,eAHD,CAIA;AACA;AALA,mBAMK,IAAIA,GAAG,CAACQ,YAAR,EAAsB;AACvBtC,gBAAAA,OAAO,CAACuC,IAAR,CAAa,qCAAb,EAAoDT,GAApD;AACH;AACJ;;AACD,mBAAOH,CAAP;AACH,WAhBD;AAiBH;AAED;;;AACQrB,QAAAA,aAAa,CAACkC,EAAD,EAAU;AAC3B,cAAI,CAAC;AAAA;AAAA,0CAAYC,QAAjB,EAA2B;AAE3BD,UAAAA,EAAE,CAAChB,KAAH,CAASkB,eAAT,CAAyBhB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASE,OAAT,CAAiBlB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD,EAH2B,CAU3B;;AACAa,UAAAA,EAAE,CAAChB,KAAH,CAASsB,eAAT,CAAyBpB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASI,OAAT,CAAiBpB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD;AAMH;AAED;;;AACQV,QAAAA,aAAa,CAACN,GAAD,EAAW;AAC5B,cAAI,CAAC;AAAA;AAAA,0CAAY8B,QAAjB,EAA2B,OADC,CAG5B;;AACA9B,UAAAA,GAAG,CAACa,KAAJ,CAAUwB,cAAV,CAAyBtB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASE,OAAT,CAAiBlB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD,EAJ4B,CAW5B;;AACAhB,UAAAA,GAAG,CAACa,KAAJ,CAAUyB,cAAV,CAAyBvB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASI,OAAT,CAAiBpB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD;AAMH;AAED;;;AACQpB,QAAAA,QAAQ,CAACgB,MAAD,EAAc;AAC1B;AACA;AACAA,UAAAA,MAAM,CAACC,KAAP,CAAa0B,cAAb,CAA4BxB,IAA5B,CAAiCC,CAAC,IAAI;AAClC;AACA,gBAAI,KAAKwB,cAAL,CAAoBxB,CAAC,CAACyB,GAAtB,CAAJ,EAAgC;AAC5B;AAAA;AAAA,gCAAKnD,GAAL,CAASoD,WAAT,uBAA+B1B,CAAC,CAACyB,GAAF,CAAME,OAArC,SAAgD3B,CAAC,CAACyB,GAAF,CAAMG,IAAtD;AACA,qBAAO5B,CAAP;AACH,aALiC,CAOlC;;;AACA,gBAAI,CAAC,KAAK6B,0BAAL,EAAL,EAAwC;AACpC;AAAA;AAAA,gCAAKvD,GAAL,CAASoD,WAAT,CAAqB,kCAArB,EADoC,CAGpC;;AACA,qBAAO,KAAKI,kBAAL,GAA0BC,IAA1B,CAA+BC,YAAY,IAAI;AAClD,oBAAIA,YAAJ,EAAkB;AACd;AAAA;AAAA,oCAAK1D,GAAL,CAASoD,WAAT,CAAqB,oBAArB,EADc,CAEd;;AACA,sBAAMO,WAAW,GAAG;AAAA;AAAA,oCAAKC,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAApB;;AACA,sBAAIF,WAAJ,EAAiB;AACbjC,oBAAAA,CAAC,CAACyB,GAAF,CAAMW,UAAN,GAAmBH,WAAnB;AACH;;AACD,yBAAOjC,CAAP,CAPc,CAOJ;AACb,iBARD,MAQO;AACH;AAAA;AAAA,oCAAK1B,GAAL,CAAS+D,OAAT,CAAiB,kBAAjB;AACA,yBAAOC,OAAO,CAACC,MAAR,CAAe;AAClBrC,oBAAAA,MAAM,EAAE,KADU;AAElBsC,oBAAAA,GAAG,EAAE;AAAEC,sBAAAA,IAAI,EAAE,eAAR;AAAyBC,sBAAAA,OAAO,EAAE;AAAlC;AAFa,mBAAf,CAAP;AAIH;AACJ,eAhBM,CAAP;AAiBH,aA7BiC,CA8BlC;;;AACA,gBAAMC,QAAQ,GAAG;AAAA;AAAA,8BAAKT,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAjB;;AACA,gBAAIQ,QAAJ,EAAc;AACV3C,cAAAA,CAAC,CAACyB,GAAF,CAAMW,UAAN,GAAmBO,QAAnB;AACH;;AACD,mBAAO3C,CAAP;AACH,WApCD,EAH0B,CAyC1B;;AACAJ,UAAAA,MAAM,CAACC,KAAP,CAAaC,iBAAb,CAA+BC,IAA/B,CAAoCC,CAAC,IAAI;AACrC,gBAAIA,CAAC,CAACC,MAAF,CAASC,MAAb,EAAqB;AACjB,kBAAMC,GAAG,GAAGH,CAAC,CAACC,MAAF,CAASE,GAArB,CADiB,CAGjB;;AACA,kBAAMyC,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ,CAJiB,CAMjB;;AACA,kBAAME,iBAAiB,GAAG;AAAA;AAAA,gCAAKZ,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,0DAAkBY,YAAnC,CAA1B;;AACA,kBAAID,iBAAJ,EAAuB;AACnB,oBAAI;AACA,sBAAME,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWJ,iBAAX,CAAlB;AACAE,kBAAAA,SAAS,CAACG,cAAV,GAA2BP,GAA3B,CAFA,CAEgC;;AAChC;AAAA;AAAA,oCAAKV,OAAL,CAAakB,GAAb,CAAiB;AAAA;AAAA,8DAAkBL,YAAnC,EAAiDE,IAAI,CAACI,SAAL,CAAeL,SAAf,CAAjD;AACH,iBAJD,CAIE,OAAOM,KAAP,EAAc;AACZjF,kBAAAA,OAAO,CAACuC,IAAR,CAAa,gBAAb,EAA+B0C,KAA/B;AACH;AACJ,eAhBgB,CAkBjB;;;AACA,kBAAInD,GAAG,CAACiC,UAAJ,KAAmBmB,SAAvB,EAAkC;AAC9B;AACA,oBAAMC,SAAS,GAAG;AACdC,kBAAAA,KAAK,EAAEtD,GAAG,CAACiC,UADG;AAEdsB,kBAAAA,WAAW,EAAEd,GAFC;AAGdO,kBAAAA,cAAc,EAAEP,GAHF;AAId;AACA;AACAe,kBAAAA,WAAW,EAAEf,GAAG,GAAG,MAAM,EAAN,GAAW,EAAX,GAAgB,IANrB;AAM2B;AACzCgB,kBAAAA,WAAW,EAAEhB,GAAG,GAAG,OAAO,EAAP,GAAY,EAAZ,GAAiB,IAPtB;AAO4B;AAC1CiB,kBAAAA,WAAW,EAAE,IAAI,EAAJ,GAAS,EAAT,GAAc,IARb,CAQmB;;AARnB,iBAAlB;AAUA;AAAA;AAAA,kCAAK3B,OAAL,CAAakB,GAAb,CAAiB,WAAjB,EAA8BjD,GAAG,CAACiC,UAAlC;AACA;AAAA;AAAA,kCAAKF,OAAL,CAAakB,GAAb,CAAiB,gBAAjB,EAAmCH,IAAI,CAACI,SAAL,CAAeG,SAAf,CAAnC;AACH;AACJ,aAlCD,CAmCA;AAnCA,iBAoCK,IAAIxD,CAAC,CAACC,MAAF,CAASuC,GAAT,CAAaC,IAAb,KAAsB,YAA1B,EAAwC;AACzC;AAAA;AAAA,gCAAKP,OAAL,CAAa4B,MAAb,CAAoB,WAApB;AACA;AAAA;AAAA,gCAAK5B,OAAL,CAAa4B,MAAb,CAAoB,gBAApB;AACH;;AACD,mBAAO9D,CAAP;AACH,WA1CD;AA2CH;AAED;AACJ;AACA;;;AACW+D,QAAAA,aAAa,GAAY;AAC5B,iBAAO,KAAKlC,0BAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACYL,QAAAA,cAAc,CAACC,GAAD,EAAoB;AACtC;AACA,cAAMuC,aAAa,GAAG,CAClB,UADkB,EACN;AACZ,iBAFkB,EAET;AACT,yBAHkB,EAGD;AACjB,sBAJkB,EAIJ;AACd,yBALkB,EAKD;AACjB,gBANkB,CAMV;AANU,WAAtB,CAFsC,CAWtC;;AACA,cAAMC,OAAO,GAAGxC,GAAG,CAACG,IAAJ,IAAYH,GAAG,CAACyC,GAAhB,IAAuB,EAAvC;AACA,cAAMC,WAAW,GAAGH,aAAa,CAACzD,QAAd,CAAuB0D,OAAvB,CAApB;;AAEA,cAAIE,WAAJ,EAAiB;AACb;AAAA;AAAA,8BAAK7F,GAAL,CAASoD,WAAT,uBAA+BuC,OAA/B;AACH;;AAED,iBAAOE,WAAP;AACH;AAED;AACJ;AACA;;;AACYtC,QAAAA,0BAA0B,GAAY;AAC1C,cAAI;AACA;AACA,gBAAMc,QAAQ,GAAG;AAAA;AAAA,8BAAKT,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,wDAAkBiC,QAAnC,CAAjB;;AACA,gBAAI,CAACzB,QAAL,EAAe;AACX,qBAAO,KAAP;AACH,aALD,CAOA;;;AACA,gBAAM0B,YAAY,GAAG;AAAA;AAAA,8BAAKnC,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,wDAAkBY,YAAnC,CAArB;;AACA,gBAAI,CAACsB,YAAL,EAAmB;AACf,qBAAO,KAAP;AACH;;AAED,gBAAMrB,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWmB,YAAX,CAAlB;AACA,gBAAMzB,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ,CAdA,CAgBA;;AACA,gBAAII,SAAS,CAACY,WAAV,IAAyBZ,SAAS,CAACY,WAAV,GAAwBhB,GAArD,EAA0D;AACtD;AAAA;AAAA,gCAAKtE,GAAL,CAASoD,WAAT,CAAqB,mBAArB;AACA,qBAAO,KAAP;AACH,aApBD,CAsBA;;;AACA,gBAAIsB,SAAS,CAACG,cAAV,IAA4BH,SAAS,CAACa,WAA1C,EAAuD;AACnD,kBAAMS,QAAQ,GAAG1B,GAAG,GAAGI,SAAS,CAACG,cAAjC;;AACA,kBAAImB,QAAQ,GAAGtB,SAAS,CAACa,WAAzB,EAAsC;AAClC;AAAA;AAAA,kCAAKvF,GAAL,CAASoD,WAAT,8DACuB6C,IAAI,CAACC,KAAL,CAAWF,QAAQ,GAAG,KAAtB,CADvB;AAGA,uBAAO,KAAP;AACH;AACJ,aA/BD,CAiCA;;;AACA,gBAAItB,SAAS,CAACW,WAAV,IAAyBX,SAAS,CAACW,WAAV,GAAwBf,GAArD,EAA0D;AACtD;AAAA;AAAA,gCAAKtE,GAAL,CAASoD,WAAT,CAAqB,aAArB;AACA,qBAAO,KAAP;AACH;;AAED,mBAAO,IAAP;AACH,WAxCD,CAwCE,OAAO4B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhF,GAAL,CAAS+D,OAAT,CAAiB,iBAAjB,EAAoCiB,KAApC;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACYxB,QAAAA,kBAAkB,GAAqB;AAC3C,iBAAO,IAAIQ,OAAJ,CAAYmC,OAAO,IAAI;AAC1B,gBAAI;AACA;AACA;AAAA;AAAA,gCAAKvC,OAAL,CAAa4B,MAAb,CAAoB;AAAA;AAAA,0DAAkBM,QAAtC;AACA;AAAA;AAAA,gCAAKlC,OAAL,CAAa4B,MAAb,CAAoB;AAAA;AAAA,0DAAkBf,YAAtC;AAEA;AAAA;AAAA,gCAAKzE,GAAL,CAASoD,WAAT,CAAqB,wBAArB,EALA,CAOA;;AACAgD,cAAAA,UAAU,iCAAC,aAAY;AACnB,oBAAI;AAAA;;AACA;AACA;AACA,iDAAM,SAAC;AAAA;AAAA,kCAAIC,IAAL,EAAkBC,oBAAxB,qBAAM,iCAAN;AACA;AAAA;AAAA,oCAAKtG,GAAL,CAASoD,WAAT,CAAqB,eAArB,EAJA,CAMA;;AACAgD,kBAAAA,UAAU,CAAC,MAAM;AACb,wBAAMG,WAAW,GAAG,CAAC,CAAC;AAAA;AAAA,sCAAK3C,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,gEAAkBiC,QAAnC,CAAtB;AACA;AAAA;AAAA,sCAAK9F,GAAL,CAASoD,WAAT,0DACkBmD,WAAW,GAAG,IAAH,GAAU,IADvC;AAGAJ,oBAAAA,OAAO,CAACI,WAAD,CAAP;AACH,mBANS,EAMP,IANO,CAAV,CAPA,CAaU;AACb,iBAdD,CAcE,OAAOvB,KAAP,EAAc;AACZ;AAAA;AAAA,oCAAKhF,GAAL,CAAS+D,OAAT,CAAiB,gBAAjB,EAAmCiB,KAAnC;AACAmB,kBAAAA,OAAO,CAAC,KAAD,CAAP;AACH;AACJ,eAnBS,GAmBP,GAnBO,CAAV;AAoBH,aA5BD,CA4BE,OAAOnB,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKhF,GAAL,CAAS+D,OAAT,CAAiB,iBAAjB,EAAoCiB,KAApC;AACAmB,cAAAA,OAAO,CAAC,KAAD,CAAP;AACH;AACJ,WAjCM,CAAP;AAkCH;AAED;AACJ;AACA;;;AACWK,QAAAA,wBAAwB,GAAS;AACpC,eAAKhD,kBAAL;AACH;;AApXkB,O", "sourcesContent": ["/*\n * @Author: dgflash\n * @Date: 2022-06-28 19:10:14\n * @LastEditors: dgflash\n * @LastEditTime: 2022-09-20 10:38:39\n */\n\nimport { WECHAT } from 'cc/env';\nimport { HttpClient as HttpClient_Browser, WsClient as WsClient_Browser } from 'tsrpc-browser';\nimport { HttpClient as HttpClient_Miniapp, WsClient as WsClient_Miniapp } from 'tsrpc-miniapp';\nimport { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { Security } from '../../tsrpc/models/Security';\nimport { ShareConfig } from '../../tsrpc/models/ShareConfig';\nimport { BaseResponse, DataUpdateType, OptimizedDataResponse } from '../../tsrpc/protocols/base';\nimport {\n    serviceProto as ServiceProtoGame,\n    ServiceType as ServiceTypeGame,\n} from '../../tsrpc/protocols/ServiceProtoGame';\nimport {\n    serviceProto as ServiceProtoGate,\n    ServiceType as ServiceTypeGate,\n} from '../../tsrpc/protocols/ServiceProtoGate';\nimport { ClientConfig } from './ClientConfig';\nimport { GameServerConfig } from './config/GameServerConfig';\nimport { GameStorageConfig } from './config/GameStorageConfig';\nimport { LocalConfig } from './config/LocalConfig';\nimport { DataManager } from './DataManager';\nimport { smc } from './SingletonModuleComp';\n\n/** TSRPC网络模块 */\nexport class CommonNet {\n    /** 连接网关服务器 Http 客户端 */\n    hcGate: HttpClient_Miniapp<ServiceTypeGate> | HttpClient_Browser<ServiceTypeGate> = null!;\n\n    /** 连接数据服务器 http 客户端 ，不用了*/\n    hcGame: HttpClient_Miniapp<ServiceTypeGame> | HttpClient_Browser<ServiceTypeGame> = null!;\n\n    /** 连接数据服务器 WebSocket 客户端 */\n    wcGame: WsClient_Miniapp<ServiceTypeGame> | WsClient_Browser<ServiceTypeGame> = null!;\n\n    /** 连接数据服务器 WebSocket 客户端 */\n    wcGate: WsClient_Miniapp<ServiceTypeGate> | WsClient_Browser<ServiceTypeGate> = null!;\n\n    constructor() {\n        /** 创建连接网关服务器 Http 客户端 */\n        this.createHcGate();\n\n        /** 🎯 纯HTTP架构：同时初始化游戏HTTP客户端 */\n        this.createHcGame();\n    }\n\n    createHcGate() {\n        // 🚪 网关客户端：连接端口5000，处理登录注册\n        const serverUrl = ClientConfig.gateUrl;\n        console.log('🚪 网关HTTP客户端初始化:', serverUrl);\n\n        this.hcGate = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGate, {\n            server: serverUrl,\n            json: ShareConfig.json,\n            logger: console,\n            // 🚀 优化：减少超时时间，加快失败响应\n            timeout: 5000, // 从默认10秒减少到5秒\n        });\n        this.flowClientApi(this.hcGate);\n        this.flowAuth(this.hcGate);\n    }\n    createWcGate(serverUrl: string) {\n        if (this.wcGate && this.wcGate.isConnected) {\n            this.wcGate.disconnect();\n            return this.wcGate;\n        }\n        // 创建客户端与游戏服务器的 WebSocket 连接\n        let wsc = new (WECHAT ? WsClient_Miniapp : WsClient_Browser)(ServiceProtoGate, {\n            server: serverUrl,\n            heartbeat: {\n                interval: LocalConfig.heartbeat_interval,\n                timeout: LocalConfig.heartbeat_timeout,\n            },\n            json: ShareConfig.json,\n            // logger: console,\n            // logMsg: true,\n        });\n        smc.net.wcGate = wsc;\n        this.flowClientMsg(wsc);\n        this.flowAuth(wsc);\n        this.flowUserGameData(wsc);\n        return wsc;\n    }\n\n    /** 创建连游戏服务器 Http 客户端 */\n    createHcGame() {\n        // 🎮 游戏客户端：连接端口5001，处理游戏逻辑\n        const serverUrl = ClientConfig.gameUrl;\n\n        console.log('🎮 游戏HTTP客户端初始化:', serverUrl);\n\n        this.hcGame = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGame, {\n            server: serverUrl,\n            json: ShareConfig.json,\n            logger: console,\n            // 🚀 优化：减少超时时间，加快失败响应\n            timeout: 5000, // 从默认10秒减少到5秒\n        });\n\n        // 🔧 同步更新GameServerConfig，保持一致性\n        GameServerConfig.httpUrl = serverUrl;\n\n        this.flowClientApi(this.hcGame);\n        this.flowAuth(this.hcGame);\n        this.flowUserGameData(this.hcGame);\n    }\n    /**\n     *  创建连接游戏服务器 Websocket 客户端\n     *  🎯 纯HTTP架构：此方法已弃用，保留用于向后兼容\n     */\n    createWscGame() {\n        // 🎯 纯HTTP架构：跳过WebSocket客户端创建\n        oops.log.logNet('🎯 纯HTTP架构：跳过WebSocket游戏客户端创建');\n\n        // 不设置wcGame，避免混淆\n        // smc.net.wcGame = null;\n\n        oops.log.logNet('✅ 纯HTTP架构：WebSocket游戏客户端已跳过');\n    }\n\n    private flowUserGameData(client: any) {\n        // 将 callApi 的结果返回给调用方之后，如果有携带用户数据，直接覆盖\n        client.flows.postApiReturnFlow.push(v => {\n            if (v.return.isSucc && v.return.res) {\n                const res = v.return.res;\n\n                // 使用新的数据管理器处理优化响应\n                if (res.updateType && Object.values(DataUpdateType).includes(res.updateType)) {\n                    const dataManager = DataManager.getInstance();\n                    dataManager.processOptimizedResponse(res as OptimizedDataResponse);\n                }\n                // 🗑️ 旧的全量数据响应已弃用，所有API已迁移到新格式\n                // 如果遇到没有updateType的响应，记录警告\n                else if (res.userGameData) {\n                    console.warn('[CommonNet] 检测到旧格式API响应，请升级API到新格式:', res);\n                }\n            }\n            return v;\n        });\n    }\n\n    /** HTTP 客户端协议数据加密、解密 */\n    private flowClientApi(hc: any) {\n        if (!ShareConfig.security) return;\n\n        hc.flows.preSendDataFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.encrypt(v.data);\n            }\n            return v;\n        });\n\n        // 在处理接收到的数据之前，通常要进行加密/解密\n        hc.flows.preRecvDataFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.decrypt(v.data);\n            }\n            return v;\n        });\n    }\n\n    /** WebSocket 客户端协议数据加密、解密 */\n    private flowClientMsg(wsc: any) {\n        if (!ShareConfig.security) return;\n\n        // 发送 Message 之前\n        wsc.flows.preSendMsgFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.encrypt(v.data);\n            }\n            return v;\n        });\n\n        // 触发 Message 监听事件之前\n        wsc.flows.preRecvMsgFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.decrypt(v.data);\n            }\n            return v;\n        });\n    }\n\n    /** 帐号登录令牌验证是否逻辑（帐号中加入登录令牌，服务器通过令牌解析玩家数据，如果存在就是已登录） */\n    private flowAuth(client: any) {\n        // HttpClient WsClient\n        // 🔧 执行 callApi 之前的token验证和插入\n        client.flows.preCallApiFlow.push(v => {\n            // 🔍 检查是否为无需token的API（白名单）\n            if (this.isTokenFreeAPI(v.req)) {\n                oops.log.logBusiness(`🔓 API ${v.req.service}/${v.req.type} 无需token验证`);\n                return v;\n            }\n\n            // 🔍 API调用前检查token有效性（主动检测挂机）\n            if (!this.validateTokenBeforeRequest()) {\n                oops.log.logBusiness('🔍 API调用前检测到token无效，尝试重新登录并重试...');\n\n                // 🔄 尝试重新登录并重试请求\n                return this.handleTokenExpired().then(loginSuccess => {\n                    if (loginSuccess) {\n                        oops.log.logBusiness('✅ 重新登录成功，重试原始请求...');\n                        // 重新添加token到请求\n                        const newSsoToken = oops.storage.get('SSO_TOKEN');\n                        if (newSsoToken) {\n                            v.req.__ssoToken = newSsoToken;\n                        }\n                        return v; // 继续执行原始请求\n                    } else {\n                        oops.log.logWarn('❌ 重新登录失败，阻止API调用');\n                        return Promise.reject({\n                            isSucc: false,\n                            err: { code: 'TOKEN_EXPIRED', message: '重新登录失败，请手动重新登录' },\n                        });\n                    }\n                });\n            }\n            // 请求前插入登录令牌\n            const ssoToken = oops.storage.get('SSO_TOKEN');\n            if (ssoToken) {\n                v.req.__ssoToken = ssoToken;\n            }\n            return v;\n        });\n\n        // 将 callApi 的结果返回给调用方之后将登录令牌存到本地（收到协议时将登录令牌存到本地）\n        client.flows.postApiReturnFlow.push(v => {\n            if (v.return.isSucc) {\n                const res = v.return.res as BaseResponse;\n\n                // 🔧 每次API成功响应时更新token信息\n                const now = Date.now();\n\n                // 更新现有token的访问时间（如果存在）\n                const existingTokenInfo = oops.storage.get(GameStorageConfig.SSOTokenInfo);\n                if (existingTokenInfo) {\n                    try {\n                        const tokenInfo = JSON.parse(existingTokenInfo);\n                        tokenInfo.lastAccessTime = now; // 🔧 关键：每次请求成功都更新访问时间\n                        oops.storage.set(GameStorageConfig.SSOTokenInfo, JSON.stringify(tokenInfo));\n                    } catch (error) {\n                        console.warn('更新token访问时间失败:', error);\n                    }\n                }\n\n                // 请求成功后刷新登录令牌（如果服务端返回了新token）\n                if (res.__ssoToken !== undefined) {\n                    // 🎮 三消游戏优化：与服务端新配置保持一致\n                    const tokenData = {\n                        token: res.__ssoToken,\n                        createdTime: now,\n                        lastAccessTime: now,\n                        // 服务端新配置：8小时基础 + 最多2次刷新 = 最长24小时\n                        // 客户端提前30分钟判断过期，避免边界情况\n                        expiredTime: now + 7.5 * 60 * 60 * 1000, // 7.5小时客户端过期\n                        maxLifetime: now + 23.5 * 60 * 60 * 1000, // 23.5小时最大生命周期\n                        maxIdleTime: 4 * 60 * 60 * 1000, // 4小时最大空闲时间\n                    };\n                    oops.storage.set('SSO_TOKEN', res.__ssoToken);\n                    oops.storage.set('SSO_TOKEN_INFO', JSON.stringify(tokenData));\n                }\n            }\n            // 登录令牌过期时删除客户端登录令牌（可跳转到登录界面）\n            else if (v.return.err.code === 'NEED_LOGIN') {\n                oops.storage.remove('SSO_TOKEN');\n                oops.storage.remove('SSO_TOKEN_INFO');\n            }\n            return v;\n        });\n    }\n\n    /**\n     * 🔍 验证token有效性（公共方法，供其他模块调用）\n     */\n    public validateToken(): boolean {\n        return this.validateTokenBeforeRequest();\n    }\n\n    /**\n     * � 检查是否为无需token的API（白名单）\n     */\n    private isTokenFreeAPI(req: any): boolean {\n        // 无需token的API白名单\n        const tokenFreeAPIs = [\n            'Register', // 注册\n            'Login', // 登录\n            'FacebookLogin', // Facebook登录\n            'GuestLogin', // 游客登录\n            'GetGameConfig', // 获取游戏配置\n            'Ping', // 心跳检测\n        ];\n\n        // 检查API类型\n        const apiType = req.type || req.api || '';\n        const isTokenFree = tokenFreeAPIs.includes(apiType);\n\n        if (isTokenFree) {\n            oops.log.logBusiness(`🔓 API ${apiType} 在白名单中，跳过token验证`);\n        }\n\n        return isTokenFree;\n    }\n\n    /**\n     * �🔍 API调用前验证token有效性（主动挂机检测）\n     */\n    private validateTokenBeforeRequest(): boolean {\n        try {\n            // 检查是否有token\n            const ssoToken = oops.storage.get(GameStorageConfig.SSOToken);\n            if (!ssoToken) {\n                return false;\n            }\n\n            // 检查token信息\n            const tokenInfoStr = oops.storage.get(GameStorageConfig.SSOTokenInfo);\n            if (!tokenInfoStr) {\n                return false;\n            }\n\n            const tokenInfo = JSON.parse(tokenInfoStr);\n            const now = Date.now();\n\n            // 检查最大生命周期\n            if (tokenInfo.maxLifetime && tokenInfo.maxLifetime < now) {\n                oops.log.logBusiness('🔍 token已超过最大生命周期');\n                return false;\n            }\n\n            // 检查空闲时间\n            if (tokenInfo.lastAccessTime && tokenInfo.maxIdleTime) {\n                const idleTime = now - tokenInfo.lastAccessTime;\n                if (idleTime > tokenInfo.maxIdleTime) {\n                    oops.log.logBusiness(\n                        `🔍 token空闲时间过长: ${Math.floor(idleTime / 60000)}分钟`\n                    );\n                    return false;\n                }\n            }\n\n            // 检查基础过期时间\n            if (tokenInfo.expiredTime && tokenInfo.expiredTime < now) {\n                oops.log.logBusiness('🔍 token已过期');\n                return false;\n            }\n\n            return true;\n        } catch (error) {\n            oops.log.logWarn('⚠️ token验证过程出错:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 🔧 处理token过期（触发重新登录）\n     */\n    private handleTokenExpired(): Promise<boolean> {\n        return new Promise(resolve => {\n            try {\n                // 清理本地token\n                oops.storage.remove(GameStorageConfig.SSOToken);\n                oops.storage.remove(GameStorageConfig.SSOTokenInfo);\n\n                oops.log.logBusiness('🔄 Token过期，触发后台重新登录...');\n\n                // 🔄 触发Role模块的强制完整登录流程\n                setTimeout(async () => {\n                    try {\n                        // 调用Role的私有方法需要通过反射或公共接口\n                        // 这里触发后台数据加载，它会自动检测到token无效并重新登录\n                        await (smc.role as any).loadDataInBackground?.();\n                        oops.log.logBusiness('✅ 后台重新登录流程已启动');\n\n                        // 等待一段时间让登录完成\n                        setTimeout(() => {\n                            const hasNewToken = !!oops.storage.get(GameStorageConfig.SSOToken);\n                            oops.log.logBusiness(\n                                `🔍 重新登录结果: ${hasNewToken ? '成功' : '失败'}`\n                            );\n                            resolve(hasNewToken);\n                        }, 2000); // 等待2秒让登录完成\n                    } catch (error) {\n                        oops.log.logWarn('⚠️ 启动后台重新登录失败:', error);\n                        resolve(false);\n                    }\n                }, 100);\n            } catch (error) {\n                oops.log.logWarn('⚠️ 处理token过期失败:', error);\n                resolve(false);\n            }\n        });\n    }\n\n    /**\n     * 🔧 公共方法：处理token过期（供外部调用）\n     */\n    public handleTokenExpiredPublic(): void {\n        this.handleTokenExpired();\n    }\n}\n"]}