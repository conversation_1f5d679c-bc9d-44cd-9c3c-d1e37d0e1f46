"use strict";
// ShareConfig.ts - dev分支配置 (个人开发环境)
// 根据分支自动确定环境配置，简化部署流程
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareConfig = exports.Platform = exports.Environment = void 0;
exports.validateConfig = validateConfig;
exports.printConfigInfo = printConfigInfo;
var Environment;
(function (Environment) {
    Environment["DEVELOPMENT"] = "development";
    Environment["FACEBOOK_MOCK"] = "facebook_mock";
    Environment["PRODUCTION_PERSONAL"] = "production_personal";
    Environment["PRODUCTION_FACEBOOK"] = "production_facebook";
})(Environment || (exports.Environment = Environment = {}));
var Platform;
(function (Platform) {
    Platform["PERSONAL"] = "personal";
    Platform["FACEBOOK"] = "facebook";
})(Platform || (exports.Platform = Platform = {}));
// 根据当前分支确定环境
function getCurrentEnvironment() {
    // dev 分支固定返回 DEVELOPMENT 环境
    return Environment.DEVELOPMENT;
}
// 🔧 dev分支专用：个人开发环境配置
const DEVELOPMENT_CONFIG = {
    environment: Environment.DEVELOPMENT,
    platform: Platform.PERSONAL,
    serverUrl: 'http://localhost:3000',
    clientUrl: 'http://localhost:7456',
    mongoUrl: 'mongodb://localhost:27017',
    mongoDbName: 'moyu_dev',
    port: 3000,
    isProduction: false,
    enableCors: true,
    corsOrigins: [
        'http://localhost:7456',
        'http://127.0.0.1:7456',
        'http://localhost:8080'
    ],
    logLevel: 'debug',
    enableFacebookSDK: false,
    enableAnalytics: false,
    maxPlayersPerRoom: 4,
    gameSettings: {
        enableRanking: true,
        enableRewards: true,
        enableSocialFeatures: false // 个人开发环境不启用社交功能
    },
    // 兼容属性
    https: false,
    gate: 'localhost:3000',
    httpPort: 3000,
    wsPort: 3000,
    gamePort: 3001,
    gameServerUrl: "",
    json: true,
    security: true,
    heartbeat_interval: 30000,
    heartbeat_timeout: 5000,
};
// 环境配置映射
const CONFIG_MAP = {
    [Environment.DEVELOPMENT]: DEVELOPMENT_CONFIG,
    [Environment.FACEBOOK_MOCK]: DEVELOPMENT_CONFIG, // 简化
    [Environment.PRODUCTION_PERSONAL]: DEVELOPMENT_CONFIG, // 简化
    [Environment.PRODUCTION_FACEBOOK]: DEVELOPMENT_CONFIG // 简化
};
// 获取当前配置 - dev分支专用个人开发配置
exports.ShareConfig = DEVELOPMENT_CONFIG;
// 配置验证
function validateConfig() {
    const config = exports.ShareConfig;
    if (!config.serverUrl || !config.clientUrl) {
        console.error('Missing required URL configuration');
        return false;
    }
    if (!config.mongoUrl || !config.mongoDbName) {
        console.error('Missing required MongoDB configuration');
        return false;
    }
    if (config.enableFacebookSDK && !config.facebookAppId) {
        console.error('Facebook SDK enabled but no App ID provided');
        return false;
    }
    return true;
}
// 打印当前配置信息
function printConfigInfo() {
    console.log('=== ShareConfig Information (dev) ===');
    console.log(`Environment: ${exports.ShareConfig.environment}`);
    console.log(`Platform: ${exports.ShareConfig.platform}`);
    console.log(`Server URL: ${exports.ShareConfig.serverUrl}`);
    console.log(`Client URL: ${exports.ShareConfig.clientUrl}`);
    console.log(`Database: ${exports.ShareConfig.mongoDbName}`);
    console.log(`Gateway Port: ${exports.ShareConfig.port} (登录注册)`);
    console.log(`Game Port: ${exports.ShareConfig.gamePort} (游戏逻辑)`);
    console.log(`Game Server URL: ${exports.ShareConfig.gameServerUrl}`);
    console.log(`Production: ${exports.ShareConfig.isProduction}`);
    console.log(`Facebook SDK: ${exports.ShareConfig.enableFacebookSDK}`);
    console.log('============================================================');
}
exports.default = exports.ShareConfig;
