"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserUtil = void 0;
exports.sanitizeUserDataTypes = sanitizeUserDataTypes;
const faker_1 = require("@faker-js/faker");
const bcrypt_1 = __importDefault(require("bcrypt"));
const mongodb_1 = require("mongodb");
const GameConst_1 = require("../../tsrpc/models/GameConst");
const ShareConfig_1 = require("../../tsrpc/models/ShareConfig");
const base_1 = require("../../tsrpc/protocols/base");
const IPUntil_1 = require("../3rdData/IPUntil");
const Account_1 = require("../account/Account");
const User_1 = require("../account/bll/User");
const Config_1 = require("../config/Config");
const Rank_1 = require("../rank/bll/Rank");
/** 用户工具 */
class UserUtil {
    static initPropData() {
        return {
            [base_1.PropType.PropsMoveOut]: {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: 1,
                propType: base_1.PropType.PropsMoveOut,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: 'Description for PropsMoveOut',
            },
            [base_1.PropType.PropsTips]: {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: 2,
                propType: base_1.PropType.PropsTips,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: 'Description for PropsTips',
            },
            [base_1.PropType.PropsReShuffle]: {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: 3,
                propType: base_1.PropType.PropsReShuffle,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: 'Description for PropsReShuffle',
            },
            [base_1.PropType.PropsRevive]: {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: 4,
                propType: base_1.PropType.PropsRevive,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: 'Description for PropsRevive',
            },
            [base_1.PropType.PropsExp]: {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: 5,
                propType: base_1.PropType.PropsExp,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: 'Description for PropsExp',
            },
            [base_1.PropType.PropsCoin]: {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: 6,
                propType: base_1.PropType.PropsCoin,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: 'Description for PropsCoin',
            },
            [base_1.PropType.PropsDayLeftCount]: {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: 7,
                propType: base_1.PropType.PropsDayLeftCount,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: 'Description for PropsDayLeftCount',
            },
        };
    }
    static initUserData(key, args) {
        let objId = new mongodb_1.ObjectId(args.guuid);
        return {
            _id: objId,
            key: key,
            userName: args.userName,
            openid: '',
            propUseData: this.initPropData(),
            createtime: new Date(),
            index: 0,
            recordData: {},
            passTimes: 0,
            currCountryPassTimes: 0,
            lastChangeCountryTime: new Date(),
            countryCode: args.countryCode,
            sex: base_1.SexType.Man,
            nickName: faker_1.faker.person.fullName({ sex: 'male' }),
            avatarId: 1,
            guuid: objId.toString(), // 生成10位的十六进制字符串
            platform: args.platform,
            platformType: args.platformType,
            passWord: bcrypt_1.default.hashSync(args.passWord, Config_1.Config.passwordSaltRounds),
            isNewPlayer: true,
            isGuest: args.isGuest,
            selfCountryRank: 0,
            lastStep: 0,
            googleUuid: '',
            facebookId: args.facebookId || '',
        };
    }
    /** 修复用户数据 ，如果数据不存在则创建 ，任何DbUser中的字段*/
    static fixUserData(userData) {
        // 当前版本无需数据修复，该方法为历史兼容预留
        return false;
    }
    static isValidObject(obj, prototype) {
        // 修改 T 的约束
        const prototypeKeys = Object.keys(prototype);
        const objKeys = Object.keys(obj);
        // 检查是否有多余的属性
        for (const key of objKeys) {
            if (!prototypeKeys.includes(key)) {
                return false; // 找到多余的属性
            }
        }
        // 检查是否符合接口
        for (const key of prototypeKeys) {
            if (!(key in obj)) {
                return false; // 缺少必要的属性
            }
        }
        return true; // 符合接口且没有多余属性
    }
    static updateRecordData(userData, propType, value, time, levelId) {
        if (propType == base_1.PropType.PropsDayLeftCount) {
            let timeStr = time ? time : new Date().toDateString();
            let keyType = base_1.RecordType.Level;
            // 初始化当日记录数据结构
            if (!userData.recordData[timeStr]) {
                userData.recordData[timeStr] = {
                    [keyType]: {
                        recordType: keyType,
                        value: 0,
                        lastUpdateTime: new Date(),
                        createTime: new Date(),
                        levelDetails: {}, // 🔧 新增：存储各关卡的详细挑战记录
                    },
                };
            }
            // 确保记录数据结构完整
            if (!userData.recordData[timeStr][keyType].levelDetails) {
                userData.recordData[timeStr][keyType].levelDetails = {};
            }
            // 更新总挑战次数
            userData.recordData[timeStr][keyType].value += Math.abs(value);
            userData.recordData[timeStr][keyType].lastUpdateTime = new Date();
            // 🔧 新增：记录具体关卡的挑战次数
            if (levelId && levelId > 0) {
                const levelKey = `level_${levelId}`;
                if (!userData.recordData[timeStr][keyType].levelDetails[levelKey]) {
                    userData.recordData[timeStr][keyType].levelDetails[levelKey] = {
                        levelId: levelId,
                        attempts: 0,
                        lastAttemptTime: new Date(),
                    };
                }
                userData.recordData[timeStr][keyType].levelDetails[levelKey].attempts +=
                    Math.abs(value);
                userData.recordData[timeStr][keyType].levelDetails[levelKey].lastAttemptTime =
                    new Date();
                console.log(`📊 记录关卡 ${levelId} 挑战次数: ${Math.abs(value)}, 总计: ${userData.recordData[timeStr][keyType].levelDetails[levelKey].attempts}`);
            }
        }
    }
    static updateRecordDataPass(userData, propType, timeStr) {
        if (propType == base_1.PropType.PropsDayLeftCount) {
            timeStr = timeStr ? timeStr : new Date().toDateString();
            let key = base_1.RecordType.Level;
            if (userData.recordData[timeStr][key]) {
                userData.recordData[timeStr][key].passTime = new Date();
            }
        }
    }
    static async saveUser(userData) {
        return false;
    }
    static isValidRecordData(recordData) {
        return (typeof recordData === 'object' &&
            !Array.isArray(recordData &&
                Object.keys(recordData).every(key => typeof recordData[key] === 'object')));
    }
    static onGamePass(userData, clientIP) {
        userData.passTimes += 1;
        userData.currCountryPassTimes += 1;
        // 更新国家排行榜
        Rank_1.Rank.UpdateCountryScore(userData);
        // 更新世界排行榜
        Rank_1.Rank.UpdateWorldScore(userData.countryCode);
        // 如果有IP信息，更新城市排行榜（Facebook环境下跳过，因为使用Facebook SDK地理信息）
        if (clientIP && ShareConfig_1.ShareConfig.platform !== 'facebook') {
            try {
                const locationInfo = IPUntil_1.IPUntil.getLocationInfoByIP(clientIP);
                if (locationInfo.cityCode) {
                    Rank_1.Rank.UpdateCityScore(userData, locationInfo.cityCode);
                }
            }
            catch (error) {
                console.error('更新城市排行榜失败:', error);
            }
        }
        else if (ShareConfig_1.ShareConfig.platform === 'facebook') {
            console.log('🌍 Facebook环境：跳过IP地理位置查询，城市排行榜使用Facebook地理信息');
        }
    }
    /** 更新用户排名 */
    static async updateUserRank(userData) {
        try {
            const rank = await Rank_1.Rank.getRankInCountry(userData.countryCode, userData.guuid);
            return rank || 0;
        }
        catch (error) {
            console.error('获取用户排名失败:', error);
            return userData.selfCountryRank || 0;
        }
    }
    static async saveUserToDatabase(key) {
        let user = Account_1.account.AccountModel.users.get(key);
        if (user) {
            User_1.User.updateUserData(user._id, user).then(res => {
                if (res) {
                    console.log(`保存用户${user.nickName}数据到数据库成功`);
                }
                else {
                    console.log(`保存用户${user.nickName}数据到数据库失败`, res);
                }
                Account_1.account.AccountModel.users.delete(key);
            });
        }
    }
}
exports.UserUtil = UserUtil;
/**
 * 处理用户数据中的日期字段类型转换
 * MongoDB返回的日期字段可能是字符串，需要转换为Date对象
 */
function sanitizeUserDataTypes(dbUser) {
    // 确保日期字段是Date类型
    const createtime = dbUser.createtime instanceof Date ? dbUser.createtime : new Date(dbUser.createtime);
    const lastChangeCountryTime = dbUser.lastChangeCountryTime instanceof Date
        ? dbUser.lastChangeCountryTime
        : new Date(dbUser.lastChangeCountryTime);
    // 🔧 重要修复：确保propUseData字段始终存在且格式正确
    let processedPropUseData = {};
    if (dbUser.propUseData &&
        typeof dbUser.propUseData === 'object' &&
        !Array.isArray(dbUser.propUseData)) {
        // 用户已有propUseData，处理日期字段类型转换
        for (const [key, propData] of Object.entries(dbUser.propUseData)) {
            const prop = propData;
            processedPropUseData[key] = {
                ...prop,
                lastResetTime: prop.lastResetTime instanceof Date
                    ? prop.lastResetTime
                    : new Date(prop.lastResetTime),
                getTime: prop.getTime instanceof Date ? prop.getTime : new Date(prop.getTime),
                lastUpdateTime: prop.lastUpdateTime instanceof Date
                    ? prop.lastUpdateTime
                    : new Date(prop.lastUpdateTime),
            };
        }
    }
    else {
        // 🔧 老用户数据修复：propUseData缺失或格式错误，使用默认初始化数据
        console.log(`[sanitizeUserDataTypes] 用户 ${dbUser.key} 的propUseData缺失或格式错误，重新初始化`);
        processedPropUseData = UserUtil.initPropData();
    }
    // 🔧 数据完整性检查：确保所有道具类型都存在
    const requiredPropTypes = [
        base_1.PropType.PropsMoveOut,
        base_1.PropType.PropsTips,
        base_1.PropType.PropsReShuffle,
        base_1.PropType.PropsRevive,
        base_1.PropType.PropsExp,
        base_1.PropType.PropsCoin,
        base_1.PropType.PropsDayLeftCount,
    ];
    for (const propType of requiredPropTypes) {
        if (!processedPropUseData[propType]) {
            console.log(`[sanitizeUserDataTypes] 用户 ${dbUser.key} 缺少道具类型 ${propType}，补充初始化`);
            processedPropUseData[propType] = {
                lastResetTime: new Date(),
                getTime: new Date(),
                propId: propType,
                propType: propType,
                amount: GameConst_1.GameConst.dayFreeLimtsUse,
                lastUpdateTime: new Date(),
                desc: `Description for prop ${propType}`,
            };
        }
    }
    // 处理recordData中的日期字段
    const processedRecordData = {};
    if (dbUser.recordData) {
        for (const [timeKey, typeData] of Object.entries(dbUser.recordData)) {
            const processedTypeData = {};
            for (const [typeKey, record] of Object.entries(typeData)) {
                const rec = record;
                processedTypeData[typeKey] = {
                    ...rec,
                    lastUpdateTime: rec.lastUpdateTime instanceof Date
                        ? rec.lastUpdateTime
                        : new Date(rec.lastUpdateTime),
                    passTime: rec.passTime
                        ? rec.passTime instanceof Date
                            ? rec.passTime
                            : new Date(rec.passTime)
                        : undefined,
                    createTime: rec.createTime instanceof Date ? rec.createTime : new Date(rec.createTime),
                    // 🔧 新增：处理levelDetails中的日期字段
                    levelDetails: rec.levelDetails
                        ? Object.fromEntries(Object.entries(rec.levelDetails).map(([levelKey, levelData]) => [
                            levelKey,
                            {
                                ...levelData,
                                lastAttemptTime: levelData.lastAttemptTime instanceof Date
                                    ? levelData.lastAttemptTime
                                    : new Date(levelData.lastAttemptTime),
                            },
                        ]))
                        : undefined,
                };
            }
            processedRecordData[timeKey] = processedTypeData;
        }
    }
    const result = {
        ...dbUser,
        createtime: createtime,
        lastChangeCountryTime: lastChangeCountryTime,
        propUseData: processedPropUseData,
        recordData: processedRecordData,
        // 确保facebookId字段存在，如果不存在则设为空字符串
        facebookId: dbUser.facebookId || '',
        // 确保googleUuid字段存在，如果不存在则设为空字符串
        googleUuid: dbUser.googleUuid || '',
    };
    console.log(`[sanitizeUserDataTypes] 用户 ${dbUser.key} 数据处理完成，propUseData包含 ${Object.keys(processedPropUseData).length} 个道具类型`);
    return result;
}
