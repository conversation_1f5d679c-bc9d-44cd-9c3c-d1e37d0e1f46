/*
 * @Author: dgflash
 * @Date: 2022-06-28 19:10:14
 * @LastEditors: dgflash
 * @LastEditTime: 2022-09-20 10:38:39
 */

import { WECHAT } from 'cc/env';
import { HttpClient as HttpClient_Browser, WsClient as WsClient_Browser } from 'tsrpc-browser';
import { HttpClient as HttpClient_Miniapp, WsClient as WsClient_Miniapp } from 'tsrpc-miniapp';
import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { Security } from '../../tsrpc/models/Security';
import { ShareConfig } from '../../tsrpc/models/ShareConfig';
import { BaseResponse, DataUpdateType, OptimizedDataResponse } from '../../tsrpc/protocols/base';
import {
    serviceProto as ServiceProtoGame,
    ServiceType as ServiceTypeGame,
} from '../../tsrpc/protocols/ServiceProtoGame';
import {
    serviceProto as ServiceProtoGate,
    ServiceType as ServiceTypeGate,
} from '../../tsrpc/protocols/ServiceProtoGate';
import { ClientConfig } from './ClientConfig';
import { GameServerConfig } from './config/GameServerConfig';
import { GameStorageConfig } from './config/GameStorageConfig';
import { LocalConfig } from './config/LocalConfig';
import { DataManager } from './DataManager';
import { smc } from './SingletonModuleComp';

/** TSRPC网络模块 */
export class CommonNet {
    /** 连接网关服务器 Http 客户端 */
    hcGate: HttpClient_Miniapp<ServiceTypeGate> | HttpClient_Browser<ServiceTypeGate> = null!;

    /** 连接数据服务器 http 客户端 ，不用了*/
    hcGame: HttpClient_Miniapp<ServiceTypeGame> | HttpClient_Browser<ServiceTypeGame> = null!;

    /** 连接数据服务器 WebSocket 客户端 */
    wcGame: WsClient_Miniapp<ServiceTypeGame> | WsClient_Browser<ServiceTypeGame> = null!;

    /** 连接数据服务器 WebSocket 客户端 */
    wcGate: WsClient_Miniapp<ServiceTypeGate> | WsClient_Browser<ServiceTypeGate> = null!;

    constructor() {
        /** 创建连接网关服务器 Http 客户端 */
        this.createHcGate();

        /** 🎯 纯HTTP架构：同时初始化游戏HTTP客户端 */
        this.createHcGame();
    }

    createHcGate() {
        // 🚪 网关客户端：连接端口5000，处理登录注册
        const serverUrl = ClientConfig.gateUrl;
        console.log('🚪 网关HTTP客户端初始化:', serverUrl);

        this.hcGate = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGate, {
            server: serverUrl,
            json: ShareConfig.json,
            logger: console,
            // 🚀 优化：减少超时时间，加快失败响应
            timeout: 5000, // 从默认10秒减少到5秒
        });
        this.flowClientApi(this.hcGate);
        this.flowAuth(this.hcGate);
    }
    createWcGate(serverUrl: string) {
        if (this.wcGate && this.wcGate.isConnected) {
            this.wcGate.disconnect();
            return this.wcGate;
        }
        // 创建客户端与游戏服务器的 WebSocket 连接
        let wsc = new (WECHAT ? WsClient_Miniapp : WsClient_Browser)(ServiceProtoGate, {
            server: serverUrl,
            heartbeat: {
                interval: LocalConfig.heartbeat_interval,
                timeout: LocalConfig.heartbeat_timeout,
            },
            json: ShareConfig.json,
            // logger: console,
            // logMsg: true,
        });
        smc.net.wcGate = wsc;
        this.flowClientMsg(wsc);
        this.flowAuth(wsc);
        this.flowUserGameData(wsc);
        return wsc;
    }

    /** 创建连游戏服务器 Http 客户端 */
    createHcGame() {
        // 🎮 游戏客户端：连接端口5001，处理游戏逻辑
        const serverUrl = ClientConfig.gameUrl;

        console.log('🎮 游戏HTTP客户端初始化:', serverUrl);

        this.hcGame = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGame, {
            server: serverUrl,
            json: ShareConfig.json,
            logger: console,
            // 🚀 优化：减少超时时间，加快失败响应
            timeout: 5000, // 从默认10秒减少到5秒
        });

        // 🔧 同步更新GameServerConfig，保持一致性
        GameServerConfig.httpUrl = serverUrl;

        this.flowClientApi(this.hcGame);
        this.flowAuth(this.hcGame);
        this.flowUserGameData(this.hcGame);
    }
    /**
     *  创建连接游戏服务器 Websocket 客户端
     *  🎯 纯HTTP架构：此方法已弃用，保留用于向后兼容
     */
    createWscGame() {
        // 🎯 纯HTTP架构：跳过WebSocket客户端创建
        oops.log.logNet('🎯 纯HTTP架构：跳过WebSocket游戏客户端创建');

        // 不设置wcGame，避免混淆
        // smc.net.wcGame = null;

        oops.log.logNet('✅ 纯HTTP架构：WebSocket游戏客户端已跳过');
    }

    private flowUserGameData(client: any) {
        // 将 callApi 的结果返回给调用方之后，如果有携带用户数据，直接覆盖
        client.flows.postApiReturnFlow.push(v => {
            if (v.return.isSucc && v.return.res) {
                const res = v.return.res;

                // 使用新的数据管理器处理优化响应
                if (res.updateType && Object.values(DataUpdateType).includes(res.updateType)) {
                    const dataManager = DataManager.getInstance();
                    dataManager.processOptimizedResponse(res as OptimizedDataResponse);
                }
                // 🗑️ 旧的全量数据响应已弃用，所有API已迁移到新格式
                // 如果遇到没有updateType的响应，记录警告
                else if (res.userGameData) {
                    console.warn('[CommonNet] 检测到旧格式API响应，请升级API到新格式:', res);
                }
            }
            return v;
        });
    }

    /** HTTP 客户端协议数据加密、解密 */
    private flowClientApi(hc: any) {
        if (!ShareConfig.security) return;

        hc.flows.preSendDataFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.encrypt(v.data);
            }
            return v;
        });

        // 在处理接收到的数据之前，通常要进行加密/解密
        hc.flows.preRecvDataFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.decrypt(v.data);
            }
            return v;
        });
    }

    /** WebSocket 客户端协议数据加密、解密 */
    private flowClientMsg(wsc: any) {
        if (!ShareConfig.security) return;

        // 发送 Message 之前
        wsc.flows.preSendMsgFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.encrypt(v.data);
            }
            return v;
        });

        // 触发 Message 监听事件之前
        wsc.flows.preRecvMsgFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.decrypt(v.data);
            }
            return v;
        });
    }

    /** 帐号登录令牌验证是否逻辑（帐号中加入登录令牌，服务器通过令牌解析玩家数据，如果存在就是已登录） */
    private flowAuth(client: any) {
        // HttpClient WsClient
        // 🔧 执行 callApi 之前的token验证和插入
        client.flows.preCallApiFlow.push(v => {
            // 🔍 检查是否为无需token的API（白名单）
            if (this.isTokenFreeAPI(v.req)) {
                oops.log.logBusiness(`🔓 API ${v.req.service}/${v.req.type} 无需token验证`);
                return v;
            }

            // 🔍 API调用前检查token有效性（主动检测挂机）
            if (!this.validateTokenBeforeRequest()) {
                oops.log.logBusiness('🔍 API调用前检测到token无效，尝试重新登录并重试...');

                // 🔄 尝试重新登录并重试请求
                return this.handleTokenExpired().then(loginSuccess => {
                    if (loginSuccess) {
                        oops.log.logBusiness('✅ 重新登录成功，重试原始请求...');
                        // 重新添加token到请求
                        const newSsoToken = oops.storage.get('SSO_TOKEN');
                        if (newSsoToken) {
                            v.req.__ssoToken = newSsoToken;
                        }
                        return v; // 继续执行原始请求
                    } else {
                        oops.log.logWarn('❌ 重新登录失败，阻止API调用');
                        return Promise.reject({
                            isSucc: false,
                            err: { code: 'TOKEN_EXPIRED', message: '重新登录失败，请手动重新登录' },
                        });
                    }
                });
            }
            // 请求前插入登录令牌
            const ssoToken = oops.storage.get('SSO_TOKEN');
            if (ssoToken) {
                v.req.__ssoToken = ssoToken;
            }
            return v;
        });

        // 将 callApi 的结果返回给调用方之后将登录令牌存到本地（收到协议时将登录令牌存到本地）
        client.flows.postApiReturnFlow.push(v => {
            if (v.return.isSucc) {
                const res = v.return.res as BaseResponse;

                // 🔧 每次API成功响应时更新token信息
                const now = Date.now();

                // 更新现有token的访问时间（如果存在）
                const existingTokenInfo = oops.storage.get(GameStorageConfig.SSOTokenInfo);
                if (existingTokenInfo) {
                    try {
                        const tokenInfo = JSON.parse(existingTokenInfo);
                        tokenInfo.lastAccessTime = now; // 🔧 关键：每次请求成功都更新访问时间
                        oops.storage.set(GameStorageConfig.SSOTokenInfo, JSON.stringify(tokenInfo));
                    } catch (error) {
                        console.warn('更新token访问时间失败:', error);
                    }
                }

                // 请求成功后刷新登录令牌（如果服务端返回了新token）
                if (res.__ssoToken !== undefined) {
                    // 🎮 三消游戏优化：与服务端新配置保持一致
                    const tokenData = {
                        token: res.__ssoToken,
                        createdTime: now,
                        lastAccessTime: now,
                        // 服务端新配置：8小时基础 + 最多2次刷新 = 最长24小时
                        // 客户端提前30分钟判断过期，避免边界情况
                        expiredTime: now + 7.5 * 60 * 60 * 1000, // 7.5小时客户端过期
                        maxLifetime: now + 23.5 * 60 * 60 * 1000, // 23.5小时最大生命周期
                        maxIdleTime: 4 * 60 * 60 * 1000, // 4小时最大空闲时间
                    };
                    oops.storage.set('SSO_TOKEN', res.__ssoToken);
                    oops.storage.set('SSO_TOKEN_INFO', JSON.stringify(tokenData));
                }
            }
            // 登录令牌过期时删除客户端登录令牌（可跳转到登录界面）
            else if (v.return.err.code === 'NEED_LOGIN') {
                oops.storage.remove('SSO_TOKEN');
                oops.storage.remove('SSO_TOKEN_INFO');
            }
            return v;
        });
    }

    /**
     * 🔍 验证token有效性（公共方法，供其他模块调用）
     */
    public validateToken(): boolean {
        return this.validateTokenBeforeRequest();
    }

    /**
     * � 检查是否为无需token的API（白名单）
     */
    private isTokenFreeAPI(req: any): boolean {
        // 无需token的API白名单
        const tokenFreeAPIs = [
            'Register', // 注册
            'Login', // 登录
            'FacebookLogin', // Facebook登录
            'GuestLogin', // 游客登录
            'GetGameConfig', // 获取游戏配置
            'Ping', // 心跳检测
        ];

        // 检查API类型
        const apiType = req.service || req.type || req.api || '';
        const isTokenFree = tokenFreeAPIs.includes(apiType);

        if (isTokenFree) {
            oops.log.logBusiness(`🔓 API ${apiType} 在白名单中，跳过token验证`);
        } else {
            oops.log.logBusiness(
                `🔒 API ${apiType} 需要token验证 (service: ${req.service}, type: ${req.type})`
            );
        }

        return isTokenFree;
    }

    /**
     * �🔍 API调用前验证token有效性（主动挂机检测）
     */
    private validateTokenBeforeRequest(): boolean {
        try {
            // 检查是否有token
            const ssoToken = oops.storage.get(GameStorageConfig.SSOToken);
            if (!ssoToken) {
                return false;
            }

            // 检查token信息
            const tokenInfoStr = oops.storage.get(GameStorageConfig.SSOTokenInfo);
            if (!tokenInfoStr) {
                return false;
            }

            const tokenInfo = JSON.parse(tokenInfoStr);
            const now = Date.now();

            // 检查最大生命周期
            if (tokenInfo.maxLifetime && tokenInfo.maxLifetime < now) {
                oops.log.logBusiness('🔍 token已超过最大生命周期');
                return false;
            }

            // 检查空闲时间
            if (tokenInfo.lastAccessTime && tokenInfo.maxIdleTime) {
                const idleTime = now - tokenInfo.lastAccessTime;
                if (idleTime > tokenInfo.maxIdleTime) {
                    oops.log.logBusiness(
                        `🔍 token空闲时间过长: ${Math.floor(idleTime / 60000)}分钟`
                    );
                    return false;
                }
            }

            // 检查基础过期时间
            if (tokenInfo.expiredTime && tokenInfo.expiredTime < now) {
                oops.log.logBusiness('🔍 token已过期');
                return false;
            }

            return true;
        } catch (error) {
            oops.log.logWarn('⚠️ token验证过程出错:', error);
            return false;
        }
    }

    /**
     * 🔧 处理token过期（触发重新登录）
     */
    private handleTokenExpired(): Promise<boolean> {
        return new Promise(resolve => {
            try {
                // 清理本地token
                oops.storage.remove(GameStorageConfig.SSOToken);
                oops.storage.remove(GameStorageConfig.SSOTokenInfo);

                oops.log.logBusiness('🔄 Token过期，触发后台重新登录...');

                // 🔄 触发Role模块的强制完整登录流程
                setTimeout(async () => {
                    try {
                        // 调用Role的私有方法需要通过反射或公共接口
                        // 这里触发后台数据加载，它会自动检测到token无效并重新登录
                        await (smc.role as any).loadDataInBackground?.();
                        oops.log.logBusiness('✅ 后台重新登录流程已启动');

                        // 等待一段时间让登录完成
                        setTimeout(() => {
                            const hasNewToken = !!oops.storage.get(GameStorageConfig.SSOToken);
                            oops.log.logBusiness(
                                `🔍 重新登录结果: ${hasNewToken ? '成功' : '失败'}`
                            );
                            resolve(hasNewToken);
                        }, 2000); // 等待2秒让登录完成
                    } catch (error) {
                        oops.log.logWarn('⚠️ 启动后台重新登录失败:', error);
                        resolve(false);
                    }
                }, 100);
            } catch (error) {
                oops.log.logWarn('⚠️ 处理token过期失败:', error);
                resolve(false);
            }
        });
    }

    /**
     * 🔧 公共方法：处理token过期（供外部调用）
     */
    public handleTokenExpiredPublic(): void {
        this.handleTokenExpired();
    }
}
