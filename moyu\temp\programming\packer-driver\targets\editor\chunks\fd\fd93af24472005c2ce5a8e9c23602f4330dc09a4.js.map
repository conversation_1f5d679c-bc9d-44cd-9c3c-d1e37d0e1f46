{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/tsrpc/models/ShareConfig.ts"], "names": ["getCurrentEnvironment", "Environment", "DEVELOPMENT", "validateConfig", "config", "ShareConfig", "serverUrl", "console", "error", "mongoUrl", "mongoDbName", "enableFacebookSDK", "facebookAppId", "printConfigInfo", "log", "environment", "platform", "port", "gamePort", "gameServerUrl", "isProduction", "Platform", "DEVELOPMENT_CONFIG", "PERSONAL", "cors<PERSON><PERSON><PERSON>", "https", "gate", "json", "security", "CONFIG_MAP", "FACEBOOK_MOCK", "PRODUCTION_PERSONAL", "PRODUCTION_FACEBOOK"], "mappings": ";;;;;AAsCA;AACA,WAASA,qBAAT,GAA8C;AAC1C;AACA,WAAOC,WAAW,CAACC,WAAnB;AACH,G,CAED;;;AAkCA;AACO,WAASC,cAAT,GAAmC;AACtC,UAAMC,MAAM,GAAGC,WAAf;;AAEA,QAAI,CAACD,MAAM,CAACE,SAAZ,EAAuB;AACnBC,MAAAA,OAAO,CAACC,KAAR,CAAc,oCAAd;AACA,aAAO,KAAP;AACH;;AAED,QAAI,CAACJ,MAAM,CAACK,QAAR,IAAoB,CAACL,MAAM,CAACM,WAAhC,EAA6C;AACzCH,MAAAA,OAAO,CAACC,KAAR,CAAc,wCAAd;AACA,aAAO,KAAP;AACH;;AAED,QAAIJ,MAAM,CAACO,iBAAP,IAA4B,CAACP,MAAM,CAACQ,aAAxC,EAAuD;AACnDL,MAAAA,OAAO,CAACC,KAAR,CAAc,6CAAd;AACA,aAAO,KAAP;AACH;;AAED,WAAO,IAAP;AACH,G,CAED;;;AACO,WAASK,eAAT,GAAiC;AACpCN,IAAAA,OAAO,CAACO,GAAR,CAAY,uCAAZ;AACAP,IAAAA,OAAO,CAACO,GAAR,CAAa,gBAAeT,WAAW,CAACU,WAAY,EAApD;AACAR,IAAAA,OAAO,CAACO,GAAR,CAAa,aAAYT,WAAW,CAACW,QAAS,EAA9C;AACAT,IAAAA,OAAO,CAACO,GAAR,CAAa,eAAcT,WAAW,CAACC,SAAU,EAAjD;AACAC,IAAAA,OAAO,CAACO,GAAR,CAAa,aAAYT,WAAW,CAACK,WAAY,EAAjD;AACAH,IAAAA,OAAO,CAACO,GAAR,CAAa,iBAAgBT,WAAW,CAACY,IAAK,SAA9C;AACAV,IAAAA,OAAO,CAACO,GAAR,CAAa,cAAaT,WAAW,CAACa,QAAS,SAA/C;AACAX,IAAAA,OAAO,CAACO,GAAR,CAAa,oBAAmBT,WAAW,CAACc,aAAc,EAA1D;AACAZ,IAAAA,OAAO,CAACO,GAAR,CAAa,eAAcT,WAAW,CAACe,YAAa,EAApD;AACAb,IAAAA,OAAO,CAACO,GAAR,CAAa,iBAAgBT,WAAW,CAACM,iBAAkB,EAA3D;AACAJ,IAAAA,OAAO,CAACO,GAAR,CAAY,8DAAZ;AACH;;;oBAlCeX,c;qBAsBAU;;;;;;;;;;;;AArGhB;AACA;6BAEYZ,W,0BAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;eAAAA,W;;;0BAOAoB,Q,0BAAAA,Q;AAAAA,QAAAA,Q;AAAAA,QAAAA,Q;eAAAA,Q;;;AAmCNC,MAAAA,kB,GAAmC;AACrCP,QAAAA,WAAW,EAAEd,WAAW,CAACC,WADY;AAErCc,QAAAA,QAAQ,EAAEK,QAAQ,CAACE,QAFkB;AAGrCjB,QAAAA,SAAS,EAAE,uBAH0B;AAIrCG,QAAAA,QAAQ,EAAE,2BAJ2B;AAKrCC,QAAAA,WAAW,EAAE,UALwB;AAMrCO,QAAAA,IAAI,EAAE,IAN+B;AAOrCG,QAAAA,YAAY,EAAE,KAPuB;AAQrCI,QAAAA,WAAW,EAAE,CAAC,uBAAD,EAA0B,uBAA1B,EAAmD,uBAAnD,CARwB;AASrCb,QAAAA,iBAAiB,EAAE,KATkB;AAWrC;AACAc,QAAAA,KAAK,EAAE,KAZ8B;AAarCC,QAAAA,IAAI,EAAE,gBAb+B;AAcrCC,QAAAA,IAAI,EAAE,IAd+B;AAerCC,QAAAA,QAAQ,EAAE,IAf2B;AAiBrC;AACAV,QAAAA,QAAQ,EAAE,IAlB2B;AAmBrCC,QAAAA,aAAa,EAAE;AAnBsB,O,EAsBzC;;AACMU,MAAAA,U,GAAgD;AAClD,SAAC5B,WAAW,CAACC,WAAb,GAA2BoB,kBADuB;AAElD,SAACrB,WAAW,CAAC6B,aAAb,GAA6BR,kBAFqB;AAED;AACjD,SAACrB,WAAW,CAAC8B,mBAAb,GAAmCT,kBAHe;AAGK;AACvD,SAACrB,WAAW,CAAC+B,mBAAb,GAAmCV,kBAJe,CAIK;;AAJL,O,EAOtD;;6BACajB,W,GAA4BiB,kB;;yBAuC1BjB,W", "sourcesContent": ["// ShareConfig.ts - dev分支配置 (个人开发环境)\n// 根据分支自动确定环境配置，简化部署流程\n\nexport enum Environment {\n    DEVELOPMENT = 'development',\n    FACEBOOK_MOCK = 'facebook_mock',\n    PRODUCTION_PERSONAL = 'production_personal',\n    PRODUCTION_FACEBOOK = 'production_facebook',\n}\n\nexport enum Platform {\n    PERSONAL = 'personal',\n    FACEBOOK = 'facebook',\n}\n\nexport interface ServerConfig {\n    environment: Environment;\n    platform: Platform;\n    serverUrl: string;\n    mongoUrl: string;\n    mongoDbName: string;\n    port: number;\n    isProduction: boolean;\n    corsOrigins: string[];\n    enableFacebookSDK: boolean;\n    facebookAppId?: string;\n\n    // 核心协议配置\n    https: boolean;\n    gate: string;\n    json: boolean;\n    security: boolean;\n\n    // 双端口HTTP架构支持\n    gamePort: number;\n    gameServerUrl: string;\n}\n\n// 根据当前分支确定环境\nfunction getCurrentEnvironment(): Environment {\n    // dev 分支固定返回 DEVELOPMENT 环境\n    return Environment.DEVELOPMENT;\n}\n\n// 🔧 dev分支专用：个人开发环境配置\nconst DEVELOPMENT_CONFIG: ServerConfig = {\n    environment: Environment.DEVELOPMENT,\n    platform: Platform.PERSONAL,\n    serverUrl: 'http://localhost:3000',\n    mongoUrl: 'mongodb://localhost:27017',\n    mongoDbName: 'moyu_dev',\n    port: 3000,\n    isProduction: false,\n    corsOrigins: ['http://localhost:7456', 'http://127.0.0.1:7456', 'http://localhost:8080'],\n    enableFacebookSDK: false,\n\n    // 核心协议配置\n    https: false,\n    gate: 'localhost:3000',\n    json: true,\n    security: true,\n\n    // 双端口HTTP架构支持\n    gamePort: 3001,\n    gameServerUrl: 'http://localhost:3001',\n};\n\n// 环境配置映射\nconst CONFIG_MAP: Record<Environment, ServerConfig> = {\n    [Environment.DEVELOPMENT]: DEVELOPMENT_CONFIG,\n    [Environment.FACEBOOK_MOCK]: DEVELOPMENT_CONFIG, // 简化\n    [Environment.PRODUCTION_PERSONAL]: DEVELOPMENT_CONFIG, // 简化\n    [Environment.PRODUCTION_FACEBOOK]: DEVELOPMENT_CONFIG, // 简化\n};\n\n// 获取当前配置 - dev分支专用个人开发配置\nexport const ShareConfig: ServerConfig = DEVELOPMENT_CONFIG;\n\n// 配置验证\nexport function validateConfig(): boolean {\n    const config = ShareConfig;\n\n    if (!config.serverUrl) {\n        console.error('Missing required URL configuration');\n        return false;\n    }\n\n    if (!config.mongoUrl || !config.mongoDbName) {\n        console.error('Missing required MongoDB configuration');\n        return false;\n    }\n\n    if (config.enableFacebookSDK && !config.facebookAppId) {\n        console.error('Facebook SDK enabled but no App ID provided');\n        return false;\n    }\n\n    return true;\n}\n\n// 打印当前配置信息\nexport function printConfigInfo(): void {\n    console.log('=== ShareConfig Information (dev) ===');\n    console.log(`Environment: ${ShareConfig.environment}`);\n    console.log(`Platform: ${ShareConfig.platform}`);\n    console.log(`Server URL: ${ShareConfig.serverUrl}`);\n    console.log(`Database: ${ShareConfig.mongoDbName}`);\n    console.log(`Gateway Port: ${ShareConfig.port} (登录注册)`);\n    console.log(`Game Port: ${ShareConfig.gamePort} (游戏逻辑)`);\n    console.log(`Game Server URL: ${ShareConfig.gameServerUrl}`);\n    console.log(`Production: ${ShareConfig.isProduction}`);\n    console.log(`Facebook SDK: ${ShareConfig.enableFacebookSDK}`);\n    console.log('============================================================');\n}\n\nexport default ShareConfig;\n"]}