"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TTL = exports.GameConst = void 0;
/**
 * 客户端和游戏服务器共用的常量
 */
class GameConst {
}
exports.GameConst = GameConst;
GameConst.dayFreeLimts = 9999; // 每天可以玩多少次
GameConst.dayFreeLimtsUse = 500; // 每天可以用多少次道具（测试值：5个）
GameConst.usePropCd = 2000; //道具冷却时间(毫秒)
GameConst.pickIntervalClick = 0; //点击物品的间隔 毫秒
GameConst.defaultCountryCode = 'Other'; // 国家代码
GameConst.newPlayerDefaultProps = {
    moveOut: 1, // 移出道具默认数量
    tips: 1, // 提示道具默认数量
    reShuffle: 1, // 洗牌道具默认数量
    revive: 1, // 复活道具默认数量
};
var TTL;
(function (TTL) {
    TTL[TTL["None"] = 0] = "None";
    TTL[TTL["OneHour"] = 3600] = "OneHour";
    TTL[TTL["OneDay"] = 86400] = "OneDay";
    TTL[TTL["OneDayAndHalf"] = 129600] = "OneDayAndHalf";
    TTL[TTL["OneWeek"] = 604800] = "OneWeek";
    TTL[TTL["OneWeekHalf"] = 302400] = "OneWeekHalf";
    TTL[TTL["OneMonth"] = 2592000] = "OneMonth";
    TTL[TTL["OneMonthHalf"] = 1296000] = "OneMonthHalf";
})(TTL || (exports.TTL = TTL = {}));
