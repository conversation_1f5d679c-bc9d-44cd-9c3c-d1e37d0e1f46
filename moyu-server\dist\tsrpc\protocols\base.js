"use strict";
//** 这里是服务端和客户端可能都用到的数据结构 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.JustAuthPlatformType = exports.DisconnectReason = exports.SceneType = exports.PropType = exports.RecordType = exports.GameStatusType = exports.SceneItemType = exports.BundleName = exports.RankType = exports.SexType = exports.DataUpdateType = void 0;
/** 数据更新类型枚举 */
var DataUpdateType;
(function (DataUpdateType) {
    DataUpdateType["PROP"] = "PROP";
    DataUpdateType["PROGRESS"] = "PROGRESS";
    DataUpdateType["BASIC_INFO"] = "BASIC_INFO";
    DataUpdateType["RECORD"] = "RECORD";
    DataUpdateType["FULL"] = "FULL";
})(DataUpdateType || (exports.DataUpdateType = DataUpdateType = {}));
var SexType;
(function (SexType) {
    SexType[SexType["None"] = 1] = "None";
    SexType[SexType["Man"] = 2] = "Man";
    SexType[SexType["Woman"] = 3] = "Woman";
})(SexType || (exports.SexType = SexType = {}));
var RankType;
(function (RankType) {
    RankType[RankType["None"] = 0] = "None";
    RankType[RankType["Friend"] = 1] = "Friend";
    RankType[RankType["Country"] = 2] = "Country";
    RankType[RankType["World"] = 3] = "World";
    RankType[RankType["City"] = 4] = "City";
})(RankType || (exports.RankType = RankType = {}));
var BundleName;
(function (BundleName) {
})(BundleName || (exports.BundleName = BundleName = {}));
var SceneItemType;
(function (SceneItemType) {
    SceneItemType[SceneItemType["NONE"] = 0] = "NONE";
    SceneItemType[SceneItemType["Foods"] = 1] = "Foods";
    SceneItemType[SceneItemType["FoodsExtra"] = 2] = "FoodsExtra";
    SceneItemType[SceneItemType["Max"] = 3] = "Max";
})(SceneItemType || (exports.SceneItemType = SceneItemType = {}));
var GameStatusType;
(function (GameStatusType) {
    GameStatusType[GameStatusType["Pause"] = 0] = "Pause";
    GameStatusType[GameStatusType["Start"] = 1] = "Start";
    GameStatusType[GameStatusType["Win"] = 2] = "Win";
    GameStatusType[GameStatusType["Fail"] = 3] = "Fail";
    GameStatusType[GameStatusType["End"] = 4] = "End";
})(GameStatusType || (exports.GameStatusType = GameStatusType = {}));
var RecordType;
(function (RecordType) {
    RecordType[RecordType["None"] = 0] = "None";
    RecordType[RecordType["Level"] = 1] = "Level";
    RecordType[RecordType["Max"] = 2] = "Max";
})(RecordType || (exports.RecordType = RecordType = {}));
// 把所有可以增加或者减少的都做成道具。
var PropType;
(function (PropType) {
    PropType[PropType["PropsNone"] = 0] = "PropsNone";
    PropType[PropType["PropsMoveOut"] = 1] = "PropsMoveOut";
    PropType[PropType["PropsTips"] = 2] = "PropsTips";
    PropType[PropType["PropsReShuffle"] = 3] = "PropsReShuffle";
    PropType[PropType["PropsRevive"] = 4] = "PropsRevive";
    PropType[PropType["PropsExp"] = 5] = "PropsExp";
    PropType[PropType["PropsCoin"] = 6] = "PropsCoin";
    PropType[PropType["PropsDayLeftCount"] = 7] = "PropsDayLeftCount";
})(PropType || (exports.PropType = PropType = {}));
var SceneType;
(function (SceneType) {
    SceneType[SceneType["NONE"] = 0] = "NONE";
    SceneType[SceneType["Guide"] = 1] = "Guide";
    SceneType[SceneType["Hall"] = 2] = "Hall";
    SceneType[SceneType["Foods"] = 3] = "Foods";
    SceneType[SceneType["FoodsExtra"] = 4] = "FoodsExtra";
})(SceneType || (exports.SceneType = SceneType = {}));
var DisconnectReason;
(function (DisconnectReason) {
    DisconnectReason[DisconnectReason["None"] = 0] = "None";
    DisconnectReason["ReLogin"] = "ReLogin";
    DisconnectReason["ServerRestart"] = "ServerRestart";
    DisconnectReason["Kick"] = "Kick";
    DisconnectReason["NewVersion"] = "NewVersion";
    DisconnectReason["NetworkError"] = "NetworkError";
})(DisconnectReason || (exports.DisconnectReason = DisconnectReason = {}));
var JustAuthPlatformType;
(function (JustAuthPlatformType) {
    JustAuthPlatformType["google"] = "google";
})(JustAuthPlatformType || (exports.JustAuthPlatformType = JustAuthPlatformType = {}));
