"use strict";
/*
 * 创建测试用户脚本
 */
Object.defineProperty(exports, "__esModule", { value: true });
const MongoDB_1 = require("./module/common/MongoDB");
const User_1 = require("./module/account/bll/User");
const UserService_1 = require("./module/account/bll/UserService");
async function createTestUser() {
    try {
        console.log('🔍 连接数据库...');
        await MongoDB_1.MongoDB.init();
        User_1.User.init();
        console.log('✅ 数据库连接成功');
        // 检查测试用户是否已存在
        const existingUser = await User_1.User.getUserByUserName('testuser');
        if (existingUser) {
            console.log('✅ 测试用户已存在');
            console.log('用户信息:', {
                userName: existingUser.userName,
                guuid: existingUser.guuid,
                key: existingUser.key
            });
            return existingUser;
        }
        console.log('📝 创建测试用户...');
        // 创建测试用户
        const result = await UserService_1.UserService.registerUser(false, // 不是游客
        false, // 不是第三方登录
        '127.0.0.1', // IP地址
        'PC', // 平台
        'direct', // 平台类型
        undefined, // guuid (自动生成)
        'testuser', // 用户名
        'testpass123' // 密码
        );
        if (result) {
            console.log('✅ 测试用户创建成功!');
            console.log('用户信息:', {
                userName: result.userName,
                guuid: result.guuid
            });
            console.log('📝 可以使用以下信息登录:');
            console.log('   用户名: testuser');
            console.log('   密码: testpass123');
            return result;
        }
        else {
            console.log('❌ 测试用户创建失败');
            return null;
        }
    }
    catch (error) {
        console.error('💥 创建测试用户出错:', error);
        return null;
    }
    finally {
        // 关闭数据库连接
        process.exit(0);
    }
}
// 运行脚本
if (require.main === module) {
    createTestUser();
}
