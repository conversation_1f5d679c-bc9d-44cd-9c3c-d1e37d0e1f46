"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ApiGameGenTestRankData;
const Rank_1 = require("../../../module/rank/bll/Rank");
async function ApiGameGenTestRankData(call) {
    const { rankType, identifier, count = 50 } = call.req;
    if (!['country', 'city', 'world'].includes(rankType)) {
        return call.error('无效的排行榜类型', { code: 'INVALID_TYPE' });
    }
    if (!identifier) {
        return call.error('标识符不能为空', { code: 'MISSING_IDENTIFIER' });
    }
    if (count <= 0 || count > 1000) {
        return call.error('数量必须在1-1000之间', { code: 'INVALID_COUNT' });
    }
    try {
        console.log(`[TestRankData] 开始生成 ${rankType} 排行榜测试数据: ${identifier}, 数量: ${count}`);
        const generatedCount = await Rank_1.Rank.generateTestData(rankType, identifier, count);
        const response = {
            code: 0,
            message: '测试数据生成成功',
            generatedCount: generatedCount,
            rankType: rankType,
            identifier: identifier
        };
        console.log(`[TestRankData] 测试数据生成完成: ${generatedCount} 条`);
        return call.succ(response);
    }
    catch (error) {
        console.error('生成测试数据失败:', error);
        return call.error('生成测试数据失败', { code: 'GENERATION_FAILED' });
    }
}
