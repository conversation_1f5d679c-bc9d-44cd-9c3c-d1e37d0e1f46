"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiFacebookUserStats = ApiFacebookUserStats;
const User_1 = require("../../../module/account/bll/User");
async function ApiFacebookUserStats(call) {
    console.log('📊 [FacebookUserStats] 收到Facebook用户统计请求');
    try {
        // 简单的管理员验证（生产环境应该使用更安全的方式）
        const adminKey = call.req.adminKey;
        if (adminKey !== 'moyu_admin_2024') {
            console.log('❌ [FacebookUserStats] 管理员密钥验证失败');
            return call.error('unauthorized');
        }
        console.log('🔍 [FacebookUserStats] 开始查询Facebook用户数据...');
        // 获取所有Facebook用户
        const allFacebookUsers = await User_1.User.getAllFacebookUsers();
        console.log(`📋 [FacebookUserStats] 找到 ${allFacebookUsers.length} 个Facebook用户`);
        // 计算今日新增用户
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayNewUsers = allFacebookUsers.filter(user => user.createtime >= today).length;
        // 获取最近10个用户
        const recentUsers = allFacebookUsers
            .sort((a, b) => b.createtime.getTime() - a.createtime.getTime())
            .slice(0, 10)
            .map(user => ({
            key: user.key,
            guuid: user.guuid,
            userName: user.userName,
            nickName: user.nickName,
            facebookId: user.facebookId,
            countryCode: user.countryCode,
            platform: user.platform,
            passTimes: user.passTimes,
            createTime: user.createtime,
            lastLoginTime: user.createtime // 暂时使用创建时间，后续可以添加最后登录时间字段
        }));
        // 统计国家分布
        const countryMap = new Map();
        allFacebookUsers.forEach(user => {
            const country = user.countryCode || 'Unknown';
            const existing = countryMap.get(country) || { userCount: 0, totalPassTimes: 0 };
            existing.userCount++;
            existing.totalPassTimes += user.passTimes || 0;
            countryMap.set(country, existing);
        });
        const countryStats = Array.from(countryMap.entries())
            .map(([country, stats]) => ({
            country,
            userCount: stats.userCount,
            totalPassTimes: stats.totalPassTimes
        }))
            .sort((a, b) => b.userCount - a.userCount);
        console.log('📊 [FacebookUserStats] 统计结果:', {
            总用户数: allFacebookUsers.length,
            今日新增: todayNewUsers,
            国家分布: countryStats.length + '个国家',
            最近用户: recentUsers.length + '个'
        });
        // 详细打印最近用户信息
        console.log('👥 [FacebookUserStats] 最近Facebook用户:');
        recentUsers.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.nickName} (${user.facebookId}) - ${user.countryCode} - 通关${user.passTimes}次`);
        });
        // 详细打印国家统计
        console.log('🌍 [FacebookUserStats] 国家分布统计:');
        countryStats.forEach((stat, index) => {
            console.log(`   ${index + 1}. ${stat.country}: ${stat.userCount}用户, 总通关${stat.totalPassTimes}次`);
        });
        const response = {
            code: 0,
            message: 'Facebook用户统计获取成功',
            totalFacebookUsers: allFacebookUsers.length,
            todayNewUsers: todayNewUsers,
            recentUsers: recentUsers,
            countryStats: countryStats
        };
        console.log('✅ [FacebookUserStats] 统计完成，返回数据');
        call.succ(response);
    }
    catch (error) {
        console.error('💥 [FacebookUserStats] 获取统计数据失败:', error.message);
        call.error('get_stats_failed');
    }
}
