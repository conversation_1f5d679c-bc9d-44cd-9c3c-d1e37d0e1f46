"use strict";
/*
 * @Author: dgflash
 * @Date: 2022-07-12 15:29:32
 * @LastEditors: dgflash
 * @LastEditTime: 2022-07-12 16:11:48
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const mongodb_1 = require("mongodb");
const MongoDB_1 = require("../../common/MongoDB");
const UserUtil_1 = require("../../common/UserUtil");
/** 用户数据表逻辑 */
class User {
    static init() {
        this.c = MongoDB_1.MongoDB.db.collection(MongoDB_1.DbCollectionName.user);
    }
    /**
     * 注册新玩家
     * @param InitUserDataArgs
     * @returns 玩家自增唯一编号
     */
    static async addUser(args) {
        var key = await MongoDB_1.MongoDB.getNextSequenceValue(MongoDB_1.DbCollectionName.user);
        // 插入用户数据
        let userData = UserUtil_1.UserUtil.initUserData(key, args);
        const insertResult = await this.c.insertOne(userData);
        // 检查插入是否成功
        if (!insertResult.acknowledged) {
            throw new Error("用户插入失败");
        }
        // 返回客户端结果
        return userData.guuid;
    }
    /**
     * 通过玩家名删除玩家数据
     * @param userName  玩家名
     * @returns 是否删除成功
     */
    static async delUserByUserName(userName) {
        var ret = await this.c.deleteOne({ userName: userName });
        return ret.acknowledged && ret.deletedCount > 0;
    }
    /**
      * 删除所有玩家数据
      * @returns 是否删除成功
      */
    static async delUserByAll() {
        var ret = await this.c.deleteMany({});
        return ret.acknowledged && ret.deletedCount > 0;
    }
    /**
     * 通过账号名获取玩家数据
     * @param userName  玩家名
     * @returns 玩家数据
     */
    static async getUserByUserName(userName) {
        var dUser = await this.c.findOne({ userName: userName });
        if (dUser) {
            let hadFix = UserUtil_1.UserUtil.fixUserData(dUser);
            if (hadFix) {
                await this.c.updateOne({ _id: dUser._id }, { $set: dUser });
            }
        }
        return dUser;
    }
    static async getUserByGoogleUuid(googleUuid) {
        if (googleUuid == '') {
            return null;
        }
        return await this.c.findOne({ googleUuid: googleUuid });
    }
    /**
     * 通过Facebook ID获取玩家数据
     * @param facebookId Facebook用户ID
     * @returns 玩家数据
     */
    static async getUserByFacebookId(facebookId) {
        if (facebookId == '') {
            return null;
        }
        return await this.c.findOne({ facebookId: facebookId });
    }
    /**
 * 通过openid获取玩家数据
 * @param openid  玩家名
 * @returns 玩家数据
 */
    static async getUserByOpenid(openid) {
        return await this.c.findOne({ openid: openid });
    }
    static async getUserByKey(key) {
        return await this.c.findOne({ key: key });
    }
    static async getUserByGuuid(guuid) {
        return await this.c.findOne({ guuid: guuid });
    }
    /** 修改玩家数据 */
    static async updateUserData(_id, userData) {
        // 确保_id是正确的ObjectId对象
        let queryId;
        if (_id instanceof mongodb_1.ObjectId) {
            queryId = _id;
        }
        else if (typeof _id === 'string') {
            queryId = new mongodb_1.ObjectId(_id);
        }
        else {
            console.error(`[User.updateUserData] 无效的_id类型: ${typeof _id}`);
            return false;
        }
        const updateFields = {};
        // 遍历 userData 对象，构建更新字段
        Object.keys(userData).forEach(key => {
            if (key !== '_id' && userData[key] !== undefined) {
                updateFields[key] = userData[key];
            }
        });
        try {
            // 执行更新操作
            const result = await this.c.updateOne({ _id: queryId }, { $set: updateFields });
            const success = result.acknowledged && result.matchedCount > 0;
            return success;
        }
        catch (error) {
            console.error(`[User.updateUserData] MongoDB更新异常:`, error);
            return false;
        }
    }
    /**
     * 获取所有Facebook用户
     * @returns 所有Facebook用户列表
     */
    static async getAllFacebookUsers() {
        try {
            // 查询所有有facebookId字段且不为空的用户
            const facebookUsers = await this.c.find({
                facebookId: { $exists: true, $ne: '' }
            }).toArray();
            console.log(`📊 [User.getAllFacebookUsers] 查询到 ${facebookUsers.length} 个Facebook用户`);
            return facebookUsers;
        }
        catch (error) {
            console.error('❌ [User.getAllFacebookUsers] 查询Facebook用户失败:', error);
            return [];
        }
    }
}
exports.User = User;
User.c = null;
