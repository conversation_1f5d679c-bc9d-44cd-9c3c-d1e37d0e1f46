"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiFacebookScoreValidation = ApiFacebookScoreValidation;
async function ApiFacebookScoreValidation(call) {
    console.log('🔒 [FacebookScoreValidation] 收到分数验证请求');
    try {
        const { score, level, playTime, clientTimestamp } = call.req;
        console.log('📊 [FacebookScoreValidation] 验证数据:', {
            score,
            level,
            playTime,
            clientTimestamp,
            serverTime: Date.now()
        });
        // 获取用户信息（通过SSO Token）
        // 这里需要实现SSO Token验证逻辑
        // 1. 基础数据合理性检查
        const validationDetails = {
            scoreCheck: false,
            timeCheck: false,
            levelCheck: false,
            rateCheck: false
        };
        // 分数范围检查
        validationDetails.scoreCheck = score >= 0 && score <= 100000;
        // 游戏时间检查（最少10秒，最多30分钟）
        validationDetails.timeCheck = playTime >= 10 && playTime <= 1800;
        // 关卡检查
        validationDetails.levelCheck = level >= 1 && level <= 1000;
        // 分数获取速率检查（每秒最多100分）
        const maxScoreRate = playTime * 100;
        validationDetails.rateCheck = score <= maxScoreRate;
        // 2. 高级验证：与历史数据对比
        const isAdvancedValid = await validateAdvancedChecks(score, level, playTime);
        // 3. 综合判断
        const basicValid = Object.values(validationDetails).every(check => check);
        const isValid = basicValid && isAdvancedValid;
        let adjustedScore = score;
        // 如果分数不合理，进行调整
        if (!isValid) {
            adjustedScore = Math.min(score, maxScoreRate, level * 1000);
            console.log('⚠️ [FacebookScoreValidation] 分数异常，调整分数:', {
                原始分数: score,
                调整分数: adjustedScore,
                验证详情: validationDetails
            });
        }
        const response = {
            code: 0,
            message: isValid ? '分数验证通过' : '分数验证失败，已调整',
            isValid,
            adjustedScore: isValid ? undefined : adjustedScore,
            validationDetails
        };
        console.log('✅ [FacebookScoreValidation] 验证完成:', response);
        call.succ(response);
    }
    catch (error) {
        console.error('💥 [FacebookScoreValidation] 验证失败:', error.message);
        call.error('validation_failed');
    }
}
/**
 * 高级验证检查
 */
async function validateAdvancedChecks(score, level, playTime) {
    try {
        // 1. 检查分数与关卡的合理性
        const maxScoreForLevel = level * 2000; // 每关最多2000分
        if (score > maxScoreForLevel) {
            console.log('🚨 [AdvancedValidation] 分数超出关卡限制:', { score, level, maxScore: maxScoreForLevel });
            return false;
        }
        // 2. 检查游戏时间的合理性
        const minTimeForLevel = level * 30; // 每关至少30秒
        const maxTimeForLevel = level * 300; // 每关最多5分钟
        if (playTime < minTimeForLevel || playTime > maxTimeForLevel) {
            console.log('🚨 [AdvancedValidation] 游戏时间异常:', { playTime, level, minTime: minTimeForLevel, maxTime: maxTimeForLevel });
            return false;
        }
        // 3. 检查分数增长速率
        const scorePerSecond = score / playTime;
        if (scorePerSecond > 100) { // 每秒最多100分
            console.log('🚨 [AdvancedValidation] 分数增长速率异常:', { scorePerSecond });
            return false;
        }
        // 4. 可以添加更多检查...
        // - 与玩家历史最高分对比
        // - 与同等级其他玩家对比
        // - 检查游戏行为模式
        return true;
    }
    catch (error) {
        console.error('❌ [AdvancedValidation] 高级验证失败:', error);
        return false;
    }
}
