"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const MongoDB_1 = require("../module/common/MongoDB");
const RedisManager_1 = require("../module/common/RedisManager");
const Rank_1 = require("../module/rank/bll/Rank");
async function testRankingLogic() {
    console.log('🧪 开始测试排行榜逻辑...');
    try {
        // 首先初始化MongoDB
        console.log('🔌 初始化数据库连接...');
        await MongoDB_1.MongoDB.init();
        // 初始化Redis连接
        console.log('🔌 初始化Redis连接...');
        try {
            await RedisManager_1.RedisManager.init();
            // 等待Redis连接就绪
            let retryCount = 0;
            const maxRetries = 5;
            while (!RedisManager_1.RedisManager.isReady() && retryCount < maxRetries) {
                console.log(`⏳ 等待Redis连接就绪... (${retryCount + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                retryCount++;
            }
            if (!RedisManager_1.RedisManager.isReady()) {
                console.warn('⚠️ Redis连接超时，但继续运行...');
            }
        }
        catch (redisError) {
            console.warn('⚠️ Redis连接失败，但继续运行:', redisError);
        }
        // 然后初始化排行榜
        console.log('🏆 初始化排行榜系统...');
        Rank_1.Rank.init();
        console.log('\n📊 测试数据准备...');
        // 创建测试用户数据
        const testUsers = [
            // 中国用户
            { countryCode: 'CN', cityCode: 'sz', cityName: '深圳', passTimes: 10, nickName: '深圳玩家1' },
            { countryCode: 'CN', cityCode: 'sz', cityName: '深圳', passTimes: 8, nickName: '深圳玩家2' },
            { countryCode: 'CN', cityCode: 'gz', cityName: '广州', passTimes: 12, nickName: '广州玩家1' },
            { countryCode: 'CN', cityCode: 'bj', cityName: '北京', passTimes: 15, nickName: '北京玩家1' },
            // 美国用户
            { countryCode: 'US', cityCode: 'nyc', cityName: 'New York', passTimes: 20, nickName: 'NYC Player1' },
            { countryCode: 'US', cityCode: 'la', cityName: 'Los Angeles', passTimes: 18, nickName: 'LA Player1' },
            // 日本用户
            { countryCode: 'JP', cityCode: 'tokyo', cityName: '东京', passTimes: 25, nickName: '东京玩家1' },
        ];
        // 更新用户数据到各个排行榜
        for (const user of testUsers) {
            console.log(`📝 处理用户: ${user.nickName} (${user.countryCode}-${user.cityCode})`);
            // 创建模拟用户对象
            const mockUser = {
                guuid: `test_${user.nickName}`,
                countryCode: user.countryCode,
                nickName: user.nickName,
                avatarId: 1,
                avatar: '',
                currCountryPassTimes: user.passTimes
            };
            // 1. 更新国家排行榜 (玩家数据)
            await Rank_1.Rank.UpdateCountryScore(mockUser);
            // 2. 更新城市排行榜 (玩家数据)
            await Rank_1.Rank.UpdateCityScore(mockUser, user.cityCode);
            // 3. 更新国家内城市排行榜 (城市聚合数据)
            await Rank_1.Rank.UpdateCountryCityScore(user.countryCode, user.cityCode, user.cityName);
        }
        // 4. 更新世界排行榜 (国家聚合数据)
        const countries = ['CN', 'US', 'JP'];
        for (const country of countries) {
            await Rank_1.Rank.UpdateWorldScore(country);
        }
        console.log('\n🔍 测试排行榜查询...');
        // 测试世界排行榜 (应该显示国家代码)
        console.log('\n=== 世界排行榜 (各国家比拼) ===');
        const worldRanking = await Rank_1.Rank.getWorldList();
        worldRanking.forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        // 测试中国国家排行榜 (应该显示玩家名字)
        console.log('\n=== 中国国家排行榜 (本国玩家比拼) ===');
        const cnCountryRanking = await Rank_1.Rank.getCountryList('CN');
        cnCountryRanking.forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        // 测试中国城市排行榜 (应该显示城市名字)
        console.log('\n=== 中国城市排行榜 (本国城市比拼) ===');
        const cnCityRanking = await Rank_1.Rank.getCountryCityList('CN');
        cnCityRanking.forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        // 测试深圳城市排行榜 (应该显示深圳玩家)
        console.log('\n=== 深圳城市排行榜 (深圳玩家比拼) ===');
        const szCityRanking = await Rank_1.Rank.getCityList('sz');
        szCityRanking.forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        console.log('\n✅ 排行榜逻辑测试完成！');
    }
    catch (error) {
        console.error('❌ 测试失败:', error);
    }
}
// 运行测试
testRankingLogic().then(() => {
    console.log('🎉 测试脚本执行完成');
    process.exit(0);
}).catch(error => {
    console.error('💥 测试脚本执行失败:', error);
    process.exit(1);
});
