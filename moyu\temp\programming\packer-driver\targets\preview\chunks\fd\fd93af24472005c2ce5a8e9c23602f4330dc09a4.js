System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, Environment, Platform, DEVELOPMENT_CONFIG, CONFIG_MAP, ShareConfig;

  // 配置验证
  function validateConfig() {
    var config = ShareConfig;

    if (!config.serverUrl) {
      console.error('Missing required URL configuration');
      return false;
    }

    if (!config.mongoUrl || !config.mongoDbName) {
      console.error('Missing required MongoDB configuration');
      return false;
    }

    if (config.enableFacebookSDK && !config.facebookAppId) {
      console.error('Facebook SDK enabled but no App ID provided');
      return false;
    }

    return true;
  } // 打印当前配置信息


  function printConfigInfo() {
    console.log('=== ShareConfig Information (dev) ===');
    console.log("Environment: " + ShareConfig.environment);
    console.log("Platform: " + ShareConfig.platform);
    console.log("Server URL: " + ShareConfig.serverUrl);
    console.log("Database: " + ShareConfig.mongoDbName);
    console.log("Gateway Port: " + ShareConfig.port + " (\u767B\u5F55\u6CE8\u518C)");
    console.log("Game Port: " + ShareConfig.gamePort + " (\u6E38\u620F\u903B\u8F91)");
    console.log("Game Server URL: " + ShareConfig.gameServerUrl);
    console.log("Production: " + ShareConfig.isProduction);
    console.log("Facebook SDK: " + ShareConfig.enableFacebookSDK);
    console.log('============================================================');
  }

  _export({
    validateConfig: validateConfig,
    printConfigInfo: printConfigInfo
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "74cfdUc6HZLNJCAYnQxXpa+", "ShareConfig", undefined);

      // ShareConfig.ts - dev分支配置 (个人开发环境)
      // 根据分支自动确定环境配置，简化部署流程
      _export("Environment", Environment = /*#__PURE__*/function (Environment) {
        Environment["DEVELOPMENT"] = "development";
        Environment["FACEBOOK_MOCK"] = "facebook_mock";
        Environment["PRODUCTION_PERSONAL"] = "production_personal";
        Environment["PRODUCTION_FACEBOOK"] = "production_facebook";
        return Environment;
      }({}));

      _export("Platform", Platform = /*#__PURE__*/function (Platform) {
        Platform["PERSONAL"] = "personal";
        Platform["FACEBOOK"] = "facebook";
        return Platform;
      }({}));

      // 🔧 dev分支专用：个人开发环境配置
      DEVELOPMENT_CONFIG = {
        environment: Environment.DEVELOPMENT,
        platform: Platform.PERSONAL,
        serverUrl: 'http://localhost:3000',
        mongoUrl: 'mongodb://localhost:27017',
        mongoDbName: 'moyu_dev',
        port: 3000,
        isProduction: false,
        corsOrigins: ['*'],
        enableFacebookSDK: false,
        // 核心协议配置
        https: false,
        gate: 'localhost:3000',
        json: true,
        security: true,
        // 双端口HTTP架构支持
        gamePort: 3001,
        gameServerUrl: 'http://localhost:3001'
      }; // 环境配置映射

      CONFIG_MAP = {
        [Environment.DEVELOPMENT]: DEVELOPMENT_CONFIG,
        [Environment.FACEBOOK_MOCK]: DEVELOPMENT_CONFIG,
        // 简化
        [Environment.PRODUCTION_PERSONAL]: DEVELOPMENT_CONFIG,
        // 简化
        [Environment.PRODUCTION_FACEBOOK]: DEVELOPMENT_CONFIG // 简化

      }; // 获取当前配置 - dev分支专用个人开发配置

      _export("ShareConfig", ShareConfig = DEVELOPMENT_CONFIG);

      _export("default", ShareConfig);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fd93af24472005c2ce5a8e9c23602f4330dc09a4.js.map