"use strict";
/*
 * Redis缓存管理器 - 优化数据缓存，提升性能
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisCacheManager = void 0;
const chalk_1 = __importDefault(require("chalk"));
const RedisManager_1 = require("./RedisManager");
class RedisCacheManager {
    /**
     * 初始化缓存管理器
     */
    static async init() {
        try {
            console.log(chalk_1.default.cyan('📝 初始化Redis缓存管理器...'));
            // 启动统计定时器
            this.startStatsTimer();
            console.log(chalk_1.default.green('✅ Redis缓存管理器初始化完成'));
            console.log(chalk_1.default.gray(`📋 配置: TTL=${this.config.defaultTTL}s, 最大键数=${this.config.maxCacheSize}`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Redis缓存管理器初始化失败:'), error);
            throw error;
        }
    }
    /**
     * 设置缓存
     */
    static async set(key, value, type = 'temp', customTTL) {
        try {
            const strategy = this.cacheStrategies[type];
            const fullKey = this.getFullKey(strategy.prefix + key);
            // 序列化数据
            const serializedValue = this.serialize(value);
            // 确定过期时间
            const ttl = customTTL !== undefined ? customTTL :
                strategy.ttl === -1 ? undefined : strategy.ttl;
            // 设置缓存
            await RedisManager_1.RedisManager.set(fullKey, serializedValue, ttl);
            // 更新统计
            this.stats.sets++;
            console.log(chalk_1.default.blue(`📥 缓存设置: ${fullKey.substring(0, 50)}... TTL=${ttl || '永久'}`));
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 设置缓存失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 获取缓存
     */
    static async get(key, type = 'temp') {
        try {
            const strategy = this.cacheStrategies[type];
            const fullKey = this.getFullKey(strategy.prefix + key);
            const value = await RedisManager_1.RedisManager.get(fullKey);
            if (value === null) {
                this.stats.misses++;
                return null;
            }
            // 更新统计
            this.stats.hits++;
            // 反序列化数据
            const deserializedValue = this.deserialize(value);
            console.log(chalk_1.default.green(`📤 缓存命中: ${fullKey.substring(0, 50)}...`));
            return deserializedValue;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 获取缓存失败 ${key}:`), error);
            this.stats.misses++;
            return null;
        }
    }
    /**
     * 删除缓存
     */
    static async del(key, type = 'temp') {
        try {
            const strategy = this.cacheStrategies[type];
            const fullKey = this.getFullKey(strategy.prefix + key);
            await RedisManager_1.RedisManager.del(fullKey);
            // 更新统计
            this.stats.deletes++;
            console.log(chalk_1.default.yellow(`🗑️  缓存删除: ${fullKey.substring(0, 50)}...`));
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 删除缓存失败 ${key}:`), error);
        }
    }
    /**
     * 检查缓存是否存在
     */
    static async exists(key, type = 'temp') {
        try {
            const strategy = this.cacheStrategies[type];
            const fullKey = this.getFullKey(strategy.prefix + key);
            return await RedisManager_1.RedisManager.exists(fullKey);
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 检查缓存存在性失败 ${key}:`), error);
            return false;
        }
    }
    /**
     * 获取或设置缓存（缓存穿透保护）
     */
    static async getOrSet(key, fetcher, type = 'temp', customTTL) {
        try {
            // 先尝试从缓存获取
            const cached = await this.get(key, type);
            if (cached !== null) {
                return cached;
            }
            // 缓存未命中，从数据源获取
            console.log(chalk_1.default.cyan(`🔄 缓存未命中，从数据源获取: ${key}`));
            const data = await fetcher();
            // 设置缓存
            await this.set(key, data, type, customTTL);
            return data;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ getOrSet失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 批量设置缓存
     */
    static async mset(items) {
        try {
            const promises = items.map(item => this.set(item.key, item.value, item.type || 'temp', item.ttl));
            await Promise.all(promises);
            console.log(chalk_1.default.blue(`📥 批量设置缓存: ${items.length} 个`));
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 批量设置缓存失败:'), error);
            throw error;
        }
    }
    /**
     * 批量获取缓存
     */
    static async mget(keys) {
        try {
            const promises = keys.map(item => this.get(item.key, item.type || 'temp'));
            const results = await Promise.all(promises);
            const hitCount = results.filter(r => r !== null).length;
            console.log(chalk_1.default.green(`📤 批量获取缓存: ${hitCount}/${keys.length} 命中`));
            return results;
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 批量获取缓存失败:'), error);
            throw error;
        }
    }
    /**
     * 清空指定类型的所有缓存
     */
    static async clearType(type) {
        try {
            const strategy = this.cacheStrategies[type];
            const pattern = this.getFullKey(strategy.prefix + '*');
            const deleted = await RedisManager_1.RedisManager.deletePattern(pattern);
            console.log(chalk_1.default.yellow(`🧹 清空 ${type} 类型缓存: ${deleted} 个`));
            return deleted;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ 清空缓存类型失败 ${type}:`), error);
            return 0;
        }
    }
    /**
     * 刷新过期缓存
     */
    static async refreshExpiring(refresher) {
        try {
            let refreshed = 0;
            // 检查所有自动刷新的缓存类型
            for (const [typeName, strategy] of Object.entries(this.cacheStrategies)) {
                if (!strategy.autoRefresh)
                    continue;
                const pattern = this.getFullKey(strategy.prefix + '*');
                const keys = await RedisManager_1.RedisManager.getClient().keys(pattern);
                for (const fullKey of keys) {
                    const ttl = await RedisManager_1.RedisManager.ttl(fullKey);
                    // 如果TTL少于策略TTL的20%，则刷新
                    const refreshThreshold = strategy.ttl * 0.2;
                    if (ttl > 0 && ttl < refreshThreshold) {
                        try {
                            const originalKey = fullKey
                                .replace(this.config.keyPrefix, '')
                                .replace(strategy.prefix, '');
                            const newData = await refresher(originalKey, typeName);
                            await this.set(originalKey, newData, typeName);
                            refreshed++;
                            console.log(chalk_1.default.blue(`🔄 自动刷新缓存: ${originalKey}`));
                        }
                        catch (error) {
                            console.error(chalk_1.default.yellow(`⚠️  刷新缓存失败: ${fullKey}`), error);
                        }
                    }
                }
            }
            if (refreshed > 0) {
                console.log(chalk_1.default.green(`✅ 自动刷新完成: ${refreshed} 个缓存`));
            }
            return refreshed;
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 刷新过期缓存失败:'), error);
            return 0;
        }
    }
    /**
     * 获取缓存统计信息
     */
    static async getStats() {
        try {
            // 更新键统计
            const cacheKeys = await RedisManager_1.RedisManager.getClient().keys(this.config.keyPrefix + '*');
            this.stats.totalKeys = cacheKeys.length;
            // 更新内存统计
            const memoryUsage = await RedisManager_1.RedisManager.getMemoryUsage();
            this.stats.usedMemory = memoryUsage.usedMemory;
            return { ...this.stats };
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 获取缓存统计失败:'), error);
            return this.stats;
        }
    }
    /**
     * 重置统计信息
     */
    static resetStats() {
        this.stats.hits = 0;
        this.stats.misses = 0;
        this.stats.sets = 0;
        this.stats.deletes = 0;
        console.log(chalk_1.default.blue('📊 缓存统计已重置'));
    }
    /**
     * 计算命中率
     */
    static getHitRate() {
        const total = this.stats.hits + this.stats.misses;
        return total > 0 ? (this.stats.hits / total) * 100 : 0;
    }
    // ============ 私有方法 ============
    /**
     * 获取完整键名
     */
    static getFullKey(key) {
        return this.config.keyPrefix + key;
    }
    /**
     * 序列化数据
     */
    static serialize(value) {
        try {
            return JSON.stringify(value);
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 序列化失败:'), error);
            return String(value);
        }
    }
    /**
     * 反序列化数据
     */
    static deserialize(value) {
        try {
            return JSON.parse(value);
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ 反序列化失败:'), error);
            return value;
        }
    }
    /**
     * 启动统计定时器
     */
    static startStatsTimer() {
        setInterval(async () => {
            const stats = await this.getStats();
            const hitRate = this.getHitRate();
            console.log(chalk_1.default.blue(`📊 缓存统计: 命中率=${hitRate.toFixed(2)}%, ` +
                `命中=${stats.hits}, 未命中=${stats.misses}, ` +
                `总键数=${stats.totalKeys}, 内存=${stats.usedMemory}`));
        }, 10 * 60 * 1000); // 每10分钟输出一次
    }
    /**
     * 关闭缓存管理器
     */
    static async close() {
        console.log(chalk_1.default.green('✅ Redis缓存管理器已关闭'));
    }
}
exports.RedisCacheManager = RedisCacheManager;
RedisCacheManager.config = {
    defaultTTL: 30 * 60, // 30分钟
    maxCacheSize: 10000, // 10000个键
    enableCompression: true, // 启用压缩
    keyPrefix: 'cache:' // 缓存键前缀
};
RedisCacheManager.stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    totalKeys: 0,
    usedMemory: '0B'
};
// 不同类型数据的缓存策略
RedisCacheManager.cacheStrategies = {
    // 排行榜缓存 - 5分钟过期
    ranking: {
        ttl: 5 * 60,
        prefix: 'rank:',
        autoRefresh: true
    },
    // 用户基础信息缓存 - 1小时过期
    userBasic: {
        ttl: 60 * 60,
        prefix: 'user_basic:',
        autoRefresh: false
    },
    // 游戏配置缓存 - 永不过期，手动刷新
    gameConfig: {
        ttl: -1,
        prefix: 'game_config:',
        autoRefresh: false
    },
    // 临时数据缓存 - 5分钟过期
    temp: {
        ttl: 5 * 60,
        prefix: 'temp:',
        autoRefresh: false
    },
    // 统计数据缓存 - 15分钟过期
    stats: {
        ttl: 15 * 60,
        prefix: 'stats:',
        autoRefresh: true
    }
};
