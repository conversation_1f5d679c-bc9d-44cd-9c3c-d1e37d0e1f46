System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EditBox, Node, sys, oops, PlatformUtil, ecs, CCVMParentComp, JustAuthPlatformType, CommonTools, ClientConfig, GameStorageConfig, PromptManager, smc, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _class3, _crd, ccclass, property, LoginViewComp;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatformUtil(extras) {
    _reporterNs.report("PlatformUtil", "../../../../../extensions/oops-plugin-framework/assets/core/utils/PlatformUtil", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCCVMParentComp(extras) {
    _reporterNs.report("CCVMParentComp", "../../../../../extensions/oops-plugin-framework/assets/module/common/CCVMParentComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfJustAuthPlatformType(extras) {
    _reporterNs.report("JustAuthPlatformType", "../../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCommonTools(extras) {
    _reporterNs.report("CommonTools", "../../../tsrpc/protocols/commonTools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfReqFacebookLogin(extras) {
    _reporterNs.report("ReqFacebookLogin", "../../../tsrpc/protocols/gate/PtlFacebookLogin", _context.meta, extras);
  }

  function _reportPossibleCrUseOfReqRegister(extras) {
    _reporterNs.report("ReqRegister", "../../../tsrpc/protocols/gate/PtlRegister", _context.meta, extras);
  }

  function _reportPossibleCrUseOfClientConfig(extras) {
    _reporterNs.report("ClientConfig", "../../common/ClientConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStorageConfig(extras) {
    _reporterNs.report("GameStorageConfig", "../../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPromptManager(extras) {
    _reporterNs.report("PromptManager", "../../common/prompt/PromptManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLastLogin(extras) {
    _reporterNs.report("LastLogin", "../bll/Login", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      EditBox = _cc.EditBox;
      Node = _cc.Node;
      sys = _cc.sys;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      PlatformUtil = _unresolved_3.PlatformUtil;
    }, function (_unresolved_4) {
      ecs = _unresolved_4.ecs;
    }, function (_unresolved_5) {
      CCVMParentComp = _unresolved_5.CCVMParentComp;
    }, function (_unresolved_6) {
      JustAuthPlatformType = _unresolved_6.JustAuthPlatformType;
    }, function (_unresolved_7) {
      CommonTools = _unresolved_7.CommonTools;
    }, function (_unresolved_8) {
      ClientConfig = _unresolved_8.ClientConfig;
    }, function (_unresolved_9) {
      GameStorageConfig = _unresolved_9.GameStorageConfig;
    }, function (_unresolved_10) {
      PromptManager = _unresolved_10.PromptManager;
    }, function (_unresolved_11) {
      smc = _unresolved_11.smc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4e091gilDVKO7MagP5GRpoa", "LoginViewComp", undefined);

      __checkObsolete__(['_decorator', 'EditBox', 'Node', 'sys']);

      ({
        ccclass,
        property
      } = _decorator);

      // 登录界面和注册界面切换
      _export("LoginViewComp", LoginViewComp = (_dec = ccclass('LoginViewComp'), _dec2 = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register('LoginView', false), _dec3 = property(Node), _dec4 = property(Node), _dec5 = property({
        type: EditBox
      }), _dec6 = property({
        type: EditBox
      }), _dec7 = property({
        type: EditBox
      }), _dec8 = property({
        type: EditBox
      }), _dec9 = property({
        type: EditBox
      }), _dec(_class = _dec2(_class = (_class2 = (_class3 = class LoginViewComp extends (_crd && CCVMParentComp === void 0 ? (_reportPossibleCrUseOfCCVMParentComp({
        error: Error()
      }), CCVMParentComp) : CCVMParentComp) {
        constructor(...args) {
          super(...args);

          /** VM 组件绑定数据 */
          this.data = {
            isRegister: 0,
            //显示注册
            loginFail: 0,
            // 登录失败
            isSafeAccount: 1,
            // 账号安全
            isSafePassword: 1,
            // 密码安全
            isPasswordMatch: 1 // 密码匹配

          };

          _initializerDefineProperty(this, "loginNode", _descriptor, this);

          _initializerDefineProperty(this, "registerNode", _descriptor2, this);

          _initializerDefineProperty(this, "loginAccountEBox", _descriptor3, this);

          _initializerDefineProperty(this, "loginPasswordEBox", _descriptor4, this);

          _initializerDefineProperty(this, "regAccountEBox", _descriptor5, this);

          _initializerDefineProperty(this, "regPasswordEBox", _descriptor6, this);

          _initializerDefineProperty(this, "regPasswordAgainEBox", _descriptor7, this);

          // 上次登录信息
          this.lastLogin = void 0;
          // 回调函数
          this.callbacks = void 0;
        }

        /**
         * 获取最后一次Facebook登录的结果（供InitRes使用）
         */
        static getLastFacebookLoginResult() {
          return LoginViewComp.lastFacebookLoginResult;
        }

        onAdded(data) {
          this.lastLogin = data == null ? void 0 : data.lastLogin;
          this.callbacks = data == null ? void 0 : data.callbacks;
        }

        reset() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.removeByNode(this.node);
        }

        onLoad() {
          super.onLoad();
          this.setButton(false);
        }

        start() {
          this.regAccountEBox.node.on('editing-did-ended', this.checkAccountEdit, this);
          this.regAccountEBox.node.on('editing-did-change', this.checkAccountEdit, this);
          this.regPasswordEBox.node.on('editing-did-ended', this.checkPasswordEdit, this);
          this.regPasswordEBox.node.on('editing-did-change', this.checkPasswordEdit, this);
          this.regPasswordAgainEBox.node.on('editing-did-ended', this.checkPasswordMatch, this);
          this.regPasswordAgainEBox.node.on('editing-did-change', this.checkPasswordMatch, this);

          if (this.lastLogin) {
            var _this$lastLogin, _this$lastLogin2;

            this.loginAccountEBox.string = ((_this$lastLogin = this.lastLogin) == null ? void 0 : _this$lastLogin.userName) || '';
            this.loginPasswordEBox.string = ((_this$lastLogin2 = this.lastLogin) == null ? void 0 : _this$lastLogin2.passWord) || '';
          }

          this.checkPasswordEdit();
          this.checkAccountEdit();
        }

        checkAccountEdit() {
          let ret = (_crd && CommonTools === void 0 ? (_reportPossibleCrUseOfCommonTools({
            error: Error()
          }), CommonTools) : CommonTools).checkUsername(this.regAccountEBox.string);
          this.data.isSafeAccount = ret ? 1 : 0;
          return ret;
        }

        checkPasswordEdit() {
          let ret = (_crd && CommonTools === void 0 ? (_reportPossibleCrUseOfCommonTools({
            error: Error()
          }), CommonTools) : CommonTools).checkPassword(this.regPasswordEBox.string);
          this.data.isSafePassword = ret ? 1 : 0;
          return ret;
        }

        checkPasswordMatch() {
          if (this.regPasswordEBox.string !== this.regPasswordAgainEBox.string) {
            this.data.isPasswordMatch = 0;
            return false;
          }

          this.data.isPasswordMatch = 1;
          return true;
        }

        googleButton1() {
          this.googleLogin();
        }

        googleButton2() {
          this.googleLogin();
        }

        static async doAfterLogin(httpUrl) {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔄 开始doAfterLogin流程（纯HTTP架构）...', {
            httpUrl
          }); // 检查SSO Token是否存在

          const ssoToken = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.get('SSO_TOKEN');
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔑 当前SSO Token状态:', {
            exists: !!ssoToken,
            length: ssoToken ? ssoToken.length : 0,
            preview: ssoToken ? ssoToken.substring(0, 8) + '...' : '无'
          });
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('📋 开始Role.loadData()调用（HTTP API）...');
          let res = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role.loadData();
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('📋 Role.loadData()结果:', {
            success: res
          });
          return res; // 直接返回结果
        }

        static async doLogin(loginData) {
          let reqLoginData = {
            server: loginData.httpUrl,
            userName: loginData.userName,
            passWord: loginData.passWord
          };
          const ret = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).net.hcGate.callApi(`Login`, reqLoginData);

          if (ret.isSucc) {
            let res = await LoginViewComp.doAfterLogin(loginData.httpUrl);

            if (res) {
              // 设置本地存储登录数据
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).storage.set((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                error: Error()
              }), GameStorageConfig) : GameStorageConfig).UserDumpKey, reqLoginData);
            }

            return res;
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast(ret.err.message, true);
            return false;
          }
        }

        async loginButton() {
          var _initialize$GateModel;

          if (this.loginAccountEBox.string.length == 0 || this.loginPasswordEBox.string.length == 0) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('login_empty', true);
            return false;
          } // 获取服务器配置


          const url = ((_initialize$GateModel = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).initialize.GateModel.area) == null ? void 0 : _initialize$GateModel.httpUrl) || 'http://127.0.0.1:3000';
          let ret = await LoginViewComp.doLogin({
            userName: this.loginAccountEBox.string,
            passWord: this.loginPasswordEBox.string,
            isGuest: false,
            httpUrl: url
          });

          if (ret) {
            var _this$callbacks;

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('Old user manual login success');
            await ((_this$callbacks = this.callbacks) == null || _this$callbacks.onOldUserLogin == null ? void 0 : _this$callbacks.onOldUserLogin());
            this.reset();
          }

          return ret;
        }

        forgetPasswordButton() {}

        async loginGuestButton() {
          let res = await this.register({
            isGuest: true
          });

          if (res) {
            var _initialize$GateModel2;

            // 获取服务器配置
            const httpUrl = ((_initialize$GateModel2 = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).initialize.GateModel.area) == null ? void 0 : _initialize$GateModel2.httpUrl) || 'http://127.0.0.1:3000';
            let ret = await LoginViewComp.doLogin({
              userName: res.userName,
              passWord: res.passWord,
              isGuest: true,
              httpUrl: httpUrl
            });

            if (ret) {
              var _this$callbacks2;

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('Guest login success (new user)');
              await ((_this$callbacks2 = this.callbacks) == null || _this$callbacks2.onNewUserLogin == null ? void 0 : _this$callbacks2.onNewUserLogin());
              this.reset();
            }

            return ret;
          }

          return false;
        }

        async register(data) {
          let args = {
            platform: (_crd && PlatformUtil === void 0 ? (_reportPossibleCrUseOfPlatformUtil({
              error: Error()
            }), PlatformUtil) : PlatformUtil).getPlateform(),
            platformType: sys.platform,
            isGuest: data.isGuest || false,
            userName: data.userName,
            passWord: data.passWord
          };
          const retRegister = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).net.hcGate.callApi(`Register`, args);

          if (retRegister.isSucc) {
            return retRegister.res;
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast(retRegister.err.message, true);
            return false;
          }
        }

        async confirmRegButton() {
          if (this.checkAccountEdit() && this.checkPasswordEdit() && this.checkPasswordMatch()) {
            let ret = await this.register({
              userName: this.regAccountEBox.string,
              passWord: this.regPasswordEBox.string,
              isGuest: false
            });

            if (ret) {
              (_crd && PromptManager === void 0 ? (_reportPossibleCrUseOfPromptManager({
                error: Error()
              }), PromptManager) : PromptManager).instance.confirm(7, async () => {
                var _initialize$GateModel3;

                // 获取服务器配置
                const httpUrl = ((_initialize$GateModel3 = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                  error: Error()
                }), smc) : smc).initialize.GateModel.area) == null ? void 0 : _initialize$GateModel3.httpUrl) || 'http://127.0.0.1:3000';
                let res = await LoginViewComp.doLogin({
                  userName: ret.userName,
                  passWord: ret.passWord,
                  isGuest: false,
                  httpUrl: httpUrl
                });

                if (res) {
                  var _this$callbacks3;

                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('Register user login success (new user)');
                  await ((_this$callbacks3 = this.callbacks) == null || _this$callbacks3.onNewUserLogin == null ? void 0 : _this$callbacks3.onNewUserLogin());
                  this.reset();
                }
              });
            }
          }

          return;
        }

        async googleLogin() {
          let ret = await this.justAuthByPlatformType((_crd && JustAuthPlatformType === void 0 ? (_reportPossibleCrUseOfJustAuthPlatformType({
            error: Error()
          }), JustAuthPlatformType) : JustAuthPlatformType).google);
          return ret;
        }

        async justAuthByPlatformType(type) {
          // 🎯 纯HTTP架构：第三方登录暂不支持，返回false
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('⚠️ 纯HTTP架构：第三方登录功能暂不支持');
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.toast('第三方登录功能暂不支持', true);
          return false;
        }

        async connectGateWs() {
          // 🎯 纯HTTP架构：WebSocket连接已移除
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('⚠️ 纯HTTP架构：WebSocket连接已移除');
          return false;
        }
        /**
         * Facebook自动登录 - 纯HTTP版本
         * 在Facebook环境中自动使用Facebook ID登录，返回服务端的用户信息
         */


        static async doFacebookLogin() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎮 开始Facebook登录（纯HTTP架构）...');

          try {
            // 获取Facebook登录数据
            const facebookData = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).fbInstantManager.getFacebookLoginData();

            if (!facebookData) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ 无法获取Facebook登录数据');
              return false;
            } // 🔒 使用纯HTTP API进行Facebook登录


            const httpUrl = (_crd && ClientConfig === void 0 ? (_reportPossibleCrUseOfClientConfig({
              error: Error()
            }), ClientConfig) : ClientConfig).httpUrl; // 准备Facebook登录请求

            const reqFacebookLogin = {
              facebookId: facebookData.facebookId,
              playerName: facebookData.playerName,
              playerPhoto: facebookData.playerPhoto,
              locale: facebookData.locale,
              countryCode: facebookData.countryCode,
              platform: facebookData.platform,
              platformType: facebookData.platformType,
              httpUrl: httpUrl // 改为HTTP URL

            };
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('📤 发送Facebook登录请求到服务器（HTTP API）...', {
              facebookId: facebookData.facebookId,
              playerName: facebookData.playerName
            });
            const ret = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGate.callApi(`FacebookLogin`, reqFacebookLogin);

            if (ret.isSucc) {
              const fbRes = ret.res;
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ Facebook登录API成功:', {
                guuid: fbRes.guuid,
                userName: fbRes.userName,
                isNewUser: fbRes.isNewUser
              }); // 🔑 缓存登录结果供后续使用

              LoginViewComp.lastFacebookLoginResult = {
                success: true,
                isNewUser: fbRes.isNewUser,
                userData: {
                  guuid: fbRes.guuid,
                  userName: fbRes.userName,
                  facebookId: facebookData.facebookId,
                  playerName: facebookData.playerName
                }
              }; // 存储SSO Token（如果需要）

              if (fbRes.__ssoToken) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).storage.set('SSO_TOKEN', fbRes.__ssoToken);
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('🔑 SSO Token已存储');
              }

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`🎉 Facebook${fbRes.isNewUser ? '新' : '老'}用户登录成功！`);
              return true;
            } else {
              const errorInfo = {
                message: ret.err.message || 'Unknown error',
                code: ret.err.code || 'Unknown code'
              };
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ Facebook登录失败:', errorInfo); // 缓存失败结果

              LoginViewComp.lastFacebookLoginResult = {
                success: false,
                error: errorInfo
              };
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('💥 Facebook登录异常:', error); // 缓存异常结果

            LoginViewComp.lastFacebookLoginResult = {
              success: false,
              error: error
            };
            return false;
          }
        }

      }, _class3.lastFacebookLoginResult = null, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "loginNode", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "registerNode", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "loginAccountEBox", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "loginPasswordEBox", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "regAccountEBox", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "regPasswordEBox", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "regPasswordAgainEBox", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=07cddd1fe1649f069bd739c5ce0f0e3e17e0691a.js.map