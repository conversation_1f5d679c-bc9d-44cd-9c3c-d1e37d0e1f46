"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rank = void 0;
const MongoDB_1 = require("../../common/MongoDB");
const RedisCacheManager_1 = require("../../common/RedisCacheManager");
const Leaderboard_1 = require("../Leaderboard");
class Rank {
    static init() {
        this.leaderboard = new Leaderboard_1.Leaderboard(MongoDB_1.MongoDB.rankDb);
    }
    /** 获取世界排行榜中的排名*/
    static async getRankInWorld(countryCode) {
        return await this.leaderboard.position(MongoDB_1.RankDbCollectionName.worldRank, countryCode);
    }
    /** 获取国家排行榜中的排名*/
    static async getRankInCountry(countryCode, uuid) {
        // 🔧 参数校验：确保 countryCode 不为空且为有效字符串
        if (!countryCode || typeof countryCode !== 'string' || countryCode.trim().length === 0) {
            console.warn(`⚠️ getRankInCountry: 无效的countryCode "${countryCode}", 使用默认值 US`);
            countryCode = 'US';
        }
        else {
            countryCode = countryCode.trim().toUpperCase();
            if (countryCode.length === 0 || countryCode.length > 10) {
                console.warn(`⚠️ getRankInCountry: 国家代码长度异常 "${countryCode}", 使用默认值 US`);
                countryCode = 'US';
            }
        }
        return await this.leaderboard.position(countryCode, uuid);
    }
    /** 获取城市排行榜中的排名 */
    static async getRankInCity(cityCode, uuid) {
        return await this.leaderboard.position(`city_${cityCode}`, uuid);
    }
    static async getCountryData(countryCode, uuid) {
        // 🔧 参数校验：确保 countryCode 不为空且为有效字符串
        if (!countryCode || typeof countryCode !== 'string' || countryCode.trim().length === 0) {
            console.warn(`⚠️ getCountryData: 无效的countryCode "${countryCode}", 使用默认值 US`);
            countryCode = 'US';
        }
        else {
            countryCode = countryCode.trim().toUpperCase();
            if (countryCode.length === 0 || countryCode.length > 10) {
                console.warn(`⚠️ getCountryData: 国家代码长度异常 "${countryCode}", 使用默认值 US`);
                countryCode = 'US';
            }
        }
        return await this.leaderboard.get(countryCode, uuid);
    }
    static async getWorldData(countryCode) {
        // 🔧 参数校验：确保 countryCode 不为空且为有效字符串
        if (!countryCode || typeof countryCode !== 'string' || countryCode.trim().length === 0) {
            console.warn(`⚠️ getWorldData: 无效的countryCode "${countryCode}", 使用默认值 US`);
            countryCode = 'US';
        }
        else {
            countryCode = countryCode.trim().toUpperCase();
            if (countryCode.length === 0 || countryCode.length > 10) {
                console.warn(`⚠️ getWorldData: 国家代码长度异常 "${countryCode}", 使用默认值 US`);
                countryCode = 'US';
            }
        }
        return await this.leaderboard.get(MongoDB_1.RankDbCollectionName.worldRank, countryCode);
    }
    /** 获取城市数据 */
    static async getCityData(cityCode, uuid) {
        return await this.leaderboard.get(`city_${cityCode}`, uuid);
    }
    /** 获取世界排行榜列表 */
    static async getWorldList() {
        const cacheKey = 'world_rank_list';
        if (this.CONFIG.ENABLE_CACHE) {
            const cached = await RedisCacheManager_1.RedisCacheManager.get(cacheKey, 'ranking');
            if (cached) {
                console.log('🎯 使用缓存的世界排行榜数据');
                return cached;
            }
        }
        let listOptions = { _id: 0, createdAt: 0 };
        let res = await this.leaderboard.list(MongoDB_1.RankDbCollectionName.worldRank, listOptions, this.CONFIG.RANK_LIMIT);
        if (this.CONFIG.ENABLE_CACHE) {
            await RedisCacheManager_1.RedisCacheManager.set(cacheKey, res, 'ranking', this.CONFIG.CACHE_TTL);
        }
        return res;
    }
    /** 获取国家排行榜列表*/
    static async getCountryList(countryCode) {
        // 🔧 参数校验：确保 countryCode 不为空且为有效字符串
        if (!countryCode || typeof countryCode !== 'string' || countryCode.trim().length === 0) {
            console.warn(`⚠️ getCountryList: 无效的countryCode "${countryCode}", 使用默认值 US`);
            countryCode = 'US';
        }
        else {
            countryCode = countryCode.trim().toUpperCase();
            if (countryCode.length === 0 || countryCode.length > 10) {
                console.warn(`⚠️ getCountryList: 国家代码长度异常 "${countryCode}", 使用默认值 US`);
                countryCode = 'US';
            }
        }
        const cacheKey = `country_rank_${countryCode}`;
        if (this.CONFIG.ENABLE_CACHE) {
            const cached = await RedisCacheManager_1.RedisCacheManager.get(cacheKey, 'ranking');
            if (cached) {
                console.log(`🎯 使用缓存的国家排行榜数据: ${countryCode}`);
                return cached;
            }
        }
        let listOptions = { _id: 0, createdAt: 0 };
        let res = await this.leaderboard.list(countryCode, listOptions, this.CONFIG.RANK_LIMIT);
        if (this.CONFIG.ENABLE_CACHE) {
            await RedisCacheManager_1.RedisCacheManager.set(cacheKey, res, 'ranking', this.CONFIG.CACHE_TTL);
        }
        return res;
    }
    /** 获取城市排行榜列表 */
    static async getCityList(cityCode) {
        const cacheKey = `city_rank_${cityCode}`;
        const collectionName = `city_${cityCode}`;
        if (this.CONFIG.ENABLE_CACHE) {
            const cached = await RedisCacheManager_1.RedisCacheManager.get(cacheKey, 'ranking');
            if (cached) {
                console.log(`🎯 使用缓存的城市排行榜数据: ${cityCode}`);
                return cached;
            }
        }
        let listOptions = { _id: 0, createdAt: 0 };
        let res = await this.leaderboard.list(collectionName, listOptions, this.CONFIG.RANK_LIMIT);
        if (this.CONFIG.ENABLE_CACHE) {
            await RedisCacheManager_1.RedisCacheManager.set(cacheKey, res, 'ranking', this.CONFIG.CACHE_TTL);
        }
        return res;
    }
    /** 获取国家内所有城市的排行榜列表 (按城市聚合) */
    static async getCountryCityList(countryCode) {
        const cacheKey = `country_city_rank_${countryCode}`;
        if (this.CONFIG.ENABLE_CACHE) {
            const cached = await RedisCacheManager_1.RedisCacheManager.get(cacheKey, 'ranking');
            if (cached) {
                console.log(`🎯 使用缓存的国家城市排行榜数据: ${countryCode}`);
                return cached;
            }
        }
        // 获取该国家的所有城市排行榜数据
        // 这里需要聚合该国家所有城市的数据
        const collectionName = `country_cities_${countryCode}`;
        let listOptions = { _id: 0, createdAt: 0 };
        let res = await this.leaderboard.list(collectionName, listOptions, this.CONFIG.RANK_LIMIT);
        if (this.CONFIG.ENABLE_CACHE) {
            await RedisCacheManager_1.RedisCacheManager.set(cacheKey, res, 'ranking', this.CONFIG.CACHE_TTL);
        }
        return res;
    }
    /** 获取城市在国家内的排名 */
    static async getRankInCountryCity(countryCode, cityCode) {
        const collectionName = `country_cities_${countryCode}`;
        return await this.leaderboard.position(collectionName, cityCode);
    }
    /** 获取国家内城市的数据 */
    static async getCountryCityData(countryCode, cityCode) {
        const collectionName = `country_cities_${countryCode}`;
        return await this.leaderboard.get(collectionName, cityCode);
    }
    /** 更新国家排行榜分数, 分数为当前国家通关次数*/
    static async UpdateCountryScore(userData) {
        // 🔧 参数校验：确保 countryCode 不为空且为有效字符串
        let countryCode = userData.countryCode;
        if (!countryCode || typeof countryCode !== 'string' || countryCode.trim().length === 0) {
            console.warn(`⚠️ UpdateCountryScore: 用户 ${userData.guuid} 的countryCode无效 "${countryCode}", 使用默认值 US`);
            countryCode = 'US';
            userData.countryCode = countryCode; // 同步更新用户数据
        }
        else {
            countryCode = countryCode.trim().toUpperCase();
            if (countryCode.length === 0 || countryCode.length > 10) {
                console.warn(`⚠️ UpdateCountryScore: 用户 ${userData.guuid} 的国家代码长度异常 "${countryCode}", 使用默认值 US`);
                countryCode = 'US';
                userData.countryCode = countryCode; // 同步更新用户数据
            }
        }
        let rankRecord = {
            id: userData.guuid,
            name: userData.nickName,
            avatar: userData.avatar,
            avatarId: userData.avatarId,
            score: userData.currCountryPassTimes,
            updateTime: new Date(),
        };
        const result = await this.leaderboard.record(countryCode, rankRecord);
        // 清除相关缓存 (安全处理Redis错误)
        if (this.CONFIG.ENABLE_CACHE) {
            try {
                await RedisCacheManager_1.RedisCacheManager.del(`country_rank_${countryCode}`, 'ranking');
            }
            catch (cacheError) {
                console.warn(`⚠️ 清除缓存失败，但继续运行: country_rank_${countryCode}`, cacheError);
            }
        }
        return result;
    }
    /** 更新城市排行榜分数 */
    static async UpdateCityScore(userData, cityCode) {
        let rankRecord = {
            id: userData.guuid,
            name: userData.nickName,
            avatar: userData.avatar,
            avatarId: userData.avatarId,
            score: userData.currCountryPassTimes, // 使用通关次数作为分数
            updateTime: new Date(),
        };
        const collectionName = `city_${cityCode}`;
        const result = await this.leaderboard.record(collectionName, rankRecord);
        // 清除相关缓存 (安全处理Redis错误)
        if (this.CONFIG.ENABLE_CACHE) {
            try {
                await RedisCacheManager_1.RedisCacheManager.del(`city_rank_${cityCode}`, 'ranking');
            }
            catch (cacheError) {
                console.warn(`⚠️ 清除缓存失败，但继续运行: city_rank_${cityCode}`, cacheError);
            }
        }
        return result;
    }
    /** 更新世界排行榜分数 (按国家) */
    static async UpdateWorldScore(countryCode) {
        // 🔧 参数校验：确保 countryCode 不为空且为有效字符串
        if (!countryCode || typeof countryCode !== 'string' || countryCode.trim().length === 0) {
            console.warn(`⚠️ UpdateWorldScore: 无效的countryCode "${countryCode}", 使用默认值 US`);
            countryCode = 'US';
        }
        else {
            countryCode = countryCode.trim().toUpperCase();
            if (countryCode.length === 0 || countryCode.length > 10) {
                console.warn(`⚠️ UpdateWorldScore: 国家代码长度异常 "${countryCode}", 使用默认值 US`);
                countryCode = 'US';
            }
        }
        // 计算该国家的总通关次数
        const countryData = await this.getCountryList(countryCode);
        const totalScore = countryData.reduce((sum, record) => sum + record.score, 0);
        let rankRecord = {
            id: countryCode,
            name: countryCode, // 世界排行榜显示国家代码
            score: totalScore,
            updateTime: new Date(),
        };
        const result = await this.leaderboard.record(MongoDB_1.RankDbCollectionName.worldRank, rankRecord);
        // 清除相关缓存 (安全处理Redis错误)
        if (this.CONFIG.ENABLE_CACHE) {
            try {
                await RedisCacheManager_1.RedisCacheManager.del('world_rank_list', 'ranking');
            }
            catch (cacheError) {
                console.warn(`⚠️ 清除缓存失败，但继续运行: world_rank_list`, cacheError);
            }
        }
        return result;
    }
    /** 生成测试排行榜数据 */
    static async generateTestData(type, identifier, count = 50) {
        console.log(`🔧 开始生成 ${type} 排行榜测试数据: ${identifier}, 数量: ${count}`);
        const testRecords = [];
        const names = [
            '张三', '李四', '王五', '赵六', '陈七', '刘八', '杨九', '黄十',
            'Alice', 'Bob', 'Charlie', 'David', 'Emma', 'Frank', 'Grace', 'Henry',
            '田中太郎', '山田花子', '佐藤次郎', '鈴木美香', 'Hans', 'Klaus', 'Greta', 'Franz'
        ];
        for (let i = 0; i < count; i++) {
            const randomName = names[Math.floor(Math.random() * names.length)];
            const randomScore = Math.floor(Math.random() * 1000) + 1; // 1-1000分
            testRecords.push({
                id: `test_user_${i + 1}`,
                name: `${randomName}_${i + 1}`,
                score: randomScore,
                avatarId: Math.floor(Math.random() * 10) + 1,
                updateTime: new Date()
            });
        }
        // 插入测试数据
        let collectionName;
        switch (type) {
            case 'world':
                collectionName = MongoDB_1.RankDbCollectionName.worldRank;
                break;
            case 'city':
                collectionName = `city_${identifier}`;
                break;
            case 'country':
            default:
                collectionName = identifier;
                break;
        }
        // 批量插入
        for (const record of testRecords) {
            await this.leaderboard.record(collectionName, record);
        }
        console.log(`✅ ${type} 排行榜测试数据生成完成: ${identifier}`);
        // 清除相关缓存
        if (this.CONFIG.ENABLE_CACHE) {
            const cacheKey = type === 'world' ? 'world_rank_list' :
                type === 'city' ? `city_rank_${identifier}` :
                    `country_rank_${identifier}`;
            await RedisCacheManager_1.RedisCacheManager.del(cacheKey, 'ranking');
        }
        return testRecords.length;
    }
    /** 清除所有排行榜缓存 */
    static async clearAllCache() {
        if (this.CONFIG.ENABLE_CACHE) {
            console.log('🧹 清除所有排行榜缓存');
            // 清除所有排行榜相关缓存
            const cacheKeys = [
                'world_rank_list',
                // 可以添加更多特定的缓存键
            ];
            for (const key of cacheKeys) {
                await RedisCacheManager_1.RedisCacheManager.del(key, 'ranking');
            }
        }
    }
    /** 获取排行榜配置 */
    static getConfig() {
        return { ...this.CONFIG };
    }
    /** 更新排行榜配置 */
    static updateConfig(newConfig) {
        Object.assign(this.CONFIG, newConfig);
        console.log('📝 排行榜配置已更新:', this.CONFIG);
    }
    /** 更新国家内城市排行榜分数 (城市级别数据) */
    static async UpdateCountryCityScore(countryCode, cityCode, cityName) {
        // 计算该城市在该国家的总通关次数
        const cityData = await this.getCityList(cityCode);
        const totalScore = cityData.reduce((sum, record) => sum + record.score, 0);
        let rankRecord = {
            id: cityCode,
            name: cityName || cityCode, // 城市名称或代码
            score: totalScore,
            updateTime: new Date(),
        };
        const collectionName = `country_cities_${countryCode}`;
        const result = await this.leaderboard.record(collectionName, rankRecord);
        // 清除相关缓存 (安全处理Redis错误)
        if (this.CONFIG.ENABLE_CACHE) {
            try {
                await RedisCacheManager_1.RedisCacheManager.del(`country_city_rank_${countryCode}`, 'ranking');
            }
            catch (cacheError) {
                console.warn(`⚠️ 清除缓存失败，但继续运行: country_city_rank_${countryCode}`, cacheError);
            }
        }
        return result;
    }
}
exports.Rank = Rank;
// 排行榜配置
Rank.CONFIG = {
    RANK_LIMIT: 20, // 排行榜显示数量 (减少数量提高性能)
    CACHE_TTL: 5 * 60, // 缓存时间（秒）
    ENABLE_CACHE: true // 是否启用缓存
};
