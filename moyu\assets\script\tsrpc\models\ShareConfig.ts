// ShareConfig.ts - dev分支配置 (个人开发环境)
// 根据分支自动确定环境配置，简化部署流程

export enum Environment {
    DEVELOPMENT = 'development',
    FACEBOOK_MOCK = 'facebook_mock',
    PRODUCTION_PERSONAL = 'production_personal',
    PRODUCTION_FACEBOOK = 'production_facebook'
}

export enum Platform {
    PERSONAL = 'personal',
    FACEBOOK = 'facebook'
}

export interface ServerConfig {
    environment: Environment;
    platform: Platform;
    serverUrl: string;
    clientUrl: string;
    mongoUrl: string;
    mongoDbName: string;
    port: number;
    isProduction: boolean;
    enableCors: boolean;
    corsOrigins: string[];
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableFacebookSDK: boolean;
    facebookAppId?: string;
    enableAnalytics: boolean;
    maxPlayersPerRoom: number;
    gameSettings: {
        enableRanking: boolean;
        enableRewards: boolean;
        enableSocialFeatures: boolean;
    };

    // 兼容旧客户端代码的属性
    https: boolean;
    gate: string;
    httpPort: number;
    wsPort: number;
    json: boolean;
    security: boolean;
    heartbeat_interval: number;
    heartbeat_timeout: number;

    // 双端口HTTP架构支持
    gamePort: number;
    gameServerUrl: string;
}

// 根据当前分支确定环境
function getCurrentEnvironment(): Environment {
    // dev 分支固定返回 DEVELOPMENT 环境
    return Environment.DEVELOPMENT;
}

// 🔧 dev分支专用：个人开发环境配置
const DEVELOPMENT_CONFIG: ServerConfig = {
    environment: Environment.DEVELOPMENT,
    platform: Platform.PERSONAL,
    serverUrl: 'http://localhost:3000',
    clientUrl: 'http://localhost:7456',
    mongoUrl: 'mongodb://localhost:27017',
    mongoDbName: 'moyu_dev',
    port: 3000,
    isProduction: false,
    enableCors: true,
    corsOrigins: [
        'http://localhost:7456',
        'http://127.0.0.1:7456',
        'http://localhost:8080'
    ],
    logLevel: 'debug',
    enableFacebookSDK: false,
    enableAnalytics: false,
    maxPlayersPerRoom: 4,
    gameSettings: {
        enableRanking: true,
        enableRewards: true,
        enableSocialFeatures: false // 个人开发环境不启用社交功能
    },
    // 兼容属性
    https: false,
    gate: 'localhost:3000',
    httpPort: 3000,
    wsPort:3000,
    gamePort:3001,
    gameServerUrl:"",
    json: true,
    security: true,
    heartbeat_interval: 30000,
    heartbeat_timeout: 5000,
    
};

// 环境配置映射
const CONFIG_MAP: Record<Environment, ServerConfig> = {
    [Environment.DEVELOPMENT]: DEVELOPMENT_CONFIG,
    [Environment.FACEBOOK_MOCK]: DEVELOPMENT_CONFIG, // 简化
    [Environment.PRODUCTION_PERSONAL]: DEVELOPMENT_CONFIG, // 简化
    [Environment.PRODUCTION_FACEBOOK]: DEVELOPMENT_CONFIG // 简化
};

// 获取当前配置 - dev分支专用个人开发配置
export const ShareConfig: ServerConfig = DEVELOPMENT_CONFIG;

// 配置验证
export function validateConfig(): boolean {
    const config = ShareConfig;

    if (!config.serverUrl || !config.clientUrl) {
        console.error('Missing required URL configuration');
        return false;
    }

    if (!config.mongoUrl || !config.mongoDbName) {
        console.error('Missing required MongoDB configuration');
        return false;
    }

    if (config.enableFacebookSDK && !config.facebookAppId) {
        console.error('Facebook SDK enabled but no App ID provided');
        return false;
    }

    return true;
}

// 打印当前配置信息
export function printConfigInfo(): void {
    console.log('=== ShareConfig Information (dev) ===');
    console.log(`Environment: ${ShareConfig.environment}`);
    console.log(`Platform: ${ShareConfig.platform}`);
    console.log(`Server URL: ${ShareConfig.serverUrl}`);
    console.log(`Client URL: ${ShareConfig.clientUrl}`);
    console.log(`Database: ${ShareConfig.mongoDbName}`);
    console.log(`Gateway Port: ${ShareConfig.port} (登录注册)`);
    console.log(`Game Port: ${ShareConfig.gamePort} (游戏逻辑)`);
    console.log(`Game Server URL: ${ShareConfig.gameServerUrl}`);
    console.log(`Production: ${ShareConfig.isProduction}`);
    console.log(`Facebook SDK: ${ShareConfig.enableFacebookSDK}`);
    console.log('============================================================');
}

export default ShareConfig; 