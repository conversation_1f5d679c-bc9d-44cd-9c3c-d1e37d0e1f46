import { _decorator, EditBox, Node, sys } from 'cc';
import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { PlatformUtil } from '../../../../../extensions/oops-plugin-framework/assets/core/utils/PlatformUtil';
import { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';
import { CCVMParentComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCVMParentComp';
import { JustAuthPlatformType } from '../../../tsrpc/protocols/base';
import { CommonTools } from '../../../tsrpc/protocols/commonTools';
import { ReqFacebookLogin } from '../../../tsrpc/protocols/gate/PtlFacebookLogin';
import { ReqRegister } from '../../../tsrpc/protocols/gate/PtlRegister';
import { ClientConfig } from '../../common/ClientConfig';
import { GameStorageConfig } from '../../common/config/GameStorageConfig';
import { PromptManager } from '../../common/prompt/PromptManager';
import { smc } from '../../common/SingletonModuleComp';
import { LastLogin } from '../bll/Login';

const { ccclass, property } = _decorator;
export interface LoginData {
    userName: string;
    passWord: string;
    isGuest: boolean;
    httpUrl: string;
}
export interface registerArgs {
    userName?: string;
    passWord?: string;
    isGuest: boolean;
}

export interface LoginCallbacks {
    onRemoved?: () => void;
    onNewUserLogin?: () => Promise<void>;
    onOldUserLogin?: () => Promise<void>;
}

// 登录界面和注册界面切换
@ccclass('LoginViewComp')
@ecs.register('LoginView', false)
export class LoginViewComp extends CCVMParentComp {
    /** VM 组件绑定数据 */
    data: any = {
        isRegister: 0, //显示注册
        loginFail: 0, // 登录失败
        isSafeAccount: 1, // 账号安全
        isSafePassword: 1, // 密码安全
        isPasswordMatch: 1, // 密码匹配
    };
    @property(Node)
    loginNode!: Node;

    @property(Node)
    registerNode!: Node;

    @property({ type: EditBox })
    private loginAccountEBox!: EditBox;
    @property({ type: EditBox })
    private loginPasswordEBox!: EditBox;
    @property({ type: EditBox })
    private regAccountEBox!: EditBox;
    @property({ type: EditBox })
    private regPasswordEBox!: EditBox;
    @property({ type: EditBox })
    private regPasswordAgainEBox!: EditBox;

    // 上次登录信息
    private lastLogin?: LastLogin;
    // 回调函数
    private callbacks?: LoginCallbacks;

    // 静态变量存储最后一次Facebook登录的结果
    private static lastFacebookLoginResult: any = null;

    /**
     * 获取最后一次Facebook登录的结果（供InitRes使用）
     */
    static getLastFacebookLoginResult(): any {
        return LoginViewComp.lastFacebookLoginResult;
    }

    onAdded(data: { lastLogin?: LastLogin; callbacks?: LoginCallbacks }) {
        this.lastLogin = data?.lastLogin;
        this.callbacks = data?.callbacks;
    }
    reset(): void {
        oops.gui.removeByNode(this.node);
    }

    onLoad(): void {
        super.onLoad();
        this.setButton(false);
    }
    protected start(): void {
        this.regAccountEBox.node.on('editing-did-ended', this.checkAccountEdit, this);
        this.regAccountEBox.node.on('editing-did-change', this.checkAccountEdit, this);
        this.regPasswordEBox.node.on('editing-did-ended', this.checkPasswordEdit, this);
        this.regPasswordEBox.node.on('editing-did-change', this.checkPasswordEdit, this);
        this.regPasswordAgainEBox.node.on('editing-did-ended', this.checkPasswordMatch, this);
        this.regPasswordAgainEBox.node.on('editing-did-change', this.checkPasswordMatch, this);
        if (this.lastLogin) {
            this.loginAccountEBox.string = this.lastLogin?.userName || '';
            this.loginPasswordEBox.string = this.lastLogin?.passWord || '';
        }
        this.checkPasswordEdit();
        this.checkAccountEdit();
    }
    private checkAccountEdit() {
        let ret = CommonTools.checkUsername(this.regAccountEBox.string);
        this.data.isSafeAccount = ret ? 1 : 0;
        return ret;
    }
    private checkPasswordEdit() {
        let ret = CommonTools.checkPassword(this.regPasswordEBox.string);
        this.data.isSafePassword = ret ? 1 : 0;
        return ret;
    }

    private checkPasswordMatch() {
        if (this.regPasswordEBox.string !== this.regPasswordAgainEBox.string) {
            this.data.isPasswordMatch = 0;
            return false;
        }
        this.data.isPasswordMatch = 1;
        return true;
    }
    googleButton1() {
        this.googleLogin();
    }
    googleButton2() {
        this.googleLogin();
    }
    private static async doAfterLogin(httpUrl: string) {
        oops.log.logBusiness('🔄 开始doAfterLogin流程（纯HTTP架构）...', { httpUrl });

        // 检查SSO Token是否存在
        const ssoToken = oops.storage.get('SSO_TOKEN');
        oops.log.logBusiness('🔑 当前SSO Token状态:', {
            exists: !!ssoToken,
            length: ssoToken ? ssoToken.length : 0,
            preview: ssoToken ? ssoToken.substring(0, 8) + '...' : '无',
        });

        oops.log.logBusiness('📋 开始Role.loadData()调用（HTTP API）...');

        let res = await smc.role.loadData();
        oops.log.logBusiness('📋 Role.loadData()结果:', { success: res });

        return res; // 直接返回结果
    }
    static async doLogin(loginData: LoginData) {
        let reqLoginData = {
            server: loginData.httpUrl as string,
            userName: loginData.userName,
            passWord: loginData.passWord,
        };
        const ret = await smc.net.hcGate.callApi(`Login`, reqLoginData);
        if (ret.isSucc) {
            let res = await LoginViewComp.doAfterLogin(loginData.httpUrl);
            if (res) {
                // 设置本地存储登录数据
                oops.storage.set(GameStorageConfig.UserDumpKey, reqLoginData);
            }
            return res;
        } else {
            oops.gui.toast(ret.err.message, true);
            return false;
        }
    }

    async loginButton() {
        if (this.loginAccountEBox.string.length == 0 || this.loginPasswordEBox.string.length == 0) {
            oops.gui.toast('login_empty', true);
            return false;
        }

        // 获取服务器配置
        const url = smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';

        let ret = await LoginViewComp.doLogin({
            userName: this.loginAccountEBox.string,
            passWord: this.loginPasswordEBox.string,
            isGuest: false,
            httpUrl: url,
        });

        if (ret) {
            oops.log.logBusiness('Old user manual login success');
            await this.callbacks?.onOldUserLogin?.();
            this.reset();
        }
        return ret;
    }

    forgetPasswordButton() {}

    async loginGuestButton() {
        let res = await this.register({ isGuest: true });
        if (res) {
            // 获取服务器配置
            const httpUrl = smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';

            let ret = await LoginViewComp.doLogin({
                userName: res.userName,
                passWord: res.passWord,
                isGuest: true,
                httpUrl: httpUrl,
            });

            if (ret) {
                oops.log.logBusiness('Guest login success (new user)');
                await this.callbacks?.onNewUserLogin?.();
                this.reset();
            }
            return ret;
        }
        return false;
    }
    private async register(data: registerArgs) {
        let args: ReqRegister = {
            platform: PlatformUtil.getPlateform(),
            platformType: sys.platform,
            isGuest: data.isGuest || false,
            userName: data.userName,
            passWord: data.passWord,
        };
        const retRegister = await smc.net.hcGate.callApi(`Register`, args);
        if (retRegister.isSucc) {
            return retRegister.res;
        } else {
            oops.gui.toast(retRegister.err.message, true);
            return false;
        }
    }
    async confirmRegButton() {
        if (this.checkAccountEdit() && this.checkPasswordEdit() && this.checkPasswordMatch()) {
            let ret = await this.register({
                userName: this.regAccountEBox.string,
                passWord: this.regPasswordEBox.string,
                isGuest: false,
            });

            if (ret) {
                PromptManager.instance.confirm(7, async () => {
                    // 获取服务器配置
                    const httpUrl =
                        smc.initialize.GateModel.area?.httpUrl || 'http://127.0.0.1:3000';

                    let res = await LoginViewComp.doLogin({
                        userName: ret.userName,
                        passWord: ret.passWord,
                        isGuest: false,
                        httpUrl: httpUrl,
                    });

                    if (res) {
                        oops.log.logBusiness('Register user login success (new user)');
                        await this.callbacks?.onNewUserLogin?.();
                        this.reset();
                    }
                });
            }
        }
        return;
    }
    private async googleLogin() {
        let ret = await this.justAuthByPlatformType(JustAuthPlatformType.google);
        return ret;
    }
    private async justAuthByPlatformType(type: JustAuthPlatformType) {
        // 🎯 纯HTTP架构：第三方登录暂不支持，返回false
        oops.log.logBusiness('⚠️ 纯HTTP架构：第三方登录功能暂不支持');
        oops.gui.toast('第三方登录功能暂不支持', true);
        return false;
    }

    private async connectGateWs() {
        // 🎯 纯HTTP架构：WebSocket连接已移除
        oops.log.logBusiness('⚠️ 纯HTTP架构：WebSocket连接已移除');
        return false;
    }

    /**
     * Facebook自动登录 - 纯HTTP版本
     * 在Facebook环境中自动使用Facebook ID登录，返回服务端的用户信息
     */
    static async doFacebookLogin(): Promise<boolean> {
        oops.log.logBusiness('🎮 开始Facebook登录（纯HTTP架构）...');

        try {
            // 获取Facebook登录数据
            const facebookData = smc.fbInstantManager.getFacebookLoginData();
            if (!facebookData) {
                oops.log.logWarn('⚠️ 无法获取Facebook登录数据');
                return false;
            }

            // 🔒 使用纯HTTP API进行Facebook登录
            const httpUrl = ClientConfig.httpUrl;

            // 准备Facebook登录请求
            const reqFacebookLogin: ReqFacebookLogin = {
                facebookId: facebookData.facebookId,
                playerName: facebookData.playerName,
                playerPhoto: facebookData.playerPhoto,
                locale: facebookData.locale,
                countryCode: facebookData.countryCode,
                platform: facebookData.platform,
                platformType: facebookData.platformType,
                httpUrl: httpUrl, // 改为HTTP URL
            };

            oops.log.logBusiness('📤 发送Facebook登录请求到服务器（HTTP API）...', {
                facebookId: facebookData.facebookId,
                playerName: facebookData.playerName,
            });

            const ret = await smc.net.hcGate.callApi(`FacebookLogin`, reqFacebookLogin);

            if (ret.isSucc) {
                const fbRes = ret.res as any;
                oops.log.logBusiness('✅ Facebook登录API成功:', {
                    guuid: fbRes.guuid,
                    userName: fbRes.userName,
                    isNewUser: fbRes.isNewUser,
                });

                // 🔑 缓存登录结果供后续使用
                LoginViewComp.lastFacebookLoginResult = {
                    success: true,
                    isNewUser: fbRes.isNewUser,
                    userData: {
                        guuid: fbRes.guuid,
                        userName: fbRes.userName,
                        facebookId: facebookData.facebookId,
                        playerName: facebookData.playerName,
                    },
                };

                // 存储SSO Token（如果需要）
                if (fbRes.__ssoToken) {
                    oops.storage.set('SSO_TOKEN', fbRes.__ssoToken);
                    oops.log.logBusiness('🔑 SSO Token已存储');
                }

                oops.log.logBusiness(`🎉 Facebook${fbRes.isNewUser ? '新' : '老'}用户登录成功！`);
                return true;
            } else {
                const errorInfo = {
                    message: ret.err.message || 'Unknown error',
                    code: ret.err.code || 'Unknown code',
                };

                oops.log.logError('❌ Facebook登录失败:', errorInfo);

                // 缓存失败结果
                LoginViewComp.lastFacebookLoginResult = {
                    success: false,
                    error: errorInfo,
                };

                return false;
            }
        } catch (error) {
            oops.log.logError('💥 Facebook登录异常:', error);

            // 缓存异常结果
            LoginViewComp.lastFacebookLoginResult = {
                success: false,
                error: error,
            };

            return false;
        }
    }
}
