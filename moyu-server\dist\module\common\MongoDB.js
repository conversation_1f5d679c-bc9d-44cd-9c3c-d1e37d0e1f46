"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoDB = exports.RankDbCollectionName = exports.DbCollectionName = void 0;
/*
 * @Author: dgflash
 * @Date: 2022-05-05 17:20:19
 * @LastEditors: dgflash
 * @LastEditTime: 2022-07-12 18:31:30
 */
const mongodb_1 = require("mongodb");
const IPUntil_1 = require("../3rdData/IPUntil");
const ShareConfig_1 = require("../../tsrpc/models/ShareConfig");
/** 数据表名 */
var DbCollectionName;
(function (DbCollectionName) {
    /** 自增量记录表 */
    DbCollectionName["counters"] = "counters";
    /** 用户表 */
    DbCollectionName["user"] = "user";
})(DbCollectionName || (exports.DbCollectionName = DbCollectionName = {}));
var RankDbCollectionName;
(function (RankDbCollectionName) {
    /** 世界排行榜 */
    RankDbCollectionName["worldRank"] = "worldRank";
})(RankDbCollectionName || (exports.RankDbCollectionName = RankDbCollectionName = {}));
class MongoDB {
    /** 实始化 mongodb 数据库 */
    static async init() {
        console.log(`🔧 MongoDB连接: ${ShareConfig_1.ShareConfig.mongoUrl}`);
        console.log(`🔧 数据库名称: ${ShareConfig_1.ShareConfig.mongoDbName}`);
        const client = await new mongodb_1.MongoClient(ShareConfig_1.ShareConfig.mongoUrl).connect(); // 连接数据库
        this.db = client.db(ShareConfig_1.ShareConfig.mongoDbName); // 使用ShareConfig的数据库名
        this.rankDb = client.db(`${ShareConfig_1.ShareConfig.mongoDbName}_rank`); // 排行榜数据库
        console.log(`✅ MongoDB连接成功: ${ShareConfig_1.ShareConfig.mongoDbName}`);
        // 初始化数据表
        for (var name in DbCollectionName) {
            if (!await this.collectionExist(name, this.db)) {
                await this.db.createCollection(name);
            }
        }
        for (var name in RankDbCollectionName) {
            if (!await this.collectionExist(name, this.rankDb)) {
                await this.rankDb.createCollection(name);
            }
        }
        // 在Facebook环境下，地理位置信息由Facebook SDK提供，无需初始化IPUntil
        if (ShareConfig_1.ShareConfig.platform !== 'facebook') {
            console.log('🌍 初始化IP地理位置服务...');
            IPUntil_1.IPUntil.init();
        }
        else {
            console.log('🌍 Facebook环境：跳过IP地理位置服务初始化，使用Facebook SDK');
        }
    }
    /** 数据表是否存在 */
    static collectionExist(name, db) {
        return new Promise((resolve, reject) => {
            db.listCollections({ name }).next((err, collinfo) => {
                if (collinfo) {
                    resolve(true);
                }
                else {
                    resolve(false);
                }
            });
        });
    }
    /**
     * 获取指定数据表的自增量
     * @param name
     * @returns
     */
    static getNextSequenceValue(name) {
        return new Promise((resolve, reject) => {
            var conters = this.db.collection(DbCollectionName.counters);
            conters.findOneAndUpdate({ key: name }, { $inc: { value: 1 } }, async (err, response) => {
                if (err)
                    throw err;
                // 没有指定配置表自增量记录
                if (response) {
                    if (response.value == null) {
                        await conters.insertOne({
                            key: name,
                            value: 1
                        });
                        resolve(await this.getNextSequenceValue(name));
                    }
                    else {
                        resolve(response.value.value);
                    }
                }
            });
        });
    }
}
exports.MongoDB = MongoDB;
