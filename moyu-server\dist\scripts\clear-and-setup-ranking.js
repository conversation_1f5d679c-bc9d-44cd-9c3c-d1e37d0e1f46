"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const Rank_1 = require("../module/rank/bll/Rank");
const MongoDB_1 = require("../module/common/MongoDB");
async function clearAndSetupRanking() {
    console.log('🧹 开始清理并重新设置排行榜数据...');
    try {
        // 初始化
        await MongoDB_1.MongoDB.init();
        Rank_1.Rank.init();
        console.log('\n🗑️ 清理错误的排行榜数据...');
        // 清理世界排行榜中的错误测试数据
        await clearCollection(MongoDB_1.RankDbCollectionName.worldRank);
        // 清理所有缓存
        await Rank_1.Rank.clearAllCache();
        console.log('\n🏗️ 创建正确的排行榜数据...');
        // 1. 创建国家排行榜数据 (玩家数据)
        await createCountryRankingData();
        // 2. 创建城市排行榜数据 (玩家数据) 
        await createCityRankingData();
        // 3. 创建国家内城市排行榜数据 (城市聚合数据)
        await createCountryCityRankingData();
        // 4. 创建世界排行榜数据 (国家聚合数据)
        await createWorldRankingData();
        console.log('\n✅ 排行榜数据设置完成！');
        // 验证数据
        await verifyRankingData();
    }
    catch (error) {
        console.error('❌ 设置失败:', error);
    }
}
async function clearCollection(collectionName) {
    try {
        const db = MongoDB_1.MongoDB.rankDb;
        await db.collection(collectionName).deleteMany({});
        console.log(`🧹 已清理集合: ${collectionName}`);
    }
    catch (error) {
        console.error(`清理集合 ${collectionName} 失败:`, error);
    }
}
async function createCountryRankingData() {
    console.log('📊 创建国家排行榜数据...');
    const countries = [
        { code: 'CN', players: 30 },
        { code: 'US', players: 25 },
        { code: 'JP', players: 20 },
        { code: 'KR', players: 15 },
        { code: 'GB', players: 12 }
    ];
    for (const country of countries) {
        console.log(`  📍 创建 ${country.code} 国家排行榜 (${country.players} 玩家)`);
        for (let i = 1; i <= country.players; i++) {
            const mockUser = {
                guuid: `${country.code}_player_${i}`,
                countryCode: country.code,
                nickName: `${country.code}Player${i}`,
                avatarId: Math.floor(Math.random() * 10) + 1,
                avatar: '',
                currCountryPassTimes: Math.floor(Math.random() * 50) + 1
            };
            await Rank_1.Rank.UpdateCountryScore(mockUser);
        }
    }
}
async function createCityRankingData() {
    console.log('🏙️ 创建城市排行榜数据...');
    const cities = [
        // 中国城市
        { countryCode: 'CN', cityCode: 'sz', cityName: '深圳', players: 8 },
        { countryCode: 'CN', cityCode: 'gz', cityName: '广州', players: 7 },
        { countryCode: 'CN', cityCode: 'bj', cityName: '北京', players: 9 },
        { countryCode: 'CN', cityCode: 'sh', cityName: '上海', players: 6 },
        // 美国城市
        { countryCode: 'US', cityCode: 'nyc', cityName: 'New York', players: 8 },
        { countryCode: 'US', cityCode: 'la', cityName: 'Los Angeles', players: 9 },
        { countryCode: 'US', cityCode: 'chicago', cityName: 'Chicago', players: 8 },
        // 日本城市
        { countryCode: 'JP', cityCode: 'tokyo', cityName: '东京', players: 10 },
        { countryCode: 'JP', cityCode: 'osaka', cityName: '大阪', players: 6 },
        { countryCode: 'JP', cityCode: 'kyoto', cityName: '京都', players: 4 },
    ];
    for (const city of cities) {
        console.log(`  🏙️ 创建 ${city.cityName} 城市排行榜 (${city.players} 玩家)`);
        for (let i = 1; i <= city.players; i++) {
            const mockUser = {
                guuid: `${city.cityCode}_player_${i}`,
                countryCode: city.countryCode,
                nickName: `${city.cityName}Player${i}`,
                avatarId: Math.floor(Math.random() * 10) + 1,
                avatar: '',
                currCountryPassTimes: Math.floor(Math.random() * 30) + 1
            };
            await Rank_1.Rank.UpdateCityScore(mockUser, city.cityCode);
        }
    }
}
async function createCountryCityRankingData() {
    console.log('🌆 创建国家内城市排行榜数据...');
    const countryCities = [
        // 中国城市
        { countryCode: 'CN', cities: [
                { cityCode: 'sz', cityName: '深圳' },
                { cityCode: 'gz', cityName: '广州' },
                { cityCode: 'bj', cityName: '北京' },
                { cityCode: 'sh', cityName: '上海' }
            ] },
        // 美国城市  
        { countryCode: 'US', cities: [
                { cityCode: 'nyc', cityName: 'New York' },
                { cityCode: 'la', cityName: 'Los Angeles' },
                { cityCode: 'chicago', cityName: 'Chicago' }
            ] },
        // 日本城市
        { countryCode: 'JP', cities: [
                { cityCode: 'tokyo', cityName: '东京' },
                { cityCode: 'osaka', cityName: '大阪' },
                { cityCode: 'kyoto', cityName: '京都' }
            ] }
    ];
    for (const country of countryCities) {
        console.log(`  🌆 创建 ${country.countryCode} 国家内城市排行榜`);
        for (const city of country.cities) {
            await Rank_1.Rank.UpdateCountryCityScore(country.countryCode, city.cityCode, city.cityName);
        }
    }
}
async function createWorldRankingData() {
    console.log('🌍 创建世界排行榜数据 (各国家比拼)...');
    const countries = ['CN', 'US', 'JP', 'KR', 'GB'];
    for (const countryCode of countries) {
        console.log(`  🌍 更新 ${countryCode} 在世界排行榜中的数据`);
        await Rank_1.Rank.UpdateWorldScore(countryCode);
    }
}
async function verifyRankingData() {
    console.log('\n🔍 验证排行榜数据...');
    // 验证世界排行榜
    console.log('\n=== 世界排行榜验证 (应显示国家代码) ===');
    const worldRanking = await Rank_1.Rank.getWorldList();
    if (worldRanking.length > 0) {
        worldRanking.slice(0, 5).forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        // 验证是否显示国家代码
        const hasCorrectFormat = worldRanking.every(record => record.name && record.name.length <= 3 && record.name === record.id);
        if (hasCorrectFormat) {
            console.log('✅ 世界排行榜格式正确：显示国家代码');
        }
        else {
            console.log('❌ 世界排行榜格式错误：应显示国家代码');
        }
    }
    else {
        console.log('❌ 世界排行榜无数据');
    }
    // 验证中国国家排行榜
    console.log('\n=== 中国国家排行榜验证 (应显示玩家名字) ===');
    const cnCountryRanking = await Rank_1.Rank.getCountryList('CN');
    if (cnCountryRanking.length > 0) {
        cnCountryRanking.slice(0, 3).forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        console.log(`✅ 中国国家排行榜：${cnCountryRanking.length} 条玩家数据`);
    }
    else {
        console.log('❌ 中国国家排行榜无数据');
    }
    // 验证中国城市排行榜
    console.log('\n=== 中国城市排行榜验证 (应显示城市名字) ===');
    const cnCityRanking = await Rank_1.Rank.getCountryCityList('CN');
    if (cnCityRanking.length > 0) {
        cnCityRanking.forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} (${record.id}) - 分数: ${record.score}`);
        });
        console.log(`✅ 中国城市排行榜：${cnCityRanking.length} 条城市数据`);
    }
    else {
        console.log('❌ 中国城市排行榜无数据');
    }
}
// 运行脚本
clearAndSetupRanking().then(() => {
    console.log('\n🎉 排行榜数据设置完成！');
    process.exit(0);
}).catch(error => {
    console.error('\n💥 设置失败:', error);
    process.exit(1);
});
