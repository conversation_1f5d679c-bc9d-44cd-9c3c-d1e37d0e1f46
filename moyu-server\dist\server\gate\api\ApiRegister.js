"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiRegister = ApiRegister;
const UserService_1 = require("../../../module/account/bll/UserService"); // 引入 UserService
async function ApiRegister(call) {
    console.log('🔍 [Register] 收到注册请求:', JSON.stringify(call.req, null, 2));
    // 插入用户数据
    let platformType = call.req.platformType;
    let platform = call.req.platform;
    let ip = call.conn.ip;
    let isGuest = call.req.isGuest;
    let result = await UserService_1.UserService.registerUser(isGuest, false, ip, platform, platformType, undefined, call.req.userName, call.req.passWord);
    if (!result) {
        console.log('❌ [Register] 注册失败，返回错误');
        call.error('common_server_error'); // 返回错误信息
        return; // 结束函数
    }
    console.log('✅ [Register] 注册成功，返回数据:', JSON.stringify(result, null, 2));
    console.log('📝 [Register] 返回数据类型:', typeof result);
    console.log('📝 [Register] 数据结构检查:', {
        hasGuuid: 'guuid' in result,
        hasUserName: 'userName' in result,
        hasPassWord: 'passWord' in result,
        guuidType: typeof result.guuid,
        userNameType: typeof result.userName,
        passWordType: typeof result.passWord
    });
    // 返回客户端结果
    call.succ(result);
}
