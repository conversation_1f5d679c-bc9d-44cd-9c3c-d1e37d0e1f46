"use strict";
/*
 * 简化统一服务器 - 纯HTTP架构，适配三消游戏
 * 双端口设计：5000(网关) + 5001(游戏)，避免协议冲突
 * 使用Redis优化会话管理
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
const chalk_1 = __importDefault(require("chalk"));
const tsrpc_1 = require("tsrpc");
const ServiceProtoGame_1 = require("./tsrpc/protocols/ServiceProtoGame");
const ServiceProtoGate_1 = require("./tsrpc/protocols/ServiceProtoGate");
const ShareConfig_1 = require("./tsrpc/models/ShareConfig");
const MongoDB_1 = require("./module/common/MongoDB");
const RedisManager_1 = require("./module/common/RedisManager");
const RedisSessionManager_1 = require("./module/common/RedisSessionManager");
const User_1 = require("./module/account/bll/User");
const Rank_1 = require("./module/rank/bll/Rank");
const Config_1 = require("./module/config/Config");
async function startSimpleUnifiedServer() {
    try {
        console.log(chalk_1.default.green('🚀 启动统一服务器...'));
        // 仅在开发环境显示详细配置信息
        if (!ShareConfig_1.ShareConfig.isProduction) {
            console.log(chalk_1.default.cyan('🔍 环境配置:'));
            console.log(chalk_1.default.cyan(`   环境: ${ShareConfig_1.ShareConfig.environment}`));
            console.log(chalk_1.default.cyan(`   平台: ${ShareConfig_1.ShareConfig.platform}`));
            console.log(chalk_1.default.cyan(`   端口: ${ShareConfig_1.ShareConfig.port}`));
            console.log(chalk_1.default.cyan(`   MongoDB: ${ShareConfig_1.ShareConfig.mongoDbName}`));
        }
        // 初始化开发环境配置
        (0, Config_1.dev)();
        // 连接数据库
        await MongoDB_1.MongoDB.init();
        User_1.User.init();
        Rank_1.Rank.init();
        console.log(chalk_1.default.green('✅ 数据库连接成功'));
        // 初始化Redis
        await RedisManager_1.RedisManager.init();
        await RedisSessionManager_1.RedisSessionManager.init();
        console.log(chalk_1.default.green('✅ Redis连接成功'));
        // 身份验证中间件
        const createAuthMiddleware = () => {
            return async (call) => {
                let conf = call.service.conf;
                let req = call.req;
                call.dbUser = null;
                if (req.__ssoToken) {
                    const user = await RedisSessionManager_1.RedisSessionManager.getSession(req.__ssoToken);
                    if (user) {
                        call.dbUser = user;
                        if (!ShareConfig_1.ShareConfig.isProduction) {
                            console.log(chalk_1.default.gray(`🔐 [认证] 用户已登录: ${user.userName}`));
                        }
                    }
                }
                if ((conf === null || conf === void 0 ? void 0 : conf.needCheckAddress) && !Config_1.Config.ips[call.conn.ip]) {
                    await call.error('没有访问权限', { code: 'NEED_AUTHORITY' });
                    return null;
                }
                else if ((conf === null || conf === void 0 ? void 0 : conf.needLogin) && !call.dbUser) {
                    if (!ShareConfig_1.ShareConfig.isProduction) {
                        console.log(chalk_1.default.red(`❌ [认证] API ${call.service.name} 需要登录`));
                    }
                    await call.error('登录后获取访问权限', { code: 'NEED_LOGIN' });
                    return null;
                }
                return call;
            };
        };
        // 数据同步中间件
        const createDataSyncMiddleware = () => {
            return {
                postApiReturnFlow: async (value) => {
                    const call = value.call;
                    const req = call.req;
                    if (value.return.isSucc && call.dbUser && req.__ssoToken) {
                        try {
                            const updated = await RedisSessionManager_1.RedisSessionManager.updateSession(req.__ssoToken, call.dbUser);
                            if (updated && !ShareConfig_1.ShareConfig.isProduction) {
                                console.log(chalk_1.default.blue(`🔄 [数据同步] ${call.service.name} 已同步会话数据`));
                            }
                        }
                        catch (error) {
                            if (!ShareConfig_1.ShareConfig.isProduction) {
                                console.warn(chalk_1.default.yellow(`⚠️ [数据同步] ${call.service.name} 同步失败:`, error));
                            }
                        }
                    }
                    return value;
                }
            };
        };
        // 创建CORS中间件
        const createCorsMiddleware = () => {
            return async (call) => {
                var _a, _b;
                const httpCall = call;
                const origin = (_a = httpCall.conn.httpReq) === null || _a === void 0 ? void 0 : _a.headers.origin;
                if (!ShareConfig_1.ShareConfig.isProduction) {
                    console.log(chalk_1.default.gray(`🌐 [CORS] 请求来源: ${origin}`));
                }
                let isAllowed = false;
                let allowedOriginValue = '';
                if (origin) {
                    for (const allowedOrigin of ShareConfig_1.ShareConfig.corsOrigins) {
                        if (allowedOrigin === '*' || allowedOrigin === origin) {
                            isAllowed = true;
                            allowedOriginValue = origin;
                            break;
                        }
                    }
                }
                if (isAllowed && httpCall.conn.httpRes && allowedOriginValue) {
                    httpCall.conn.httpRes.setHeader('Access-Control-Allow-Origin', allowedOriginValue);
                    httpCall.conn.httpRes.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
                    httpCall.conn.httpRes.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-SSO-Token');
                    httpCall.conn.httpRes.setHeader('Access-Control-Allow-Credentials', 'true');
                    httpCall.conn.httpRes.setHeader('Access-Control-Max-Age', '86400');
                }
                if (((_b = httpCall.conn.httpReq) === null || _b === void 0 ? void 0 : _b.method) === 'OPTIONS') {
                    if (isAllowed && httpCall.conn.httpRes) {
                        httpCall.conn.httpRes.statusCode = 200;
                        httpCall.conn.httpRes.end();
                        return null;
                    }
                }
                return call;
            };
        };
        // 创建网关服务器 (端口5000)
        const gateServer = new tsrpc_1.HttpServer(ServiceProtoGate_1.serviceProto, {
            port: ShareConfig_1.ShareConfig.port,
            json: true,
            logLevel: ShareConfig_1.ShareConfig.isProduction ? 'error' : 'debug'
        });
        gateServer.flows.preApiCallFlow.push(createCorsMiddleware());
        gateServer.flows.preApiCallFlow.push(createAuthMiddleware());
        // 健康检查
        gateServer.flows.preApiCallFlow.push(async (call) => {
            var _a;
            const httpCall = call;
            if (((_a = httpCall.conn.httpReq) === null || _a === void 0 ? void 0 : _a.url) === '/health') {
                httpCall.conn.httpRes.statusCode = 200;
                httpCall.conn.httpRes.setHeader('Content-Type', 'text/plain');
                httpCall.conn.httpRes.end('gateway-healthy\n');
                return null;
            }
            return call;
        });
        await gateServer.autoImplementApi(path_1.default.resolve(__dirname, 'server/gate/api'), true);
        // 创建游戏服务器 (端口5001)
        const gameServer = new tsrpc_1.HttpServer(ServiceProtoGame_1.serviceProto, {
            port: ShareConfig_1.ShareConfig.port + 1,
            json: true,
            logLevel: ShareConfig_1.ShareConfig.isProduction ? 'error' : 'debug'
        });
        gameServer.flows.preApiCallFlow.push(createCorsMiddleware());
        gameServer.flows.preApiCallFlow.push(createAuthMiddleware());
        const dataSyncMiddleware = createDataSyncMiddleware();
        gameServer.flows.postApiReturnFlow.push(dataSyncMiddleware.postApiReturnFlow);
        // 健康检查
        gameServer.flows.preApiCallFlow.push(async (call) => {
            var _a;
            const httpCall = call;
            if (((_a = httpCall.conn.httpReq) === null || _a === void 0 ? void 0 : _a.url) === '/health') {
                httpCall.conn.httpRes.statusCode = 200;
                httpCall.conn.httpRes.setHeader('Content-Type', 'text/plain');
                httpCall.conn.httpRes.end('game-healthy\n');
                return null;
            }
            return call;
        });
        await gameServer.autoImplementApi(path_1.default.resolve(__dirname, 'server/game/api'), true);
        // 启动服务器
        await Promise.all([
            gateServer.start(),
            gameServer.start()
        ]);
        console.log(chalk_1.default.green(`✅ 网关服务器: http://localhost:${ShareConfig_1.ShareConfig.port}`));
        console.log(chalk_1.default.green(`✅ 游戏服务器: http://localhost:${ShareConfig_1.ShareConfig.port + 1}`));
        console.log(chalk_1.default.green(`🌟 ${ShareConfig_1.ShareConfig.environment} 环境就绪`));
        // 会话统计（仅开发环境）
        if (!ShareConfig_1.ShareConfig.isProduction) {
            setInterval(async () => {
                const stats = await RedisSessionManager_1.RedisSessionManager.getSessionStats();
                console.log(chalk_1.default.blue(`📊 会话统计: 总会话=${stats.totalSessions}, 活跃用户=${stats.activeUsers}`));
            }, 5 * 60 * 1000);
        }
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ 服务器启动失败:'), error);
        process.exit(1);
    }
}
startSimpleUnifiedServer();
