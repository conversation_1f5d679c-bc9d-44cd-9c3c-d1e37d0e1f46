"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiUpdateProgress = ApiUpdateProgress;
const User_1 = require("../../../module/account/bll/User");
const UserUtil_1 = require("../../../module/common/UserUtil");
const Config_1 = require("../../../module/config/Config");
const base_1 = require("../../../tsrpc/protocols/base");
async function ApiUpdateProgress(call) {
    const { dbUser, req } = call;
    if (!dbUser) {
        return call.error('用户未找到', { code: 'USER_NOT_FOUND' });
    }
    const { index, passTimesIncrement, isGm } = req;
    // GM模式检查
    if (isGm && !Config_1.Config.game.gmOpen) {
        return call.error('GM模式未开启', { code: 'GM_MODE_DISABLED' });
    }
    const changes = {};
    try {
        // 处理关卡进度更新
        if (index !== undefined) {
            if (index < 0) {
                return call.error('无效的关卡索引', { code: 'INVALID_INDEX' });
            }
            // 非GM模式下，验证关卡顺序
            if (!isGm && index !== (dbUser.index + 1)) {
                return call.error('关卡顺序错误', { code: 'INVALID_PASS_ORDER' });
            }
            // 更新关卡进度
            dbUser.index = index;
            changes.index = index;
            // 触发通关逻辑
            UserUtil_1.UserUtil.onGamePass(dbUser);
            changes.passTimes = dbUser.passTimes;
            changes.currCountryPassTimes = dbUser.currCountryPassTimes;
        }
        // 处理通关次数增量
        if (passTimesIncrement !== undefined && passTimesIncrement > 0) {
            dbUser.passTimes += passTimesIncrement;
            dbUser.currCountryPassTimes += passTimesIncrement;
            changes.passTimes = dbUser.passTimes;
            changes.currCountryPassTimes = dbUser.currCountryPassTimes;
        }
        // 更新排名（如果有变化）
        if (changes.passTimes !== undefined) {
            // 这里可以异步更新排名，避免阻塞响应
            UserUtil_1.UserUtil.updateUserRank(dbUser).then((rank) => {
                if (rank !== dbUser.selfCountryRank) {
                    dbUser.selfCountryRank = rank;
                    // 可以发送消息通知客户端排名变化
                }
            }).catch(console.error);
        }
        // 保存到数据库
        await User_1.User.updateUserData(dbUser._id, {
            index: dbUser.index,
            passTimes: dbUser.passTimes,
            currCountryPassTimes: dbUser.currCountryPassTimes,
            selfCountryRank: dbUser.selfCountryRank
        });
        // 记录操作日志
        console.log(`[ProgressUpdate] User ${dbUser.key} updated:`, changes);
        // 返回增量更新
        return call.succ({
            code: 0,
            message: '进度更新成功',
            updateType: base_1.DataUpdateType.PROGRESS,
            timestamp: Date.now(),
            changes
        });
    }
    catch (error) {
        console.error('进度更新失败:', error);
        return call.error('进度更新失败', { code: 'UPDATE_FAILED' });
    }
}
