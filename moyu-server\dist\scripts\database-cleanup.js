"use strict";
/*
 * 数据库清理和重新初始化脚本
 * 用于清空现有数据，为新的简化架构做准备
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseCleanup = void 0;
const MongoDB_1 = require("../module/common/MongoDB");
class DatabaseCleanup {
    /**
     * 清空所有游戏数据
     * 注意：这是不可逆操作，请确保已备份重要数据
     */
    static async clearAllData() {
        console.log('🚨 警告：即将清空所有游戏数据！');
        console.log('请确保您已经备份了重要数据...');
        try {
            // 连接数据库
            await MongoDB_1.MongoDB.init();
            // 清空用户表
            console.log('清空用户表...');
            const userResult = await MongoDB_1.MongoDB.db.collection('user').deleteMany({});
            console.log(`✅ 已删除 ${userResult.deletedCount} 个用户记录`);
            // 清空计数器表
            console.log('清空计数器表...');
            const counterResult = await MongoDB_1.MongoDB.db.collection('counters').deleteMany({});
            console.log(`✅ 已删除 ${counterResult.deletedCount} 个计数器记录`);
            // 清空排行榜数据
            console.log('清空排行榜数据...');
            const collections = await MongoDB_1.MongoDB.db.listCollections().toArray();
            let rankCollectionsCleared = 0;
            for (const collection of collections) {
                const name = collection.name;
                // 清理排行榜相关的集合（通常以国家代码命名或包含rank）
                if (name.length === 2 || name.includes('rank') || name.includes('leaderboard')) {
                    const result = await MongoDB_1.MongoDB.db.collection(name).deleteMany({});
                    if (result.deletedCount > 0) {
                        console.log(`✅ 已清空排行榜集合 ${name}: ${result.deletedCount} 条记录`);
                        rankCollectionsCleared++;
                    }
                }
            }
            console.log(`✅ 总共清空了 ${rankCollectionsCleared} 个排行榜集合`);
            // 重新初始化计数器
            console.log('重新初始化计数器...');
            await MongoDB_1.MongoDB.db.collection('counters').insertOne({
                key: 'user',
                value: 0
            });
            console.log('✅ 计数器初始化完成');
            console.log('🎉 数据库清理完成！');
        }
        catch (error) {
            console.error('❌ 数据库清理失败:', error);
            throw error;
        }
    }
    /**
     * 清理虚假排行榜数据
     * 保留真实用户数据，只删除虚假的排行榜数据
     */
    static async clearFakeRankData() {
        console.log('清理虚假排行榜数据...');
        try {
            await MongoDB_1.MongoDB.init();
            const collections = await MongoDB_1.MongoDB.db.listCollections().toArray();
            let clearedCount = 0;
            for (const collection of collections) {
                const name = collection.name;
                if (name.length === 2 || name.includes('rank') || name.includes('leaderboard')) {
                    // 这里可以添加更精确的过滤条件来识别虚假数据
                    // 例如，删除特定时间之前创建的数据，或者删除特定格式的假数据
                    const result = await MongoDB_1.MongoDB.db.collection(name).deleteMany({
                    // 可以根据实际情况添加过滤条件
                    // 例如：name: { $regex: /fake|test|模拟/ }
                    });
                    if (result.deletedCount > 0) {
                        console.log(`✅ 已清理 ${name} 集合: ${result.deletedCount} 条记录`);
                        clearedCount += result.deletedCount;
                    }
                }
            }
            console.log(`🎉 虚假排行榜数据清理完成，总共清理了 ${clearedCount} 条记录`);
        }
        catch (error) {
            console.error('❌ 虚假数据清理失败:', error);
            throw error;
        }
    }
    /**
     * 数据库健康检查
     */
    static async healthCheck() {
        console.log('进行数据库健康检查...');
        try {
            await MongoDB_1.MongoDB.init();
            // 检查用户表
            const userCount = await MongoDB_1.MongoDB.db.collection('user').countDocuments();
            console.log(`📊 用户数量: ${userCount}`);
            // 检查计数器表
            const counterCount = await MongoDB_1.MongoDB.db.collection('counters').countDocuments();
            console.log(`📊 计数器数量: ${counterCount}`);
            // 检查排行榜集合
            const collections = await MongoDB_1.MongoDB.db.listCollections().toArray();
            const rankCollections = collections.filter(c => c.name.length === 2 || c.name.includes('rank') || c.name.includes('leaderboard'));
            console.log(`📊 排行榜集合数量: ${rankCollections.length}`);
            for (const collection of rankCollections) {
                const count = await MongoDB_1.MongoDB.db.collection(collection.name).countDocuments();
                console.log(`  - ${collection.name}: ${count} 条记录`);
            }
            console.log('✅ 数据库健康检查完成');
        }
        catch (error) {
            console.error('❌ 数据库健康检查失败:', error);
            throw error;
        }
    }
}
exports.DatabaseCleanup = DatabaseCleanup;
// 如果直接运行此脚本
if (require.main === module) {
    async function main() {
        const args = process.argv.slice(2);
        const command = args[0];
        switch (command) {
            case 'clear-all':
                console.log('⚠️  即将清空所有数据，请在 10 秒内按 Ctrl+C 取消...');
                await new Promise(resolve => setTimeout(resolve, 10000));
                await DatabaseCleanup.clearAllData();
                break;
            case 'clear-fake':
                await DatabaseCleanup.clearFakeRankData();
                break;
            case 'health-check':
                await DatabaseCleanup.healthCheck();
                break;
            default:
                console.log('用法:');
                console.log('  npm run db-cleanup clear-all      # 清空所有数据');
                console.log('  npm run db-cleanup clear-fake     # 清空虚假数据');
                console.log('  npm run db-cleanup health-check   # 数据库健康检查');
                break;
        }
        process.exit(0);
    }
    main().catch(console.error);
}
