"use strict";
/*
 * 开发环境数据清档脚本
 * 警告：此脚本会清空所有数据，仅用于开发调试！
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MongoDB_1 = require("../module/common/MongoDB");
const RedisManager_1 = require("../module/common/RedisManager");
const RedisSessionManager_1 = require("../module/common/RedisSessionManager");
const User_1 = require("../module/account/bll/User");
const Rank_1 = require("../module/rank/bll/Rank");
const Config_1 = require("../module/config/Config");
const chalk_1 = __importDefault(require("chalk"));
async function clearAllData() {
    console.log(chalk_1.default.red('🚨 开发环境数据清档工具'));
    console.log(chalk_1.default.red('⚠️  警告：此操作将清空所有数据！'));
    console.log(chalk_1.default.yellow('📝 请确认这是开发环境，生产环境禁止使用！'));
    console.log('');
    try {
        // 初始化开发环境配置
        (0, Config_1.dev)();
        // 连接数据库
        console.log(chalk_1.default.cyan('🔌 连接数据库...'));
        await MongoDB_1.MongoDB.init();
        User_1.User.init();
        Rank_1.Rank.init();
        // 连接Redis
        console.log(chalk_1.default.cyan('🔌 连接Redis...'));
        await RedisManager_1.RedisManager.init();
        await RedisSessionManager_1.RedisSessionManager.init();
        console.log(chalk_1.default.green('✅ 数据库连接成功'));
        console.log('');
        // 开始清理数据
        console.log(chalk_1.default.yellow('🧹 开始清理数据...'));
        // 1. 清空MongoDB所有集合
        console.log(chalk_1.default.cyan('📋 清空MongoDB集合...'));
        const db = MongoDB_1.MongoDB.db;
        // 获取所有集合名称
        const collections = await db.listCollections().toArray();
        console.log(chalk_1.default.gray(`   发现 ${collections.length} 个集合`));
        for (const collection of collections) {
            const collectionName = collection.name;
            try {
                const result = await db.collection(collectionName).deleteMany({});
                console.log(chalk_1.default.gray(`   ✓ 清空集合 [${collectionName}]: 删除 ${result.deletedCount} 条记录`));
            }
            catch (error) {
                console.log(chalk_1.default.red(`   ✗ 清空集合 [${collectionName}] 失败: ${error}`));
            }
        }
        // 2. 清空Redis所有数据
        console.log(chalk_1.default.cyan('🗄️  清空Redis缓存...'));
        try {
            const redis = RedisManager_1.RedisManager.getClient();
            await redis.flushAll();
            console.log(chalk_1.default.gray('   ✓ Redis缓存已清空'));
        }
        catch (error) {
            console.log(chalk_1.default.red(`   ✗ 清空Redis失败: ${error}`));
        }
        // 3. 清空会话数据
        console.log(chalk_1.default.cyan('👥 清空会话数据...'));
        try {
            // 直接通过Redis清空会话相关的键
            const redis = RedisManager_1.RedisManager.getClient();
            const sessionKeys = await redis.keys('session:*');
            const userKeys = await redis.keys('user:*');
            const allKeys = [...sessionKeys, ...userKeys];
            if (allKeys.length > 0) {
                await redis.del(allKeys);
                console.log(chalk_1.default.gray(`   ✓ 会话数据已清空 (${allKeys.length} 个键)`));
            }
            else {
                console.log(chalk_1.default.gray('   ✓ 没有会话数据需要清空'));
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`   ✗ 清空会话数据失败: ${error}`));
        }
        // 4. 清空排行榜缓存
        console.log(chalk_1.default.cyan('🏆 清空排行榜缓存...'));
        try {
            await Rank_1.Rank.clearAllCache();
            console.log(chalk_1.default.gray('   ✓ 排行榜缓存已清空'));
        }
        catch (error) {
            console.log(chalk_1.default.red(`   ✗ 清空排行榜缓存失败: ${error}`));
        }
        console.log('');
        console.log(chalk_1.default.green('🎉 数据清档完成！'));
        console.log(chalk_1.default.yellow('💡 提示：'));
        console.log(chalk_1.default.yellow('   - 所有用户数据已清空'));
        console.log(chalk_1.default.yellow('   - 所有排行榜数据已清空'));
        console.log(chalk_1.default.yellow('   - 所有缓存数据已清空'));
        console.log(chalk_1.default.yellow('   - 所有会话数据已清空'));
        console.log(chalk_1.default.yellow('   - 数据库现在是全新状态'));
        console.log('');
        // 5. 生成清理报告
        const timestamp = new Date().toISOString();
        console.log(chalk_1.default.blue(`📊 清理报告 [${timestamp}]:`));
        console.log(chalk_1.default.blue(`   MongoDB集合数: ${collections.length}`));
        console.log(chalk_1.default.blue(`   清理时间: ${timestamp}`));
        console.log(chalk_1.default.blue(`   环境: 开发环境`));
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ 清档过程中发生错误:'), error);
        process.exit(1);
    }
    finally {
        // 关闭连接
        try {
            await RedisSessionManager_1.RedisSessionManager.close();
            await RedisManager_1.RedisManager.close();
            console.log(chalk_1.default.gray('🔌 数据库连接已关闭'));
        }
        catch (error) {
            console.error(chalk_1.default.red('关闭连接时出错:'), error);
        }
        process.exit(0);
    }
}
// 添加确认机制
function confirmClearData() {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    return new Promise((resolve) => {
        rl.question(chalk_1.default.red('⚠️  确认要清空所有数据吗？请输入 "CLEAR_ALL" 确认: '), (answer) => {
            rl.close();
            resolve(answer === 'CLEAR_ALL');
        });
    });
}
// 主入口
async function main() {
    console.clear();
    // 环境检查
    if (process.env.NODE_ENV === 'production') {
        console.error(chalk_1.default.red('❌ 错误：生产环境禁止使用清档脚本！'));
        process.exit(1);
    }
    // 确认操作
    const confirmed = await confirmClearData();
    if (!confirmed) {
        console.log(chalk_1.default.yellow('❌ 操作已取消'));
        process.exit(0);
    }
    console.log('');
    await clearAllData();
}
// 运行脚本
main().catch(error => {
    console.error(chalk_1.default.red('脚本执行失败:'), error);
    process.exit(1);
});
