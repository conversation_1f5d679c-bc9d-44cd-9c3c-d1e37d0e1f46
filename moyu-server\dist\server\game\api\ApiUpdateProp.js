"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiUpdateProp = ApiUpdateProp;
const User_1 = require("../../../module/account/bll/User");
const UserUtil_1 = require("../../../module/common/UserUtil");
const base_1 = require("../../../tsrpc/protocols/base");
async function ApiUpdateProp(call) {
    const { dbUser, req } = call;
    if (!dbUser) {
        return call.error('用户未找到', { code: 'USER_NOT_FOUND' });
    }
    const { propType, amount, reason } = req;
    // 验证道具类型
    if (!Object.values(base_1.PropType).includes(propType)) {
        return call.error('无效的道具类型', { code: 'INVALID_PROP_TYPE' });
    }
    // 获取当前道具数据
    const currentProp = dbUser.propUseData[propType];
    if (!currentProp) {
        return call.error('道具数据不存在', { code: 'PROP_DATA_NOT_FOUND' });
    }
    const oldAmount = currentProp.amount;
    const newAmount = oldAmount + amount;
    // 验证道具数量不能为负数
    if (newAmount < 0) {
        return call.error('道具数量不足', { code: 'INSUFFICIENT_PROPS' });
    }
    // 更新道具数据
    currentProp.amount = newAmount;
    currentProp.lastUpdateTime = new Date();
    // 🔧 新增：如果是扣除挑战次数（PropsDayLeftCount），更新记录数据
    if (propType === base_1.PropType.PropsDayLeftCount && amount < 0) {
        console.log(`📊 更新挑战记录：用户${dbUser.key} 扣除${Math.abs(amount)}次挑战次数`);
        // 🔧 获取当前关卡ID（基于用户进度计算）
        const currentLevelId = ((dbUser.index || 0) % 999) + 1; // 简化的关卡循环逻辑，假设最多999关
        UserUtil_1.UserUtil.updateRecordData(dbUser, propType, amount, undefined, currentLevelId);
    }
    // 保存到数据库
    const updateResult = await User_1.User.updateUserData(dbUser._id, {
        propUseData: dbUser.propUseData,
        recordData: dbUser.recordData, // 🔧 新增：保存记录数据
    });
    if (!updateResult) {
        return call.error('数据保存失败', { code: 'DATABASE_UPDATE_FAILED' });
    }
    // 返回成功结果
    call.succ({
        code: 0,
        message: '道具更新成功',
        updateType: base_1.DataUpdateType.PROP,
        timestamp: Date.now(),
        changes: {
            propType: propType,
            newAmount: newAmount,
            changeAmount: amount,
            lastUpdateTime: currentProp.lastUpdateTime,
        },
    });
}
