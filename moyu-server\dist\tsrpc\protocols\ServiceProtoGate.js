"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceProto = void 0;
exports.serviceProto = {
    "version": 41,
    "services": [
        {
            "id": 4,
            "name": "admin/GameServerJoin",
            "type": "api",
            "conf": {
                "needCheckAddress": true
            }
        },
        {
            "id": 7,
            "name": "AuthLoginRes",
            "type": "msg"
        },
        {
            "id": 9,
            "name": "FacebookLogin",
            "type": "api"
        },
        {
            "id": 2,
            "name": "GameArea",
            "type": "api"
        },
        {
            "id": 8,
            "name": "GameGenTestRankData",
            "type": "api"
        },
        {
            "id": 0,
            "name": "Login",
            "type": "api"
        },
        {
            "id": 1,
            "name": "Register",
            "type": "api"
        }
    ],
    "types": {
        "admin/PtlGameServerJoin/ReqGameServerJoin": {
            "type": "Interface",
            "properties": [
                {
                    "id": 1,
                    "name": "wsServerUrl",
                    "type": {
                        "type": "String"
                    }
                }
            ]
        },
        "admin/PtlGameServerJoin/ResGameServerJoin": {
            "type": "Interface"
        },
        "MsgAuthLoginRes/MsgAuthLoginRes": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "guuid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 1,
                    "name": "success",
                    "type": {
                        "type": "Boolean"
                    }
                },
                {
                    "id": 2,
                    "name": "error",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 3,
                    "name": "__ssoToken",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "../base/BaseResponse": {
            "type": "Interface",
            "properties": [
                {
                    "id": 1,
                    "name": "code",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 2,
                    "name": "message",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 0,
                    "name": "__ssoToken",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "PtlFacebookLogin/ReqFacebookLogin": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "facebookId",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 1,
                    "name": "playerName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "playerPhoto",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 3,
                    "name": "locale",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 4,
                    "name": "countryCode",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 5,
                    "name": "platform",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 6,
                    "name": "platformType",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 8,
                    "name": "httpUrl",
                    "type": {
                        "type": "String"
                    }
                }
            ]
        },
        "PtlFacebookLogin/ResFacebookLogin": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseResponse"
                    }
                }
            ],
            "properties": [
                {
                    "id": 0,
                    "name": "guuid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 1,
                    "name": "userName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "isNewUser",
                    "type": {
                        "type": "Boolean"
                    }
                }
            ]
        },
        "PtlGameArea/ReqGameArea": {
            "type": "Interface"
        },
        "PtlGameArea/ResGameArea": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "area",
                    "type": {
                        "type": "Interface",
                        "properties": [
                            {
                                "id": 0,
                                "name": "name",
                                "type": {
                                    "type": "String"
                                }
                            },
                            {
                                "id": 2,
                                "name": "httpUrl",
                                "type": {
                                    "type": "String"
                                }
                            }
                        ]
                    }
                }
            ]
        },
        "PtlGameGenTestRankData/ReqGameGenTestRankData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "rankType",
                    "type": {
                        "type": "Union",
                        "members": [
                            {
                                "id": 0,
                                "type": {
                                    "type": "Literal",
                                    "literal": "country"
                                }
                            },
                            {
                                "id": 1,
                                "type": {
                                    "type": "Literal",
                                    "literal": "city"
                                }
                            },
                            {
                                "id": 2,
                                "type": {
                                    "type": "Literal",
                                    "literal": "world"
                                }
                            }
                        ]
                    }
                },
                {
                    "id": 1,
                    "name": "identifier",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "count",
                    "type": {
                        "type": "Number"
                    },
                    "optional": true
                }
            ]
        },
        "PtlGameGenTestRankData/ResGameGenTestRankData": {
            "type": "Interface",
            "properties": [
                {
                    "id": 0,
                    "name": "code",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 1,
                    "name": "message",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 2,
                    "name": "generatedCount",
                    "type": {
                        "type": "Number"
                    }
                },
                {
                    "id": 3,
                    "name": "rankType",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 4,
                    "name": "identifier",
                    "type": {
                        "type": "String"
                    }
                }
            ]
        },
        "PtlLogin/ReqLogin": {
            "type": "Interface",
            "properties": [
                {
                    "id": 1,
                    "name": "server",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 5,
                    "name": "userName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 6,
                    "name": "passWord",
                    "type": {
                        "type": "String"
                    }
                }
            ]
        },
        "PtlLogin/ResLogin": {
            "type": "Interface",
            "extends": [
                {
                    "id": 0,
                    "type": {
                        "type": "Reference",
                        "target": "../base/BaseResponse"
                    }
                }
            ]
        },
        "PtlRegister/ReqRegister": {
            "type": "Interface",
            "properties": [
                {
                    "id": 1,
                    "name": "platform",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 4,
                    "name": "platformType",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 6,
                    "name": "isGuest",
                    "type": {
                        "type": "Boolean"
                    }
                },
                {
                    "id": 7,
                    "name": "userName",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 8,
                    "name": "passWord",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                },
                {
                    "id": 9,
                    "name": "countryCode",
                    "type": {
                        "type": "String"
                    },
                    "optional": true
                }
            ]
        },
        "PtlRegister/ResRegister": {
            "type": "Interface",
            "properties": [
                {
                    "id": 7,
                    "name": "guuid",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 5,
                    "name": "userName",
                    "type": {
                        "type": "String"
                    }
                },
                {
                    "id": 6,
                    "name": "passWord",
                    "type": {
                        "type": "String"
                    }
                }
            ]
        }
    }
};
