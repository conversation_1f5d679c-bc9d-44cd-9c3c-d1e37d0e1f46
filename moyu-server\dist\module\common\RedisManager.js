"use strict";
/*
 * Redis管理器 - 统一管理Redis连接和操作
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisManager = void 0;
const Redis = __importStar(require("redis"));
const chalk_1 = __importDefault(require("chalk"));
class RedisManager {
    /**
     * 初始化Redis连接
     */
    static async init() {
        try {
            console.log(chalk_1.default.cyan('📝 初始化Redis连接...'));
            // 创建Redis客户端
            this.client = Redis.createClient({
                socket: {
                    host: process.env.REDIS_HOST || '127.0.0.1',
                    port: parseInt(process.env.REDIS_PORT || '6379'),
                    connectTimeout: 10000, // 连接超时10秒
                    reconnectStrategy: (retries) => {
                        const delay = Math.min(retries * 50, 2000);
                        console.log(chalk_1.default.yellow(`⚠️  Redis重连第${retries}次，延迟${delay}ms`));
                        return delay;
                    }
                },
                password: process.env.REDIS_PASSWORD || undefined,
                database: parseInt(process.env.REDIS_DB || '0')
            });
            // 监听连接事件
            this.client.on('connect', () => {
                console.log(chalk_1.default.green('✅ Redis连接建立'));
            });
            this.client.on('ready', () => {
                this.isConnected = true;
                console.log(chalk_1.default.green('✅ Redis连接就绪'));
            });
            this.client.on('error', (error) => {
                console.error(chalk_1.default.red('❌ Redis连接错误:'), error);
                this.isConnected = false;
            });
            this.client.on('end', () => {
                console.log(chalk_1.default.yellow('⚠️  Redis连接断开'));
                this.isConnected = false;
            });
            this.client.on('reconnecting', () => {
                console.log(chalk_1.default.yellow('🔄 Redis重连中...'));
            });
            // 连接Redis
            await this.client.connect();
            // 测试连接
            const pong = await this.client.ping();
            if (pong === 'PONG') {
                console.log(chalk_1.default.green('✅ Redis连接测试成功'));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Redis初始化失败:'), error);
            throw error;
        }
    }
    /**
     * 获取Redis客户端
     */
    static getClient() {
        if (!this.client || !this.isConnected) {
            throw new Error('Redis未连接，请先调用init()');
        }
        return this.client;
    }
    /**
     * 检查连接状态
     */
    static isReady() {
        var _a;
        return this.isConnected && ((_a = this.client) === null || _a === void 0 ? void 0 : _a.isReady);
    }
    /**
     * 设置键值对，支持过期时间
     */
    static async set(key, value, expireSeconds) {
        try {
            if (expireSeconds) {
                await this.client.setEx(key, expireSeconds, value);
            }
            else {
                await this.client.set(key, value);
            }
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Redis SET失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 获取键值
     */
    static async get(key) {
        try {
            return await this.client.get(key);
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Redis GET失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 删除键
     */
    static async del(key) {
        try {
            return await this.client.del(key);
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Redis DEL失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 检查键是否存在
     */
    static async exists(key) {
        try {
            const result = await this.client.exists(key);
            return result > 0;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Redis EXISTS失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 设置过期时间
     */
    static async expire(key, seconds) {
        try {
            const result = await this.client.expire(key, seconds);
            return result === true;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Redis EXPIRE失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 获取剩余过期时间
     */
    static async ttl(key) {
        try {
            return await this.client.ttl(key);
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Redis TTL失败 ${key}:`), error);
            throw error;
        }
    }
    /**
     * 批量删除匹配的键
     */
    static async deletePattern(pattern) {
        try {
            const keys = await this.client.keys(pattern);
            if (keys.length > 0) {
                return await this.client.del(keys);
            }
            return 0;
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Redis删除模式失败 ${pattern}:`), error);
            throw error;
        }
    }
    /**
     * 获取Redis信息
     */
    static async getInfo() {
        try {
            return await this.client.info();
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Redis INFO失败:'), error);
            throw error;
        }
    }
    /**
     * 获取内存使用情况
     */
    static async getMemoryUsage() {
        try {
            const info = await this.client.info('memory');
            const lines = info.split('\r\n');
            const memoryInfo = {};
            lines.forEach(line => {
                if (line.includes(':')) {
                    const [key, value] = line.split(':');
                    memoryInfo[key] = value;
                }
            });
            return {
                usedMemory: memoryInfo.used_memory_human,
                usedMemoryPeak: memoryInfo.used_memory_peak_human,
                totalSystemMemory: memoryInfo.total_system_memory_human,
                usedMemoryRss: memoryInfo.used_memory_rss_human,
                memFragmentationRatio: parseFloat(memoryInfo.mem_fragmentation_ratio)
            };
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Redis内存信息获取失败:'), error);
            throw error;
        }
    }
    /**
     * 关闭连接
     */
    static async close() {
        try {
            if (this.client && this.isConnected) {
                await this.client.quit();
                console.log(chalk_1.default.green('✅ Redis连接已关闭'));
            }
        }
        catch (error) {
            console.error(chalk_1.default.red('❌ Redis关闭失败:'), error);
        }
    }
}
exports.RedisManager = RedisManager;
RedisManager.isConnected = false;
// Redis配置
RedisManager.config = {
    host: process.env.REDIS_HOST || '127.0.0.1',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined,
    database: parseInt(process.env.REDIS_DB || '0'),
    // 连接配置
    connectTimeout: 10000, // 连接超时10秒
    commandTimeout: 5000, // 命令超时5秒
    retryDelayOnFailover: 100, // 故障转移重试延迟
    enableReadyCheck: true, // 启用就绪检查
    maxRetriesPerRequest: 3, // 每个请求最大重试次数
    lazyConnect: true, // 延迟连接
    // 重连配置
    retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        console.log(chalk_1.default.yellow(`⚠️  Redis重连第${times}次，延迟${delay}ms`));
        return delay;
    }
};
