"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Timer = void 0;
const ECS_1 = require("../../core/ecs/ECS");
const Account_1 = require("../account/Account");
const User_1 = require("../account/bll/User");
const TimerModelComp_1 = require("./model/TimerModelComp");
const saveUsersToDatabaseIterval = 5 * 1000; // 一分钟
let Timer = class Timer extends ECS_1.ecs.Entity {
    init() {
        this.addComponents(TimerModelComp_1.TimerModelComp);
    }
    saveUsersToDatabase() {
        setInterval(() => {
            Account_1.account.AccountModel.users.forEach(value => {
                User_1.User.updateUserData(value._id, value).then(res => {
                    if (res) {
                        // console.log(`保存用户${value.userName}数据到数据库成功`);
                    }
                    else {
                        console.log(`保存用户${value.userName}数据到数据库失败`, res);
                    }
                });
            });
        }, saveUsersToDatabaseIterval); // 每1分钟执行一次
    }
};
exports.Timer = Timer;
exports.Timer = Timer = __decorate([
    ECS_1.ecs.register('Timer')
], Timer);
