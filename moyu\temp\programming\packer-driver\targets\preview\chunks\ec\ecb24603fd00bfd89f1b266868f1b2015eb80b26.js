System.register(["__unresolved_0", "cc", "cc/env", "tsrpc-browser", "tsrpc-miniapp", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, WECHAT, HttpClient_Browser, WsClient_Browser, HttpClient_Miniapp, WsClient_Miniapp, oops, Security, ShareConfig, DataUpdateType, ServiceProtoGame, ServiceProtoGate, ClientConfig, GameServerConfig, GameStorageConfig, LocalConfig, DataManager, smc, CommonNet, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfHttpClient_Browser(extras) {
    _reporterNs.report("HttpClient_Browser", "tsrpc-browser", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWsClient_Browser(extras) {
    _reporterNs.report("WsClient_Browser", "tsrpc-browser", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHttpClient_Miniapp(extras) {
    _reporterNs.report("HttpClient_Miniapp", "tsrpc-miniapp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWsClient_Miniapp(extras) {
    _reporterNs.report("WsClient_Miniapp", "tsrpc-miniapp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSecurity(extras) {
    _reporterNs.report("Security", "../../tsrpc/models/Security", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShareConfig(extras) {
    _reporterNs.report("ShareConfig", "../../tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseResponse(extras) {
    _reporterNs.report("BaseResponse", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataUpdateType(extras) {
    _reporterNs.report("DataUpdateType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfOptimizedDataResponse(extras) {
    _reporterNs.report("OptimizedDataResponse", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfServiceProtoGame(extras) {
    _reporterNs.report("ServiceProtoGame", "../../tsrpc/protocols/ServiceProtoGame", _context.meta, extras);
  }

  function _reportPossibleCrUseOfServiceTypeGame(extras) {
    _reporterNs.report("ServiceTypeGame", "../../tsrpc/protocols/ServiceProtoGame", _context.meta, extras);
  }

  function _reportPossibleCrUseOfServiceProtoGate(extras) {
    _reporterNs.report("ServiceProtoGate", "../../tsrpc/protocols/ServiceProtoGate", _context.meta, extras);
  }

  function _reportPossibleCrUseOfServiceTypeGate(extras) {
    _reporterNs.report("ServiceTypeGate", "../../tsrpc/protocols/ServiceProtoGate", _context.meta, extras);
  }

  function _reportPossibleCrUseOfClientConfig(extras) {
    _reporterNs.report("ClientConfig", "./ClientConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameServerConfig(extras) {
    _reporterNs.report("GameServerConfig", "./config/GameServerConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStorageConfig(extras) {
    _reporterNs.report("GameStorageConfig", "./config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLocalConfig(extras) {
    _reporterNs.report("LocalConfig", "./config/LocalConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataManager(extras) {
    _reporterNs.report("DataManager", "./DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "./SingletonModuleComp", _context.meta, extras);
  }

  _export("CommonNet", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_ccEnv) {
      WECHAT = _ccEnv.WECHAT;
    }, function (_tsrpcBrowser) {
      HttpClient_Browser = _tsrpcBrowser.HttpClient;
      WsClient_Browser = _tsrpcBrowser.WsClient;
    }, function (_tsrpcMiniapp) {
      HttpClient_Miniapp = _tsrpcMiniapp.HttpClient;
      WsClient_Miniapp = _tsrpcMiniapp.WsClient;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      Security = _unresolved_3.Security;
    }, function (_unresolved_4) {
      ShareConfig = _unresolved_4.ShareConfig;
    }, function (_unresolved_5) {
      DataUpdateType = _unresolved_5.DataUpdateType;
    }, function (_unresolved_6) {
      ServiceProtoGame = _unresolved_6.serviceProto;
    }, function (_unresolved_7) {
      ServiceProtoGate = _unresolved_7.serviceProto;
    }, function (_unresolved_8) {
      ClientConfig = _unresolved_8.ClientConfig;
    }, function (_unresolved_9) {
      GameServerConfig = _unresolved_9.GameServerConfig;
    }, function (_unresolved_10) {
      GameStorageConfig = _unresolved_10.GameStorageConfig;
    }, function (_unresolved_11) {
      LocalConfig = _unresolved_11.LocalConfig;
    }, function (_unresolved_12) {
      DataManager = _unresolved_12.DataManager;
    }, function (_unresolved_13) {
      smc = _unresolved_13.smc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6a398mlG6VLDpWkgEPFbTWj", "CommonNet", undefined);
      /*
       * @Author: dgflash
       * @Date: 2022-06-28 19:10:14
       * @LastEditors: dgflash
       * @LastEditTime: 2022-09-20 10:38:39
       */


      /** TSRPC网络模块 */
      _export("CommonNet", CommonNet = class CommonNet {
        constructor() {
          /** 连接网关服务器 Http 客户端 */
          this.hcGate = null;

          /** 连接数据服务器 http 客户端 ，不用了*/
          this.hcGame = null;

          /** 连接数据服务器 WebSocket 客户端 */
          this.wcGame = null;

          /** 连接数据服务器 WebSocket 客户端 */
          this.wcGate = null;

          /** 创建连接网关服务器 Http 客户端 */
          this.createHcGate();
          /** 🎯 纯HTTP架构：同时初始化游戏HTTP客户端 */

          this.createHcGame();
        }

        createHcGate() {
          // 🚪 网关客户端：连接端口5000，处理登录注册
          var serverUrl = (_crd && ClientConfig === void 0 ? (_reportPossibleCrUseOfClientConfig({
            error: Error()
          }), ClientConfig) : ClientConfig).gateUrl;
          console.log('🚪 网关HTTP客户端初始化:', serverUrl);
          this.hcGate = new (WECHAT ? _crd && HttpClient_Miniapp === void 0 ? (_reportPossibleCrUseOfHttpClient_Miniapp({
            error: Error()
          }), HttpClient_Miniapp) : HttpClient_Miniapp : _crd && HttpClient_Browser === void 0 ? (_reportPossibleCrUseOfHttpClient_Browser({
            error: Error()
          }), HttpClient_Browser) : HttpClient_Browser)(_crd && ServiceProtoGate === void 0 ? (_reportPossibleCrUseOfServiceProtoGate({
            error: Error()
          }), ServiceProtoGate) : ServiceProtoGate, {
            server: serverUrl,
            json: (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).json,
            logger: console,
            // 🚀 优化：减少超时时间，加快失败响应
            timeout: 5000 // 从默认10秒减少到5秒

          });
          this.flowClientApi(this.hcGate);
          this.flowAuth(this.hcGate);
        }

        createWcGate(serverUrl) {
          if (this.wcGate && this.wcGate.isConnected) {
            this.wcGate.disconnect();
            return this.wcGate;
          } // 创建客户端与游戏服务器的 WebSocket 连接


          var wsc = new (WECHAT ? _crd && WsClient_Miniapp === void 0 ? (_reportPossibleCrUseOfWsClient_Miniapp({
            error: Error()
          }), WsClient_Miniapp) : WsClient_Miniapp : _crd && WsClient_Browser === void 0 ? (_reportPossibleCrUseOfWsClient_Browser({
            error: Error()
          }), WsClient_Browser) : WsClient_Browser)(_crd && ServiceProtoGate === void 0 ? (_reportPossibleCrUseOfServiceProtoGate({
            error: Error()
          }), ServiceProtoGate) : ServiceProtoGate, {
            server: serverUrl,
            heartbeat: {
              interval: (_crd && LocalConfig === void 0 ? (_reportPossibleCrUseOfLocalConfig({
                error: Error()
              }), LocalConfig) : LocalConfig).heartbeat_interval,
              timeout: (_crd && LocalConfig === void 0 ? (_reportPossibleCrUseOfLocalConfig({
                error: Error()
              }), LocalConfig) : LocalConfig).heartbeat_timeout
            },
            json: (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).json // logger: console,
            // logMsg: true,

          });
          (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).net.wcGate = wsc;
          this.flowClientMsg(wsc);
          this.flowAuth(wsc);
          this.flowUserGameData(wsc);
          return wsc;
        }
        /** 创建连游戏服务器 Http 客户端 */


        createHcGame() {
          // 🎮 游戏客户端：连接端口5001，处理游戏逻辑
          var serverUrl = (_crd && ClientConfig === void 0 ? (_reportPossibleCrUseOfClientConfig({
            error: Error()
          }), ClientConfig) : ClientConfig).gameUrl;
          console.log('🎮 游戏HTTP客户端初始化:', serverUrl);
          this.hcGame = new (WECHAT ? _crd && HttpClient_Miniapp === void 0 ? (_reportPossibleCrUseOfHttpClient_Miniapp({
            error: Error()
          }), HttpClient_Miniapp) : HttpClient_Miniapp : _crd && HttpClient_Browser === void 0 ? (_reportPossibleCrUseOfHttpClient_Browser({
            error: Error()
          }), HttpClient_Browser) : HttpClient_Browser)(_crd && ServiceProtoGame === void 0 ? (_reportPossibleCrUseOfServiceProtoGame({
            error: Error()
          }), ServiceProtoGame) : ServiceProtoGame, {
            server: serverUrl,
            json: (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).json,
            logger: console,
            // 🚀 优化：减少超时时间，加快失败响应
            timeout: 5000 // 从默认10秒减少到5秒

          }); // 🔧 同步更新GameServerConfig，保持一致性

          (_crd && GameServerConfig === void 0 ? (_reportPossibleCrUseOfGameServerConfig({
            error: Error()
          }), GameServerConfig) : GameServerConfig).httpUrl = serverUrl;
          this.flowClientApi(this.hcGame);
          this.flowAuth(this.hcGame);
          this.flowUserGameData(this.hcGame);
        }
        /**
         *  创建连接游戏服务器 Websocket 客户端
         *  🎯 纯HTTP架构：此方法已弃用，保留用于向后兼容
         */


        createWscGame() {
          // 🎯 纯HTTP架构：跳过WebSocket客户端创建
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logNet('🎯 纯HTTP架构：跳过WebSocket游戏客户端创建'); // 不设置wcGame，避免混淆
          // smc.net.wcGame = null;

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logNet('✅ 纯HTTP架构：WebSocket游戏客户端已跳过');
        }

        flowUserGameData(client) {
          // 将 callApi 的结果返回给调用方之后，如果有携带用户数据，直接覆盖
          client.flows.postApiReturnFlow.push(v => {
            if (v.return.isSucc && v.return.res) {
              var res = v.return.res; // 使用新的数据管理器处理优化响应

              if (res.updateType && Object.values(_crd && DataUpdateType === void 0 ? (_reportPossibleCrUseOfDataUpdateType({
                error: Error()
              }), DataUpdateType) : DataUpdateType).includes(res.updateType)) {
                var dataManager = (_crd && DataManager === void 0 ? (_reportPossibleCrUseOfDataManager({
                  error: Error()
                }), DataManager) : DataManager).getInstance();
                dataManager.processOptimizedResponse(res);
              } // 🗑️ 旧的全量数据响应已弃用，所有API已迁移到新格式
              // 如果遇到没有updateType的响应，记录警告
              else if (res.userGameData) {
                console.warn('[CommonNet] 检测到旧格式API响应，请升级API到新格式:', res);
              }
            }

            return v;
          });
        }
        /** HTTP 客户端协议数据加密、解密 */


        flowClientApi(hc) {
          if (!(_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).security) return;
          hc.flows.preSendDataFlow.push(v => {
            if (v.data instanceof Uint8Array) {
              v.data = (_crd && Security === void 0 ? (_reportPossibleCrUseOfSecurity({
                error: Error()
              }), Security) : Security).encrypt(v.data);
            }

            return v;
          }); // 在处理接收到的数据之前，通常要进行加密/解密

          hc.flows.preRecvDataFlow.push(v => {
            if (v.data instanceof Uint8Array) {
              v.data = (_crd && Security === void 0 ? (_reportPossibleCrUseOfSecurity({
                error: Error()
              }), Security) : Security).decrypt(v.data);
            }

            return v;
          });
        }
        /** WebSocket 客户端协议数据加密、解密 */


        flowClientMsg(wsc) {
          if (!(_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).security) return; // 发送 Message 之前

          wsc.flows.preSendMsgFlow.push(v => {
            if (v.data instanceof Uint8Array) {
              v.data = (_crd && Security === void 0 ? (_reportPossibleCrUseOfSecurity({
                error: Error()
              }), Security) : Security).encrypt(v.data);
            }

            return v;
          }); // 触发 Message 监听事件之前

          wsc.flows.preRecvMsgFlow.push(v => {
            if (v.data instanceof Uint8Array) {
              v.data = (_crd && Security === void 0 ? (_reportPossibleCrUseOfSecurity({
                error: Error()
              }), Security) : Security).decrypt(v.data);
            }

            return v;
          });
        }
        /** 帐号登录令牌验证是否逻辑（帐号中加入登录令牌，服务器通过令牌解析玩家数据，如果存在就是已登录） */


        flowAuth(client) {
          // HttpClient WsClient
          // 🔧 执行 callApi 之前的token验证和插入
          client.flows.preCallApiFlow.push(v => {
            // 🔍 检查是否为无需token的API（白名单）
            if (this.isTokenFreeAPI(v.req)) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83D\uDD13 API " + v.req.service + "/" + v.req.type + " \u65E0\u9700token\u9A8C\u8BC1");
              return v;
            } // 🔍 API调用前检查token有效性（主动检测挂机）


            if (!this.validateTokenBeforeRequest()) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔍 API调用前检测到token无效，尝试重新登录并重试...'); // 🔄 尝试重新登录并重试请求

              return this.handleTokenExpired().then(loginSuccess => {
                if (loginSuccess) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('✅ 重新登录成功，重试原始请求...'); // 重新添加token到请求

                  var newSsoToken = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).storage.get('SSO_TOKEN');

                  if (newSsoToken) {
                    v.req.__ssoToken = newSsoToken;
                  }

                  return v; // 继续执行原始请求
                } else {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logWarn('❌ 重新登录失败，阻止API调用');
                  return Promise.reject({
                    isSucc: false,
                    err: {
                      code: 'TOKEN_EXPIRED',
                      message: '重新登录失败，请手动重新登录'
                    }
                  });
                }
              });
            } // 请求前插入登录令牌


            var ssoToken = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).storage.get('SSO_TOKEN');

            if (ssoToken) {
              v.req.__ssoToken = ssoToken;
            }

            return v;
          }); // 将 callApi 的结果返回给调用方之后将登录令牌存到本地（收到协议时将登录令牌存到本地）

          client.flows.postApiReturnFlow.push(v => {
            if (v.return.isSucc) {
              var res = v.return.res; // 🔧 每次API成功响应时更新token信息

              var now = Date.now(); // 更新现有token的访问时间（如果存在）

              var existingTokenInfo = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                error: Error()
              }), GameStorageConfig) : GameStorageConfig).SSOTokenInfo);

              if (existingTokenInfo) {
                try {
                  var tokenInfo = JSON.parse(existingTokenInfo);
                  tokenInfo.lastAccessTime = now; // 🔧 关键：每次请求成功都更新访问时间

                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).storage.set((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                    error: Error()
                  }), GameStorageConfig) : GameStorageConfig).SSOTokenInfo, JSON.stringify(tokenInfo));
                } catch (error) {
                  console.warn('更新token访问时间失败:', error);
                }
              } // 请求成功后刷新登录令牌（如果服务端返回了新token）


              if (res.__ssoToken !== undefined) {
                // 🎮 三消游戏优化：与服务端新配置保持一致
                var tokenData = {
                  token: res.__ssoToken,
                  createdTime: now,
                  lastAccessTime: now,
                  // 服务端新配置：8小时基础 + 最多2次刷新 = 最长24小时
                  // 客户端提前30分钟判断过期，避免边界情况
                  expiredTime: now + 7.5 * 60 * 60 * 1000,
                  // 7.5小时客户端过期
                  maxLifetime: now + 23.5 * 60 * 60 * 1000,
                  // 23.5小时最大生命周期
                  maxIdleTime: 4 * 60 * 60 * 1000 // 4小时最大空闲时间

                };
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).storage.set('SSO_TOKEN', res.__ssoToken);
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).storage.set('SSO_TOKEN_INFO', JSON.stringify(tokenData));
              }
            } // 登录令牌过期时删除客户端登录令牌（可跳转到登录界面）
            else if (v.return.err.code === 'NEED_LOGIN') {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).storage.remove('SSO_TOKEN');
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).storage.remove('SSO_TOKEN_INFO');
            }

            return v;
          });
        }
        /**
         * 🔍 验证token有效性（公共方法，供其他模块调用）
         */


        validateToken() {
          return this.validateTokenBeforeRequest();
        }
        /**
         * � 检查是否为无需token的API（白名单）
         */


        isTokenFreeAPI(req) {
          // 无需token的API白名单
          var tokenFreeAPIs = ['Register', // 注册
          'Login', // 登录
          'FacebookLogin', // Facebook登录
          'GuestLogin', // 游客登录
          'GetGameConfig', // 获取游戏配置
          'Ping' // 心跳检测
          ]; // 检查API类型

          var apiType = req.type || req.api || '';
          var isTokenFree = tokenFreeAPIs.includes(apiType);

          if (isTokenFree) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83D\uDD13 API " + apiType + " \u5728\u767D\u540D\u5355\u4E2D\uFF0C\u8DF3\u8FC7token\u9A8C\u8BC1");
          }

          return isTokenFree;
        }
        /**
         * �🔍 API调用前验证token有效性（主动挂机检测）
         */


        validateTokenBeforeRequest() {
          try {
            // 检查是否有token
            var ssoToken = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
              error: Error()
            }), GameStorageConfig) : GameStorageConfig).SSOToken);

            if (!ssoToken) {
              return false;
            } // 检查token信息


            var tokenInfoStr = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
              error: Error()
            }), GameStorageConfig) : GameStorageConfig).SSOTokenInfo);

            if (!tokenInfoStr) {
              return false;
            }

            var tokenInfo = JSON.parse(tokenInfoStr);
            var now = Date.now(); // 检查最大生命周期

            if (tokenInfo.maxLifetime && tokenInfo.maxLifetime < now) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔍 token已超过最大生命周期');
              return false;
            } // 检查空闲时间


            if (tokenInfo.lastAccessTime && tokenInfo.maxIdleTime) {
              var idleTime = now - tokenInfo.lastAccessTime;

              if (idleTime > tokenInfo.maxIdleTime) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\uD83D\uDD0D token\u7A7A\u95F2\u65F6\u95F4\u8FC7\u957F: " + Math.floor(idleTime / 60000) + "\u5206\u949F");
                return false;
              }
            } // 检查基础过期时间


            if (tokenInfo.expiredTime && tokenInfo.expiredTime < now) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔍 token已过期');
              return false;
            }

            return true;
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ token验证过程出错:', error);
            return false;
          }
        }
        /**
         * 🔧 处理token过期（触发重新登录）
         */


        handleTokenExpired() {
          return new Promise(resolve => {
            try {
              // 清理本地token
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).storage.remove((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                error: Error()
              }), GameStorageConfig) : GameStorageConfig).SSOToken);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).storage.remove((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                error: Error()
              }), GameStorageConfig) : GameStorageConfig).SSOTokenInfo);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔄 Token过期，触发后台重新登录...'); // 🔄 触发Role模块的强制完整登录流程

              setTimeout( /*#__PURE__*/_asyncToGenerator(function* () {
                try {
                  var _loadDataInBackground, _ref2;

                  // 调用Role的私有方法需要通过反射或公共接口
                  // 这里触发后台数据加载，它会自动检测到token无效并重新登录
                  yield (_loadDataInBackground = (_ref2 = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                    error: Error()
                  }), smc) : smc).role).loadDataInBackground) == null ? void 0 : _loadDataInBackground.call(_ref2);
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('✅ 后台重新登录流程已启动'); // 等待一段时间让登录完成

                  setTimeout(() => {
                    var hasNewToken = !!(_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                      error: Error()
                    }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                      error: Error()
                    }), GameStorageConfig) : GameStorageConfig).SSOToken);
                    (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                      error: Error()
                    }), oops) : oops).log.logBusiness("\uD83D\uDD0D \u91CD\u65B0\u767B\u5F55\u7ED3\u679C: " + (hasNewToken ? '成功' : '失败'));
                    resolve(hasNewToken);
                  }, 2000); // 等待2秒让登录完成
                } catch (error) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logWarn('⚠️ 启动后台重新登录失败:', error);
                  resolve(false);
                }
              }), 100);
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ 处理token过期失败:', error);
              resolve(false);
            }
          });
        }
        /**
         * 🔧 公共方法：处理token过期（供外部调用）
         */


        handleTokenExpiredPublic() {
          this.handleTokenExpired();
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ecb24603fd00bfd89f1b266868f1b2015eb80b26.js.map