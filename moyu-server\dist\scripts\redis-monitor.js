"use strict";
/*
 * Redis监控脚本 - 查看Redis状态和会话信息
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const chalk_1 = __importDefault(require("chalk"));
const RedisManager_1 = require("../module/common/RedisManager");
const RedisSessionManager_1 = require("../module/common/RedisSessionManager");
async function monitorRedis() {
    var _a, _b;
    try {
        console.log(chalk_1.default.green('🔍 Redis状态监控'));
        console.log(chalk_1.default.gray('='.repeat(50)));
        // 初始化Redis连接
        await RedisManager_1.RedisManager.init();
        // 获取Redis基本信息
        console.log(chalk_1.default.cyan('\n📊 Redis基本信息:'));
        const info = await RedisManager_1.RedisManager.getInfo();
        // 解析关键信息
        const infoLines = info.split('\r\n');
        const keyInfo = {};
        infoLines.forEach(line => {
            if (line.includes(':')) {
                const [key, value] = line.split(':');
                keyInfo[key] = value;
            }
        });
        console.log(`   版本: ${keyInfo.redis_version || 'N/A'}`);
        console.log(`   运行模式: ${keyInfo.redis_mode || 'N/A'}`);
        console.log(`   运行时间: ${keyInfo.uptime_in_seconds ? Math.floor(keyInfo.uptime_in_seconds / 3600) + '小时' : 'N/A'}`);
        console.log(`   连接数: ${keyInfo.connected_clients || 'N/A'}`);
        console.log(`   已用内存: ${keyInfo.used_memory_human || 'N/A'}`);
        console.log(`   内存峰值: ${keyInfo.used_memory_peak_human || 'N/A'}`);
        console.log(`   碎片率: ${keyInfo.mem_fragmentation_ratio || 'N/A'}`);
        // 获取键空间信息
        console.log(chalk_1.default.cyan('\n🗂️  键空间信息:'));
        const dbKeys = await RedisManager_1.RedisManager.getClient().dbSize();
        console.log(`   总键数: ${dbKeys}`);
        // 会话相关的键
        const sessionKeys = await RedisManager_1.RedisManager.getClient().keys('session:*');
        const userSessionKeys = await RedisManager_1.RedisManager.getClient().keys('user_sessions:*');
        console.log(`   会话键数: ${sessionKeys.length}`);
        console.log(`   用户会话键数: ${userSessionKeys.length}`);
        // 获取内存使用详情
        console.log(chalk_1.default.cyan('\n💾 内存使用详情:'));
        const memoryUsage = await RedisManager_1.RedisManager.getMemoryUsage();
        console.log(`   已用内存: ${memoryUsage.usedMemory}`);
        console.log(`   RSS内存: ${memoryUsage.usedMemoryRss}`);
        console.log(`   内存峰值: ${memoryUsage.usedMemoryPeak}`);
        console.log(`   系统总内存: ${memoryUsage.totalSystemMemory}`);
        console.log(`   碎片率: ${((_a = memoryUsage.memFragmentationRatio) === null || _a === void 0 ? void 0 : _a.toFixed(2)) || 'N/A'}`);
        // 分析会话数据
        if (sessionKeys.length > 0) {
            console.log(chalk_1.default.cyan('\n🔐 会话分析:'));
            let activeCount = 0;
            let expiredCount = 0;
            const platforms = {};
            const now = Date.now();
            for (const sessionKey of sessionKeys.slice(0, 10)) { // 只分析前10个会话
                const sessionData = await RedisManager_1.RedisManager.get(sessionKey);
                if (sessionData) {
                    try {
                        const session = JSON.parse(sessionData);
                        if (session.expiredTime > now) {
                            activeCount++;
                        }
                        else {
                            expiredCount++;
                        }
                        const platform = ((_b = session.metadata) === null || _b === void 0 ? void 0 : _b.platform) || 'unknown';
                        platforms[platform] = (platforms[platform] || 0) + 1;
                    }
                    catch (error) {
                        console.log(chalk_1.default.yellow(`   ⚠️  无法解析会话数据: ${sessionKey}`));
                    }
                }
            }
            console.log(`   活跃会话: ${activeCount}`);
            console.log(`   过期会话: ${expiredCount}`);
            console.log(`   平台分布:`, platforms);
        }
        // 性能建议
        console.log(chalk_1.default.cyan('\n💡 性能建议:'));
        if (memoryUsage.memFragmentationRatio > 1.5) {
            console.log(chalk_1.default.yellow('   ⚠️  内存碎片率较高，建议考虑重启Redis'));
        }
        if (sessionKeys.length > 10000) {
            console.log(chalk_1.default.yellow('   ⚠️  会话数量较多，建议检查清理策略'));
        }
        if (dbKeys > 50000) {
            console.log(chalk_1.default.yellow('   ⚠️  键数量较多，建议考虑数据清理'));
        }
        const usedMemoryMB = parseInt(keyInfo.used_memory) / 1024 / 1024;
        if (usedMemoryMB > 100) {
            console.log(chalk_1.default.yellow(`   ⚠️  内存使用量较高 (${usedMemoryMB.toFixed(2)}MB)`));
        }
        console.log(chalk_1.default.green('\n✅ 监控完成'));
        // 交互式菜单
        await showInteractiveMenu();
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ 监控失败:'), error);
    }
    finally {
        await RedisManager_1.RedisManager.close();
        process.exit(0);
    }
}
async function showInteractiveMenu() {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    console.log(chalk_1.default.cyan('\n🛠️  交互操作:'));
    console.log('1. 清理过期会话');
    console.log('2. 查看详细会话列表');
    console.log('3. 清空所有会话');
    console.log('4. 退出');
    const answer = await new Promise((resolve) => {
        rl.question(chalk_1.default.yellow('请选择操作 (1-4): '), (answer) => {
            resolve(answer.trim());
        });
    });
    rl.close();
    switch (answer) {
        case '1':
            await cleanupSessions();
            break;
        case '2':
            await showDetailedSessions();
            break;
        case '3':
            await clearAllSessions();
            break;
        case '4':
        default:
            console.log(chalk_1.default.green('👋 再见！'));
            break;
    }
}
async function cleanupSessions() {
    try {
        console.log(chalk_1.default.cyan('\n🧹 清理过期会话...'));
        await RedisSessionManager_1.RedisSessionManager.init();
        const cleaned = await RedisSessionManager_1.RedisSessionManager.cleanupExpiredSessions();
        console.log(chalk_1.default.green(`✅ 清理完成，共清理 ${cleaned} 个过期会话`));
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ 清理失败:'), error);
    }
}
async function showDetailedSessions() {
    var _a, _b, _c;
    try {
        console.log(chalk_1.default.cyan('\n📋 详细会话列表:'));
        const sessionKeys = await RedisManager_1.RedisManager.getClient().keys('session:*');
        if (sessionKeys.length === 0) {
            console.log(chalk_1.default.yellow('   无活跃会话'));
            return;
        }
        console.log(chalk_1.default.gray('-'.repeat(80)));
        console.log(chalk_1.default.white('Token'.padEnd(10) + 'User'.padEnd(15) + 'Platform'.padEnd(12) + 'IP'.padEnd(15) + 'Status'));
        console.log(chalk_1.default.gray('-'.repeat(80)));
        const now = Date.now();
        for (const sessionKey of sessionKeys.slice(0, 20)) { // 只显示前20个
            const sessionData = await RedisManager_1.RedisManager.get(sessionKey);
            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);
                    const token = sessionKey.replace('session:', '').substring(0, 8) + '...';
                    const user = ((_a = session.user) === null || _a === void 0 ? void 0 : _a.userName) || 'unknown';
                    const platform = ((_b = session.metadata) === null || _b === void 0 ? void 0 : _b.platform) || 'unknown';
                    const ip = ((_c = session.metadata) === null || _c === void 0 ? void 0 : _c.ip) || 'unknown';
                    const status = session.expiredTime > now ?
                        chalk_1.default.green('Active') : chalk_1.default.red('Expired');
                    console.log(token.padEnd(10) +
                        user.padEnd(15) +
                        platform.padEnd(12) +
                        ip.padEnd(15) +
                        status);
                }
                catch (error) {
                    console.log(chalk_1.default.red(`   ❌ 无法解析: ${sessionKey}`));
                }
            }
        }
        if (sessionKeys.length > 20) {
            console.log(chalk_1.default.gray(`... 还有 ${sessionKeys.length - 20} 个会话未显示`));
        }
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ 获取会话列表失败:'), error);
    }
}
async function clearAllSessions() {
    try {
        console.log(chalk_1.default.yellow('\n⚠️  这将清空所有会话，用户需要重新登录'));
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        const confirm = await new Promise((resolve) => {
            rl.question('确认清空所有会话？(y/N): ', (answer) => {
                resolve(answer.trim().toLowerCase());
            });
        });
        rl.close();
        if (confirm === 'y' || confirm === 'yes') {
            const sessionDeleted = await RedisManager_1.RedisManager.deletePattern('session:*');
            const userSessionDeleted = await RedisManager_1.RedisManager.deletePattern('user_sessions:*');
            console.log(chalk_1.default.green(`✅ 清空完成:`));
            console.log(chalk_1.default.green(`   会话键: ${sessionDeleted} 个`));
            console.log(chalk_1.default.green(`   用户会话键: ${userSessionDeleted} 个`));
        }
        else {
            console.log(chalk_1.default.blue('📝 操作已取消'));
        }
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ 清空会话失败:'), error);
    }
}
// 运行监控
if (require.main === module) {
    monitorRedis();
}
