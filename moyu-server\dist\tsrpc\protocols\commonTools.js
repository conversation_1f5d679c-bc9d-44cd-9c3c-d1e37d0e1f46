"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonTools = void 0;
class CommonTools {
    // 用户名：6-20位，允许数字、字母和下划线（开发环境下允许1位字符）
    static checkUsername(username) {
        if (this.isEmpty(username))
            return false;
        if (this.isDev) {
            return true; // 开发环境下的验证
        }
        return /^[a-zA-Z0-9_]{6,20}$/.test(username); // 生产环境下的验证
    }
    // 密码：6-20位，必须包含数字、字母和特殊字符（开发环境下允许1位数字）
    static checkPassword(password) {
        if (this.isEmpty(password))
            return false;
        if (this.isDev) {
            return true; // 开发环境下的验证
        }
        return /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[ !"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~])[a-zA-Z\d !"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]{6,20}$/.test(password); // 生产环境下的验证
    }
    // 邮箱：更严格的邮箱验证
    static checkEmail(email) {
        if (this.isEmpty(email))
            return false;
        return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
    }
    // 判空：检查字符串是否为空或仅包含空格
    static isEmpty(value) {
        return !value || value.trim().length === 0;
    }
    // 检查guuid是否合法
    static checkGuuid(guuid) {
        return guuid.length === 24;
    }
}
exports.CommonTools = CommonTools;
CommonTools.isDev = true;
